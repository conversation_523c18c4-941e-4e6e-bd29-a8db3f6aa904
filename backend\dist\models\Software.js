"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Software = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const License_1 = require("./License");
let Software = class Software extends sequelize_typescript_1.Model {
};
exports.Software = Software;
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
        validate: {
            len: [1, 100],
        },
    }),
    __metadata("design:type", String)
], Software.prototype, "name", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
        unique: true,
        validate: {
            len: [1, 50],
        },
    }),
    __metadata("design:type", String)
], Software.prototype, "productKey", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.TEXT,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Software.prototype, "description", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.BOOLEAN,
        defaultValue: true,
    }),
    __metadata("design:type", Boolean)
], Software.prototype, "isActive", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => License_1.License),
    __metadata("design:type", Array)
], Software.prototype, "licenses", void 0);
exports.Software = Software = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'software',
        timestamps: true,
    })
], Software);
//# sourceMappingURL=Software.js.map