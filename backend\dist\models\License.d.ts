import { Model } from 'sequelize-typescript';
import { User } from './User';
import { Software } from './Software';
export declare class License extends Model<License> {
    licenseKey: string;
    activationType: 'online' | 'offline';
    activatedAt?: Date;
    expiresAt?: Date;
    machineId?: string;
    isRevoked: boolean;
    userId: number;
    softwareId: number;
    user: User;
    software: Software;
}
//# sourceMappingURL=License.d.ts.map