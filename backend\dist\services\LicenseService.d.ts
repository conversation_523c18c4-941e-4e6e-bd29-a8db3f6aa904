export declare class LicenseService {
    /**
     * 生成新的许可证密钥
     */
    static generateLicenseKey(): string;
    /**
     * 验证许可证是否有效
     */
    static validateLicense(licenseData: any): boolean;
    /**
     * 创建许可证激活响应
     */
    static createActivationResponse(success: boolean, data?: any, message?: string): any;
    /**
     * 创建许可证验证响应
     */
    static createValidationResponse(success: boolean, isValid: boolean, data?: any, message?: string): any;
    /**
     * 验证机器ID
     */
    static validateMachineId(licenseData: any, machineId: string): boolean;
    /**
     * 检查许可证是否可以激活
     */
    static canActivateLicense(licenseData: any): boolean;
    /**
     * 获取许可证状态
     */
    static getLicenseStatus(licenseData: any): string;
}
//# sourceMappingURL=LicenseService.d.ts.map