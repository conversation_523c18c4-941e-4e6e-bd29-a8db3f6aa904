"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const DataService_1 = require("./services/DataService");
// 导入路由
const auth_1 = __importDefault(require("./routes/auth"));
const admin_1 = __importDefault(require("./routes/admin"));
const user_simple_1 = __importDefault(require("./routes/user-simple"));
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
// 中间件
app.use((0, cors_1.default)());
app.use(express_1.default.json());
// 提供静态文件
app.use(express_1.default.static(path_1.default.join(__dirname, '../public')));
// 应用路由
app.use('/api/auth', auth_1.default);
app.use('/api/admin', admin_1.default);
app.use('/api/user', user_simple_1.default);
// 默认路由
app.get('/', (_req, res) => {
    res.sendFile(path_1.default.join(__dirname, '../public/index.html'));
});
// 启动服务器
async function startServer() {
    try {
        // 初始化数据服务
        await DataService_1.DataService.initialize();
        // 启动服务器
        app.listen(PORT, () => {
            console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
            console.log(`📊 管理后台: http://localhost:${PORT}/admin.html`);
            console.log(`👤 用户界面: http://localhost:${PORT}/user.html`);
        });
    }
    catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}
// 优雅关闭
process.on('SIGTERM', async () => {
    console.log('收到 SIGTERM 信号，正在关闭服务器...');
    await DataService_1.DataService.close();
    process.exit(0);
});
process.on('SIGINT', async () => {
    console.log('收到 SIGINT 信号，正在关闭服务器...');
    await DataService_1.DataService.close();
    process.exit(0);
});
// 启动服务器
startServer();
//# sourceMappingURL=app.js.map