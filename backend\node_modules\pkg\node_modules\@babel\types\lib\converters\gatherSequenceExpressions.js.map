{"version": 3, "names": ["gatherSequenceExpressions", "nodes", "scope", "declars", "exprs", "ensureLastUndefined", "node", "isEmptyStatement", "isExpression", "push", "isExpressionStatement", "expression", "isVariableDeclaration", "kind", "declar", "declarations", "bindings", "getBindingIdentifiers", "key", "Object", "keys", "id", "cloneNode", "init", "assignmentExpression", "isIfStatement", "consequent", "buildUndefinedNode", "alternate", "conditionalExpression", "test", "isBlockStatement", "body", "indexOf", "length", "sequenceExpression"], "sources": ["../../src/converters/gatherSequenceExpressions.ts"], "sourcesContent": ["import getBindingIdentifiers from \"../retrievers/getBindingIdentifiers\";\nimport {\n  isExpression,\n  isExpressionStatement,\n  isVariableDeclaration,\n  isIfStatement,\n  isBlockStatement,\n  isEmptyStatement,\n} from \"../validators/generated\";\nimport {\n  sequenceExpression,\n  assignmentExpression,\n  conditionalExpression,\n} from \"../builders/generated\";\nimport cloneNode from \"../clone/cloneNode\";\nimport type * as t from \"..\";\nimport type { Scope } from \"@babel/traverse\";\n\nexport type DeclarationInfo = {\n  kind: t.VariableDeclaration[\"kind\"];\n  id: t.Identifier;\n};\n\nexport default function gatherSequenceExpressions(\n  nodes: ReadonlyArray<t.Node>,\n  scope: Scope,\n  declars: Array<DeclarationInfo>,\n) {\n  const exprs: t.Expression[] = [];\n  let ensureLastUndefined = true;\n\n  for (const node of nodes) {\n    // if we encounter emptyStatement before a non-emptyStatement\n    // we want to disregard that\n    if (!isEmptyStatement(node)) {\n      ensureLastUndefined = false;\n    }\n\n    if (isExpression(node)) {\n      exprs.push(node);\n    } else if (isExpressionStatement(node)) {\n      exprs.push(node.expression);\n    } else if (isVariableDeclaration(node)) {\n      if (node.kind !== \"var\") return; // bailed\n\n      for (const declar of node.declarations) {\n        const bindings = getBindingIdentifiers(declar);\n        for (const key of Object.keys(bindings)) {\n          declars.push({\n            kind: node.kind,\n            id: cloneNode(bindings[key]),\n          });\n        }\n\n        if (declar.init) {\n          exprs.push(assignmentExpression(\"=\", declar.id, declar.init));\n        }\n      }\n\n      ensureLastUndefined = true;\n    } else if (isIfStatement(node)) {\n      const consequent = node.consequent\n        ? gatherSequenceExpressions([node.consequent], scope, declars)\n        : scope.buildUndefinedNode();\n      const alternate = node.alternate\n        ? gatherSequenceExpressions([node.alternate], scope, declars)\n        : scope.buildUndefinedNode();\n      if (!consequent || !alternate) return; // bailed\n\n      exprs.push(conditionalExpression(node.test, consequent, alternate));\n    } else if (isBlockStatement(node)) {\n      const body = gatherSequenceExpressions(node.body, scope, declars);\n      if (!body) return; // bailed\n\n      exprs.push(body);\n    } else if (isEmptyStatement(node)) {\n      // empty statement so ensure the last item is undefined if we're last\n      // checks if emptyStatement is first\n      if (nodes.indexOf(node) === 0) {\n        ensureLastUndefined = true;\n      }\n    } else {\n      // bailed, we can't turn this statement into an expression\n      return;\n    }\n  }\n\n  if (ensureLastUndefined) {\n    exprs.push(scope.buildUndefinedNode());\n  }\n\n  if (exprs.length === 1) {\n    return exprs[0];\n  } else {\n    return sequenceExpression(exprs);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAQA;;AAKA;;AASe,SAASA,yBAAT,CACbC,KADa,EAEbC,KAFa,EAGbC,OAHa,EAIb;EACA,MAAMC,KAAqB,GAAG,EAA9B;EACA,IAAIC,mBAAmB,GAAG,IAA1B;;EAEA,KAAK,MAAMC,IAAX,IAAmBL,KAAnB,EAA0B;IAGxB,IAAI,CAAC,IAAAM,2BAAA,EAAiBD,IAAjB,CAAL,EAA6B;MAC3BD,mBAAmB,GAAG,KAAtB;IACD;;IAED,IAAI,IAAAG,uBAAA,EAAaF,IAAb,CAAJ,EAAwB;MACtBF,KAAK,CAACK,IAAN,CAAWH,IAAX;IACD,CAFD,MAEO,IAAI,IAAAI,gCAAA,EAAsBJ,IAAtB,CAAJ,EAAiC;MACtCF,KAAK,CAACK,IAAN,CAAWH,IAAI,CAACK,UAAhB;IACD,CAFM,MAEA,IAAI,IAAAC,gCAAA,EAAsBN,IAAtB,CAAJ,EAAiC;MACtC,IAAIA,IAAI,CAACO,IAAL,KAAc,KAAlB,EAAyB;;MAEzB,KAAK,MAAMC,MAAX,IAAqBR,IAAI,CAACS,YAA1B,EAAwC;QACtC,MAAMC,QAAQ,GAAG,IAAAC,8BAAA,EAAsBH,MAAtB,CAAjB;;QACA,KAAK,MAAMI,GAAX,IAAkBC,MAAM,CAACC,IAAP,CAAYJ,QAAZ,CAAlB,EAAyC;UACvCb,OAAO,CAACM,IAAR,CAAa;YACXI,IAAI,EAAEP,IAAI,CAACO,IADA;YAEXQ,EAAE,EAAE,IAAAC,kBAAA,EAAUN,QAAQ,CAACE,GAAD,CAAlB;UAFO,CAAb;QAID;;QAED,IAAIJ,MAAM,CAACS,IAAX,EAAiB;UACfnB,KAAK,CAACK,IAAN,CAAW,IAAAe,gCAAA,EAAqB,GAArB,EAA0BV,MAAM,CAACO,EAAjC,EAAqCP,MAAM,CAACS,IAA5C,CAAX;QACD;MACF;;MAEDlB,mBAAmB,GAAG,IAAtB;IACD,CAlBM,MAkBA,IAAI,IAAAoB,wBAAA,EAAcnB,IAAd,CAAJ,EAAyB;MAC9B,MAAMoB,UAAU,GAAGpB,IAAI,CAACoB,UAAL,GACf1B,yBAAyB,CAAC,CAACM,IAAI,CAACoB,UAAN,CAAD,EAAoBxB,KAApB,EAA2BC,OAA3B,CADV,GAEfD,KAAK,CAACyB,kBAAN,EAFJ;MAGA,MAAMC,SAAS,GAAGtB,IAAI,CAACsB,SAAL,GACd5B,yBAAyB,CAAC,CAACM,IAAI,CAACsB,SAAN,CAAD,EAAmB1B,KAAnB,EAA0BC,OAA1B,CADX,GAEdD,KAAK,CAACyB,kBAAN,EAFJ;MAGA,IAAI,CAACD,UAAD,IAAe,CAACE,SAApB,EAA+B;MAE/BxB,KAAK,CAACK,IAAN,CAAW,IAAAoB,iCAAA,EAAsBvB,IAAI,CAACwB,IAA3B,EAAiCJ,UAAjC,EAA6CE,SAA7C,CAAX;IACD,CAVM,MAUA,IAAI,IAAAG,2BAAA,EAAiBzB,IAAjB,CAAJ,EAA4B;MACjC,MAAM0B,IAAI,GAAGhC,yBAAyB,CAACM,IAAI,CAAC0B,IAAN,EAAY9B,KAAZ,EAAmBC,OAAnB,CAAtC;MACA,IAAI,CAAC6B,IAAL,EAAW;MAEX5B,KAAK,CAACK,IAAN,CAAWuB,IAAX;IACD,CALM,MAKA,IAAI,IAAAzB,2BAAA,EAAiBD,IAAjB,CAAJ,EAA4B;MAGjC,IAAIL,KAAK,CAACgC,OAAN,CAAc3B,IAAd,MAAwB,CAA5B,EAA+B;QAC7BD,mBAAmB,GAAG,IAAtB;MACD;IACF,CANM,MAMA;MAEL;IACD;EACF;;EAED,IAAIA,mBAAJ,EAAyB;IACvBD,KAAK,CAACK,IAAN,CAAWP,KAAK,CAACyB,kBAAN,EAAX;EACD;;EAED,IAAIvB,KAAK,CAAC8B,MAAN,KAAiB,CAArB,EAAwB;IACtB,OAAO9B,KAAK,CAAC,CAAD,CAAZ;EACD,CAFD,MAEO;IACL,OAAO,IAAA+B,8BAAA,EAAmB/B,KAAnB,CAAP;EACD;AACF"}