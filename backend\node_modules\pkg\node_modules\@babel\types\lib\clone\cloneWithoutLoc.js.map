{"version": 3, "names": ["cloneWithoutLoc", "node", "cloneNode"], "sources": ["../../src/clone/cloneWithoutLoc.ts"], "sourcesContent": ["import cloneNode from \"./cloneNode\";\nimport type * as t from \"..\";\n\n/**\n * Create a shallow clone of a `node` excluding `_private` and location properties.\n */\nexport default function cloneWithoutLoc<T extends t.Node>(node: T): T {\n  return cloneNode(node, /* deep */ false, /* withoutLoc */ true);\n}\n"], "mappings": ";;;;;;;AAAA;;AAMe,SAASA,eAAT,CAA2CC,IAA3C,EAAuD;EACpE,OAAO,IAAAC,kBAAA,EAAUD,IAAV,EAA2B,KAA3B,EAAmD,IAAnD,CAAP;AACD"}