"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseService = void 0;
const crypto_1 = require("../utils/crypto");
class LicenseService {
    /**
     * 生成新的许可证密钥
     */
    static generateLicenseKey() {
        return crypto_1.CryptoUtils.generateLicenseKey();
    }
    /**
     * 验证许可证是否有效
     */
    static validateLicense(licenseData) {
        // 检查许可证是否已被撤销
        if (licenseData.isRevoked) {
            return false;
        }
        // 检查许可证是否已过期
        if (licenseData.expiresAt && new Date(licenseData.expiresAt) < new Date()) {
            return false;
        }
        return true;
    }
    /**
     * 创建许可证激活响应
     */
    static createActivationResponse(success, data, message) {
        return {
            success,
            ...(data && {
                licenseKey: data.licenseKey,
                activationType: data.activationType,
                expiresAt: data.expiresAt
            }),
            ...(message && { message }),
        };
    }
    /**
     * 创建许可证验证响应
     */
    static createValidationResponse(success, isValid, data, message) {
        return {
            success,
            isValid,
            ...(data && { expiresAt: data.expiresAt }),
            ...(message && { message }),
        };
    }
    /**
     * 验证机器ID
     */
    static validateMachineId(licenseData, machineId) {
        // 如果许可证没有绑定机器ID，则允许任何机器
        if (!licenseData.machineId) {
            return true;
        }
        // 如果许可证绑定了机器ID，则必须匹配
        return licenseData.machineId === machineId;
    }
    /**
     * 检查许可证是否可以激活
     */
    static canActivateLicense(licenseData) {
        // 检查许可证是否已被撤销
        if (licenseData.isRevoked) {
            return false;
        }
        // 检查许可证是否已过期
        if (licenseData.expiresAt && new Date(licenseData.expiresAt) < new Date()) {
            return false;
        }
        return true;
    }
    /**
     * 获取许可证状态
     */
    static getLicenseStatus(licenseData) {
        if (licenseData.isRevoked) {
            return 'revoked';
        }
        if (licenseData.expiresAt && new Date(licenseData.expiresAt) < new Date()) {
            return 'expired';
        }
        if (licenseData.activatedAt) {
            return 'active';
        }
        return 'pending';
    }
}
exports.LicenseService = LicenseService;
//# sourceMappingURL=LicenseService.js.map