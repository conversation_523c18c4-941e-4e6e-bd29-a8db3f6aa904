{"version": 3, "file": "docs.js", "sourceRoot": "", "sources": ["../../src/routes/docs.ts"], "names": [], "mappings": ";;AAAA,qCAAiC;AAEjC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,UAAU;AACV,MAAM,OAAO,GAAG;IACd,KAAK,EAAE,cAAc;IACrB,OAAO,EAAE,OAAO;IAChB,WAAW,EAAE,4BAA4B;IACzC,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;QACT;YACE,KAAK,EAAE,MAAM;YACb,IAAI,EAAE;gBACJ;oBACE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,gBAAgB;oBACtB,WAAW,EAAE,MAAM;oBACnB,WAAW,EAAE;wBACX,QAAQ,EAAE,iBAAiB;wBAC3B,KAAK,EAAE,eAAe;wBACtB,QAAQ,EAAE,aAAa;wBACvB,KAAK,EAAE,2BAA2B;qBACnC;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,MAAM;wBACX,GAAG,EAAE,QAAQ;qBACd;iBACF;gBACD;oBACE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,MAAM;oBACnB,WAAW,EAAE;wBACX,QAAQ,EAAE,cAAc;wBACxB,QAAQ,EAAE,aAAa;qBACxB;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,cAAc;wBACnB,GAAG,EAAE,UAAU;qBAChB;oBACD,OAAO,EAAE;wBACP,QAAQ,EAAE,OAAO;wBACjB,QAAQ,EAAE,UAAU;qBACrB;iBACF;gBACD;oBACE,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,eAAe;oBACrB,WAAW,EAAE,UAAU;oBACvB,OAAO,EAAE;wBACP,aAAa,EAAE,oBAAoB;qBACpC;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,UAAU;wBACf,GAAG,EAAE,UAAU;qBAChB;iBACF;gBACD;oBACE,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,eAAe;oBACrB,WAAW,EAAE,QAAQ;oBACrB,OAAO,EAAE;wBACP,aAAa,EAAE,oBAAoB;qBACpC;oBACD,WAAW,EAAE;wBACX,QAAQ,EAAE,aAAa;wBACvB,KAAK,EAAE,aAAa;wBACpB,QAAQ,EAAE,aAAa;qBACxB;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,MAAM;wBACX,GAAG,EAAE,UAAU;qBAChB;iBACF;aACF;SACF;QACD;YACE,KAAK,EAAE,OAAO;YACd,IAAI,EAAE;gBACJ;oBACE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,OAAO;oBACpB,WAAW,EAAE;wBACX,UAAU,EAAE,eAAe;wBAC3B,SAAS,EAAE,eAAe;wBAC1B,cAAc,EAAE,yBAAyB;qBAC1C;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,MAAM;wBACX,GAAG,EAAE,kBAAkB;qBACxB;oBACD,OAAO,EAAE;wBACP,UAAU,EAAE,qBAAqB;wBACjC,SAAS,EAAE,mBAAmB;wBAC9B,cAAc,EAAE,QAAQ;qBACzB;iBACF;gBACD;oBACE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,iCAAiC;oBACvC,WAAW,EAAE,OAAO;oBACpB,UAAU,EAAE;wBACV,UAAU,EAAE,gBAAgB;qBAC7B;oBACD,WAAW,EAAE;wBACX,SAAS,EAAE,kBAAkB;qBAC9B;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,MAAM;wBACX,GAAG,EAAE,QAAQ;qBACd;iBACF;gBACD;oBACE,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,wBAAwB;oBAC9B,WAAW,EAAE,SAAS;oBACtB,UAAU,EAAE;wBACV,UAAU,EAAE,gBAAgB;qBAC7B;oBACD,OAAO,EAAE;wBACP,aAAa,EAAE,oBAAoB;qBACpC;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,WAAW;wBAChB,GAAG,EAAE,KAAK;wBACV,GAAG,EAAE,QAAQ;qBACd;iBACF;gBACD;oBACE,MAAM,EAAE,QAAQ;oBAChB,IAAI,EAAE,+BAA+B;oBACrC,WAAW,EAAE,OAAO;oBACpB,UAAU,EAAE;wBACV,UAAU,EAAE,gBAAgB;qBAC7B;oBACD,OAAO,EAAE;wBACP,aAAa,EAAE,oBAAoB;qBACpC;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE,MAAM;wBACX,GAAG,EAAE,UAAU;wBACf,GAAG,EAAE,QAAQ;qBACd;iBACF;aACF;SACF;KACF;IACD,eAAe,EAAE;QACf;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,MAAM;YACnB,SAAS,EAAE;gBACT,GAAG,EAAE,QAAQ;aACd;SACF;QACD;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,GAAG;YACT,WAAW,EAAE,eAAe;SAC7B;QACD;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,eAAe;SAC7B;KACF;CACF,CAAC;AAEF,UAAU;AACV,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;IAC5B,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC"}