"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CryptoUtils = void 0;
const crypto_1 = __importDefault(require("crypto"));
class CryptoUtils {
    /**
     * 生成随机许可证密钥
     */
    static generateLicenseKey() {
        const timestamp = Date.now().toString(36);
        const random = crypto_1.default.randomBytes(16).toString('hex');
        return `${timestamp}-${random}`.toUpperCase();
    }
    /**
     * 生成产品密钥
     */
    static generateProductKey() {
        const segments = [];
        for (let i = 0; i < 4; i++) {
            segments.push(crypto_1.default.randomBytes(4).toString('hex').toUpperCase());
        }
        return segments.join('-');
    }
    /**
     * 加密数据
     */
    static encrypt(text, key) {
        const keyBuffer = crypto_1.default.scryptSync(key, 'salt', 32);
        const iv = crypto_1.default.randomBytes(CryptoUtils.IV_LENGTH);
        const cipher = crypto_1.default.createCipheriv(CryptoUtils.ALGORITHM, keyBuffer, iv);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return iv.toString('hex') + ':' + encrypted;
    }
    /**
     * 解密数据
     */
    static decrypt(encryptedData, key) {
        const parts = encryptedData.split(':');
        if (parts.length !== 2) {
            throw new Error('Invalid encrypted data format');
        }
        const iv = Buffer.from(parts[0], 'hex');
        const encrypted = parts[1];
        const keyBuffer = crypto_1.default.scryptSync(key, 'salt', 32);
        const decipher = crypto_1.default.createDecipheriv(CryptoUtils.ALGORITHM, keyBuffer, iv);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    /**
     * 生成哈希值
     */
    static hash(data) {
        return crypto_1.default.createHash('sha256').update(data).digest('hex');
    }
    /**
     * 验证哈希值
     */
    static verifyHash(data, hash) {
        return CryptoUtils.hash(data) === hash;
    }
    /**
     * 生成JWT密钥
     */
    static generateJWTSecret() {
        return crypto_1.default.randomBytes(64).toString('hex');
    }
}
exports.CryptoUtils = CryptoUtils;
CryptoUtils.ALGORITHM = 'aes-256-cbc';
CryptoUtils.IV_LENGTH = 16;
//# sourceMappingURL=crypto.js.map