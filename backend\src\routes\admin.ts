import { Router, Request, Response } from 'express';
import { authenticateToken } from '../middleware/auth';
import { UserService } from '../services/UserService';
import { DataService } from '../services/DataService';

const router = Router();

// 仪表板统计数据
router.get('/stats', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const users = await DataService.getUsers();
    const software = await DataService.getSoftware();
    const licenses = await DataService.getLicenses();
    
    // 计算活跃许可证（未撤销且未过期）
    const activeLicenses = licenses.filter(l => 
      !l.isRevoked && 
      (!l.expiresAt || new Date(l.expiresAt) > new Date())
    );
    
    // 获取最近的许可证
    const recentLicenses = licenses
      .slice(-5)
      .map(license => {
        const user = users.find(u => u.id === license.userId);
        const soft = software.find(s => s.id === license.softwareId);
        return {
          ...license,
          username: user ? user.username : '未知用户',
          softwareName: soft ? soft.name : '未知软件'
        };
      });

    return res.json({
      success: true,
      data: {
        totalUsers: users.length,
        totalSoftware: software.length,
        totalLicenses: licenses.length,
        activeLicenses: activeLicenses.length,
        recentLicenses: recentLicenses
      }
    });
  } catch (error) {
    console.error('[Admin] 获取统计数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

// 获取操作日志
router.get('/logs', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const limit = parseInt(req.query.limit as string) || 100;
    const logs = await DataService.getLogs(limit);

    return res.json({
      success: true,
      data: logs
    });
  } catch (error) {
    console.error('[Admin] 获取日志失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取日志失败'
    });
  }
});

// 用户管理
router.get('/users', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const users = await DataService.getUsers();
    const usersWithoutPasswords = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      createdAt: user.createdAt
    }));

    return res.json({
      success: true,
      data: usersWithoutPasswords
    });
  } catch (error) {
    console.error('[Admin] 获取用户列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

// 创建用户
router.post('/users', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const { username, email, password, roles } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱和密码不能为空'
      });
    }

    // 检查用户名是否已存在
    const existingUser = await DataService.getUserByUsername(username);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: '用户名已存在'
      });
    }

    const passwordHash = await UserService.hashPassword(password);
    const newUserId = await DataService.addUser({
      username,
      email,
      passwordHash,
      roles: roles || ['user'],
      createdAt: new Date().toISOString()
    });

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '创建用户',
      object: `用户: ${username}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '用户创建成功',
      data: { id: newUserId, username, email, roles }
    });
  } catch (error) {
    console.error('[Admin] 创建用户失败:', error);
    return res.status(500).json({
      success: false,
      message: '创建用户失败'
    });
  }
});

// 获取单个用户信息
router.get('/users/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
      return res.status(400).json({
        success: false,
        message: '用户ID无效'
      });
    }

    const users = await DataService.getUsers();
    const user = users.find(u => u.id === userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 返回用户信息（不包含密码）
    const { passwordHash, ...userInfo } = user;
    return res.json({
      success: true,
      data: userInfo
    });
  } catch (error) {
    console.error('[Admin] 获取用户信息失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 更新用户
router.put('/users/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const userId = parseInt(req.params.id);
    const { username, email, password, roles } = req.body;
    const updates: any = {};

    if (username) updates.username = username;
    if (email) updates.email = email;
    if (password) updates.passwordHash = await UserService.hashPassword(password);
    if (roles) updates.roles = roles;

    const success = await DataService.updateUser(userId, updates);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '更新用户',
      object: `用户ID: ${userId}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '用户更新成功'
    });
  } catch (error) {
    console.error('[Admin] 更新用户失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新用户失败'
    });
  }
});

// 删除用户
router.delete('/users/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const userId = parseInt(req.params.id);
    
    // 不能删除自己
    if (userId === req.user.id) {
      return res.status(400).json({
        success: false,
        message: '不能删除自己的账户'
      });
    }

    const user = await DataService.getUserById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const success = await DataService.deleteUser(userId);
    
    if (!success) {
      return res.status(500).json({
        success: false,
        message: '删除用户失败'
      });
    }

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '删除用户',
      object: `用户: ${user.username}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('[Admin] 删除用户失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除用户失败'
    });
  }
});

// 软件管理
router.get('/software', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const software = await DataService.getSoftware();
    return res.json({
      success: true,
      data: software
    });
  } catch (error) {
    console.error('[Admin] 获取软件列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取软件列表失败'
    });
  }
});

// 创建软件
router.post('/software', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const { name, productKey, description, isActive, price, trialDays } = req.body;

    if (!name || !productKey) {
      return res.status(400).json({
        success: false,
        message: '软件名称和产品密钥不能为空'
      });
    }

    const newSoftwareId = await DataService.addSoftware({
      name,
      productKey,
      description: description || '',
      isActive: isActive !== undefined ? isActive : true,
      price: price || 0,
      trialDays: trialDays || 7,
      createdAt: new Date().toISOString()
    });

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '创建软件',
      object: `软件: ${name}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '软件创建成功',
      data: { id: newSoftwareId, name, productKey }
    });
  } catch (error) {
    console.error('[Admin] 创建软件失败:', error);
    return res.status(500).json({
      success: false,
      message: '创建软件失败'
    });
  }
});

// 获取单个软件信息
router.get('/software/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const softwareId = parseInt(req.params.id);
    if (isNaN(softwareId)) {
      return res.status(400).json({
        success: false,
        message: '软件ID无效'
      });
    }

    const software = await DataService.getSoftware();
    const targetSoftware = software.find(s => s.id === softwareId);

    if (!targetSoftware) {
      return res.status(404).json({
        success: false,
        message: '软件不存在'
      });
    }

    return res.json({
      success: true,
      data: targetSoftware
    });
  } catch (error) {
    console.error('[Admin] 获取软件信息失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取软件信息失败'
    });
  }
});

// 更新软件
router.put('/software/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const softwareId = parseInt(req.params.id);
    const { name, productKey, description, isActive, price, trialDays } = req.body;
    const updates: any = {};

    if (name) updates.name = name;
    if (productKey) updates.productKey = productKey;
    if (description !== undefined) updates.description = description;
    if (isActive !== undefined) updates.isActive = isActive;
    if (price !== undefined) updates.price = price;
    if (trialDays !== undefined) updates.trialDays = trialDays;

    const success = await DataService.updateSoftware(softwareId, updates);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: '软件不存在'
      });
    }

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '更新软件',
      object: `软件ID: ${softwareId}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '软件更新成功'
    });
  } catch (error) {
    console.error('[Admin] 更新软件失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新软件失败'
    });
  }
});

// 删除软件
router.delete('/software/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const softwareId = parseInt(req.params.id);
    
    const success = await DataService.deleteSoftware(softwareId);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '软件不存在'
      });
    }

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '删除软件',
      object: `软件ID: ${softwareId}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '软件删除成功'
    });
  } catch (error) {
    console.error('[Admin] 删除软件失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除软件失败'
    });
  }
});

// 许可证管理
router.get('/licenses', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const licenses = await DataService.getLicenses();
    const users = await DataService.getUsers();
    const software = await DataService.getSoftware();
    
    const licensesWithDetails = licenses.map(license => {
      const user = users.find(u => u.id === license.userId);
      const soft = software.find(s => s.id === license.softwareId);
      return {
        ...license,
        username: user ? user.username : '未知用户',
        softwareName: soft ? soft.name : '未知软件'
      };
    });

    return res.json({
      success: true,
      data: licensesWithDetails
    });
  } catch (error) {
    console.error('[Admin] 获取许可证列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取许可证列表失败'
    });
  }
});

// 撤销许可证
router.put('/licenses/:id/revoke', authenticateToken, async (req: Request, res: Response) => {
  try {
    // 检查管理员权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const licenseId = parseInt(req.params.id);
    
    const success = await DataService.updateLicense(licenseId, { isRevoked: true });
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '许可证不存在'
      });
    }

    // 记录日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '撤销许可证',
      object: `许可证ID: ${licenseId}`,
      result: '成功'
    });

    return res.json({
      success: true,
      message: '许可证撤销成功'
    });
  } catch (error) {
    console.error('[Admin] 撤销许可证失败:', error);
    return res.status(500).json({
      success: false,
      message: '撤销许可证失败'
    });
  }
});

export default router; 