import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 用户注册
router.post('/register', UserController.register);

// 用户登录
router.post('/login', UserController.login);

// 获取当前用户信息（需要认证）
router.get('/profile', authenticateToken, UserController.getProfile);

// 更新用户信息（需要认证）
router.put('/profile', authenticateToken, UserController.updateProfile);

export default router; 