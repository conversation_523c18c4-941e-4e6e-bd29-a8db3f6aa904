import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: any;
        }
    }
}
/**
 * JWT认证中间件
 */
export declare const authenticateToken: (req: Request, res: Response, next: NextFunction) => void;
/**
 * 管理员权限验证中间件
 */
export declare const requireAdmin: (req: Request, res: Response, next: NextFunction) => void;
/**
 * 管理员或经理权限验证中间件
 */
export declare const requireManagerOrAdmin: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.d.ts.map