import { Model, Table, Column, DataType, HasMany } from 'sequelize-typescript';
import { License } from './License';

@Table({
  tableName: 'software',
  timestamps: true,
})
export class Software extends Model<Software> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      len: [1, 100],
    },
  })
  name!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    validate: {
      len: [1, 50],
    },
  })
  productKey!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description?: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  isActive!: boolean;

  @HasMany(() => License)
  licenses!: License[];
} 