"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const LicenseController_1 = require("../controllers/LicenseController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// 公开路由 - 激活许可证（不需要认证）
router.post('/activate', LicenseController_1.LicenseController.activate);
// 公开路由 - 验证许可证（不需要认证）
router.post('/validate/:licenseKey', LicenseController_1.LicenseController.validate);
// 需要认证的路由
// 获取许可证信息
router.get('/:licenseKey', auth_1.authenticateToken, LicenseController_1.LicenseController.getLicense);
// 管理员路由 - 撤销许可证（需要管理员或经理权限）
router.delete('/:licenseKey/revoke', auth_1.authenticateToken, auth_1.requireManagerOrAdmin, LicenseController_1.LicenseController.revoke);
exports.default = router;
//# sourceMappingURL=licenses.js.map