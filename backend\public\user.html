<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件权限管理系统 - 用户中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 模态对话框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        code {
            font-family: 'Consolas', 'Monaco', monospace;
            background: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            color: #333;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .nav-item {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-item:hover {
            color: #667eea;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 登录/注册页面样式 */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 80px);
            padding: 20px;
        }

        .auth-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .auth-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
        }

        .auth-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #667eea;
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 主页面样式 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .welcome-section h1 {
            color: #1f2937;
            margin-bottom: 10px;
        }

        .welcome-section p {
            color: #6b7280;
            font-size: 1.1rem;
        }

        /* 软件展示样式 */
        .software-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .software-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .software-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
        }

        .software-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .software-icon {
            width: 50px;
            height: 50px;
            background: #667eea;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 15px;
        }

        .software-info h3 {
            color: #1f2937;
            margin-bottom: 5px;
        }

        .software-info p {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .software-description {
            color: #4b5563;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .license-status {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-licensed {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .status-trial {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .status-unlicensed {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .software-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .error-message, .success-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
        }

        .error-message {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .success-message {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        /* 隐藏样式 */
        .hidden {
            display: none !important;
        }

        /* 加载动画 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .software-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
            
            .software-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="header">
        <div class="nav-container">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                软件权限管理系统
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-item" onclick="showSoftwareList()">软件中心</a>
                <a href="#" class="nav-item" onclick="showMyLicenses()">我的许可证</a>
            </div>
            <div class="user-info">
                <span id="userWelcome" class="hidden">欢迎，<span id="currentUsername"></span></span>
                <button id="logoutBtn" class="btn btn-secondary hidden" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </button>
            </div>
        </div>
    </div>

    <!-- 登录/注册页面 -->
    <div id="authPage" class="auth-container">
        <div class="auth-box">
            <div class="auth-tabs">
                <div class="auth-tab active" onclick="switchTab('login')">登录</div>
                <div class="auth-tab" onclick="switchTab('register')">注册</div>
            </div>

            <!-- 登录表单 -->
            <div id="loginForm" class="auth-form">
                <h2 style="text-align: center; margin-bottom: 30px; color: #1f2937;">用户登录</h2>
                <form id="loginFormElement" onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label for="loginUsername">用户名</label>
                        <input type="text" id="loginUsername" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <input type="password" id="loginPassword" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </button>
                </form>
            </div>

            <!-- 注册表单 -->
            <div id="registerForm" class="auth-form hidden">
                <h2 style="text-align: center; margin-bottom: 30px; color: #1f2937;">用户注册</h2>
                <form id="registerFormElement" onsubmit="handleRegister(event)">
                    <div class="form-group">
                        <label for="registerUsername">用户名</label>
                        <input type="text" id="registerUsername" required minlength="3">
                    </div>
                    <div class="form-group">
                        <label for="registerEmail">邮箱</label>
                        <input type="email" id="registerEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">密码</label>
                        <input type="password" id="registerPassword" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="registerConfirmPassword">确认密码</label>
                        <input type="password" id="registerConfirmPassword" required minlength="6">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> 注册
                    </button>
                </form>
            </div>

            <div id="authMessage"></div>
        </div>
    </div>

    <!-- 主页面 -->
    <div id="mainPage" class="main-container hidden">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <h1>欢迎使用软件权限管理系统</h1>
            <p>探索我们的软件产品，管理您的许可证和权限</p>
        </div>

        <!-- 软件列表 -->
        <div id="softwareSection">
            <h2 style="color: white; margin-bottom: 20px;">
                <i class="fas fa-box"></i> 软件产品
            </h2>
            <div id="softwareLoading" class="loading">
                <div class="spinner"></div>
                正在加载软件列表...
            </div>
            <div id="softwareGrid" class="software-grid"></div>
        </div>

        <!-- 我的许可证 -->
        <div id="licensesSection" class="hidden">
            <h2 style="color: white; margin-bottom: 20px;">
                <i class="fas fa-key"></i> 我的许可证
            </h2>
            <div id="licensesLoading" class="loading">
                <div class="spinner"></div>
                正在加载许可证列表...
            </div>
            <div id="licensesGrid" class="software-grid"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let currentToken = null;
        let softwareList = [];
        let userLicenses = [];

        // 统一的时间格式化函数
        function formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        }

        // 工具函数
        function resetForm(formId) {
            try {
                const form = document.getElementById(formId);
                if (form && typeof form.reset === 'function') {
                    form.reset();
                } else {
                    console.warn(`表单 ${formId} 不存在或不是有效的form元素`);
                }
            } catch (error) {
                console.error(`重置表单 ${formId} 时出错:`, error);
            }
        }

        function showMessage(elementId, message, isSuccess = false) {
            const element = document.getElementById(elementId);
            if (element) {
                const messageClass = isSuccess ? 'success-message' : 'error-message';
                element.innerHTML = `<div class="${messageClass}">${message}</div>`;
            }
        }

        // 通用API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...(currentToken && { 'Authorization': `Bearer ${currentToken}` }),
                        ...options.headers
                    }
                });

                // 处理认证错误
                if (response.status === 401 || response.status === 403) {
                    console.log('认证失败，清除本地存储并跳转到登录页面');
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('currentUser');
                    currentToken = null;
                    currentUser = null;
                    showAuthPage();
                    throw new Error('登录已过期，请重新登录');
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }

                return data;
            } catch (error) {
                console.error('API调用失败:', error);
                throw error;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户页面初始化...');
            
            // 检查是否已登录
            const token = localStorage.getItem('userToken');
            const userStr = localStorage.getItem('currentUser');
            
            if (token && userStr) {
                try {
                    currentToken = token;
                    currentUser = JSON.parse(userStr);
                    showMainPage();
                } catch (error) {
                    console.error('解析用户数据失败:', error);
                    // 清除无效数据
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('currentUser');
                    showAuthPage();
                }
            } else {
                showAuthPage();
            }
        });

        // 显示认证页面
        function showAuthPage() {
            document.getElementById('authPage').classList.remove('hidden');
            document.getElementById('mainPage').classList.add('hidden');
        }

        // 显示主页面
        function showMainPage() {
            console.log('显示主页面，当前用户:', currentUser);
            
            if (!currentUser || !currentUser.username) {
                console.error('currentUser 或 username 未定义:', currentUser);
                showMessage('authMessage', '用户信息错误，请重新登录');
                showAuthPage();
                return;
            }
            
            document.getElementById('authPage').classList.add('hidden');
            document.getElementById('mainPage').classList.remove('hidden');
            
            // 更新用户信息
            document.getElementById('currentUsername').textContent = currentUser.username;
            document.getElementById('userWelcome').classList.remove('hidden');
            document.getElementById('logoutBtn').classList.remove('hidden');
            
            // 加载软件列表
            loadSoftwareList();
        }

        // 切换登录/注册标签
        function switchTab(tab) {
            try {
                // 更新标签样式
                document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
                
                // 如果是通过点击事件调用，更新点击的标签
                if (event && event.target) {
                    event.target.classList.add('active');
                } else {
                    // 如果是程序调用，找到对应的标签
                    const tabs = document.querySelectorAll('.auth-tab');
                    const tabIndex = tab === 'login' ? 0 : 1;
                    if (tabs[tabIndex]) {
                        tabs[tabIndex].classList.add('active');
                    }
                }
                
                // 显示对应表单
                if (tab === 'login') {
                    document.getElementById('loginForm').classList.remove('hidden');
                    document.getElementById('registerForm').classList.add('hidden');
                } else {
                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('registerForm').classList.remove('hidden');
                }
                
                // 清除消息
                showMessage('authMessage', '');
                
            } catch (error) {
                console.error('切换标签时出错:', error);
            }
        }

        // 处理登录
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            // 清除之前的消息
            showMessage('authMessage', '');
            
            if (!username || !password) {
                showMessage('authMessage', '请输入用户名和密码');
                return;
            }
            
            try {
                console.log('发送登录请求...');
                const response = await fetch('/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('登录响应状态:', response.status);
                const data = await response.json();
                console.log('登录响应数据:', data);
                
                if (data.success) {
                    // 验证返回的数据结构
                    if (!data.data || !data.data.user || !data.data.token) {
                        console.error('登录响应数据结构不正确:', data);
                        showMessage('authMessage', '登录响应数据格式错误');
                        return;
                    }
                    
                    // 保存登录信息
                    currentToken = data.data.token;
                    currentUser = data.data.user;
                    localStorage.setItem('userToken', data.data.token);
                    localStorage.setItem('currentUser', JSON.stringify(data.data.user));
                    
                    console.log('已设置currentUser:', currentUser);
                    console.log('已设置currentToken:', currentToken);
                    
                    showMessage('authMessage', '登录成功！正在跳转...', true);
                    
                    setTimeout(() => {
                        showMainPage();
                    }, 1000);
                } else {
                    showMessage('authMessage', data.message || '登录失败');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showMessage('authMessage', '网络错误，请重试');
            }
        }

        // 处理注册
        async function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('registerConfirmPassword').value;
            
            // 清除之前的消息
            showMessage('authMessage', '');
            
            // 基本验证
            if (!username || !email || !password || !confirmPassword) {
                showMessage('authMessage', '请填写所有字段');
                return;
            }
            
            if (password.length < 6) {
                showMessage('authMessage', '密码长度至少为6位');
                return;
            }
            
            // 验证密码
            if (password !== confirmPassword) {
                showMessage('authMessage', '两次输入的密码不一致');
                return;
            }
            
            try {
                const response = await fetch('/api/user/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showMessage('authMessage', '注册成功！请登录', true);
                    
                    // 清空注册表单
                    resetForm('registerFormElement');
                    
                    // 切换到登录表单
                    setTimeout(() => {
                        switchTab('login');
                    }, 1500);
                } else {
                    showMessage('authMessage', data.message || '注册失败');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showMessage('authMessage', '网络错误，请重试');
            }
        }

        // 加载软件列表
        async function loadSoftwareList() {
            try {
                document.getElementById('softwareLoading').style.display = 'block';
                document.getElementById('softwareGrid').innerHTML = '';
                
                // 检查token
                if (!currentToken) {
                    throw new Error('用户未登录，请重新登录');
                }
                
                console.log('开始加载软件列表...');
                
                // 获取软件列表
                const softwareData = await apiCall('/api/user/software');
                console.log('软件列表数据:', softwareData);
                
                if (!softwareData.success) {
                    throw new Error(softwareData.message || '获取软件列表失败');
                }
                
                softwareList = softwareData.data || [];
                
                // 获取用户许可证
                console.log('开始获取用户许可证...');
                try {
                    const licensesData = await apiCall('/api/user/licenses');
                    console.log('许可证数据:', licensesData);
                    userLicenses = licensesData.data || [];
                    console.log('处理后的用户许可证列表:', userLicenses);
                } catch (error) {
                    console.warn('获取许可证失败，继续显示软件列表:', error);
                    userLicenses = [];
                }
                
                // 渲染软件列表
                renderSoftwareList();
                
            } catch (error) {
                console.error('加载软件列表失败:', error);
                const errorMessage = error.message || '未知错误';
                
                // 如果不是认证错误（认证错误会自动跳转），显示错误信息
                if (!error.message.includes('登录已过期')) {
                    document.getElementById('softwareGrid').innerHTML = 
                        `<div class="error-message">
                            <p>加载失败：${errorMessage}</p>
                            <button class="btn btn-primary" onclick="loadSoftwareList()" style="margin-top: 10px;">
                                <i class="fas fa-redo"></i> 重试
                            </button>
                        </div>`;
                }
            } finally {
                document.getElementById('softwareLoading').style.display = 'none';
            }
        }

        // 渲染软件列表
        function renderSoftwareList() {
            const grid = document.getElementById('softwareGrid');
            grid.innerHTML = '';
            
            softwareList.forEach(software => {
                // 查找该软件的所有有效许可证（未撤销的）
                const softwareLicenses = userLicenses.filter(l => 
                    l.softwareId === software.id && !l.isRevoked
                );
                
                console.log(`软件 ${software.name} (ID: ${software.id}) 的许可证:`, softwareLicenses);
                
                // 找到最佳许可证（优先级：未过期的正式版 > 未过期的试用版 > 已过期的）
                let bestLicense = null;
                const now = new Date();
                
                if (softwareLicenses.length > 0) {
                    // 按优先级排序许可证
                    const sortedLicenses = softwareLicenses.sort((a, b) => {
                        const aExpired = a.expiresAt && new Date(a.expiresAt) < now;
                        const bExpired = b.expiresAt && new Date(b.expiresAt) < now;
                        
                        // 未过期的排在前面
                        if (aExpired !== bExpired) {
                            return aExpired ? 1 : -1;
                        }
                        
                        // 都未过期时，正式版排在试用版前面
                        if (!aExpired && !bExpired) {
                            if (a.isTrial !== b.isTrial) {
                                return a.isTrial ? 1 : -1;
                            }
                        }
                        
                        // 按过期时间排序（晚过期的排在前面）
                        if (a.expiresAt && b.expiresAt) {
                            return new Date(b.expiresAt) - new Date(a.expiresAt);
                        }
                        
                        return 0;
                    });
                    
                    bestLicense = sortedLicenses[0];
                }
                
                console.log(`软件 ${software.name} 的最佳许可证:`, bestLicense);
                
                const hasLicense = !!bestLicense;
                const isExpired = bestLicense && bestLicense.expiresAt && 
                    new Date(bestLicense.expiresAt) < now;
                
                let statusClass, statusText, statusIcon;
                if (hasLicense && !isExpired) {
                    if (bestLicense.isTrial) {
                        statusClass = 'status-trial';
                        statusText = `试用版 (${bestLicense.expiresAt ? '到期：' + new Date(bestLicense.expiresAt).toLocaleDateString() : '永久'})`;
                        statusIcon = 'fas fa-clock';
                    } else {
                        statusClass = 'status-licensed';
                        statusText = '已授权';
                        statusIcon = 'fas fa-check-circle';
                    }
                } else if (hasLicense && isExpired) {
                    statusClass = 'status-unlicensed';
                    statusText = `已过期 (${new Date(bestLicense.expiresAt).toLocaleDateString()})`;
                    statusIcon = 'fas fa-exclamation-triangle';
                } else {
                    statusClass = 'status-unlicensed';
                    statusText = '未授权';
                    statusIcon = 'fas fa-times-circle';
                }
                
                const card = document.createElement('div');
                card.className = 'software-card';
                card.innerHTML = `
                    <div class="software-header">
                        <div class="software-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="software-info">
                            <h3>${software.name}</h3>
                        </div>
                    </div>
                    <div class="software-description">
                        ${software.description || '这是一款优秀的软件产品'}
                    </div>
                    <div class="license-status ${statusClass}">
                        <i class="${statusIcon}"></i>
                        <span style="margin-left: 8px;">${statusText}</span>
                    </div>
                    <div class="software-actions">
                        ${hasLicense && !isExpired ? 
                            `<button class="btn btn-success" onclick="showMachineIdDialog('${software.id}', '${bestLicense.licenseKey}')">
                                <i class="fas fa-download"></i> 下载激活文件
                            </button>` : 
                            `<button class="btn btn-warning" onclick="requestTrial('${software.id}')">
                                <i class="fas fa-play"></i> 申请试用
                            </button>
                            <button class="btn btn-primary" onclick="purchaseLicense('${software.id}')">
                                <i class="fas fa-shopping-cart"></i> 购买许可证
                            </button>`
                        }
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        // 申请试用
        async function requestTrial(softwareId) {
            try {
                const data = await apiCall('/api/user/trial', {
                    method: 'POST',
                    body: JSON.stringify({ softwareId })
                });
                
                if (data.success) {
                    alert('试用申请成功！试用许可证已生成。');
                    // 重新加载软件列表
                    loadSoftwareList();
                } else {
                    alert('申请失败：' + data.message);
                }
            } catch (error) {
                alert('申请失败：' + error.message);
            }
        }

        // 购买许可证
        function purchaseLicense(softwareId) {
            // TODO: 实现购买流程
            alert('购买功能开发中，请联系管理员获取正式许可证。');
        }

        // 显示机器码输入对话框
        function showMachineIdDialog(softwareId, licenseKey) {
            const dialog = document.createElement('div');
            dialog.className = 'modal';
            dialog.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <h2 style="margin-bottom: 20px;">输入目标机器码</h2>
                    <div style="margin-bottom: 20px;">
                        <p style="color: #666; margin-bottom: 10px;">请在目标计算机上运行Unity软件，复制显示的机器码：</p>
                        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                            <p style="margin-bottom: 5px;">Unity控制台中会显示类似以下信息：</p>
                            <code>[LicenseManager] 生成机器码:<br>
  设备标识符: xxxx<br>
  设备名称: xxxx<br>
  处理器: xxxx<br>
  显卡: xxxx<br>
  系统: xxxx<br>
  机器码: MACHINE-XXXXXXXX</code>
                        </div>
                        <p style="color: #666; margin-bottom: 10px;">请复制最后一行的<strong>机器码</strong>，格式为：MACHINE-后跟8位字母数字</p>
                    </div>
                    <div class="form-group">
                        <label for="machineId">机器码：</label>
                        <input type="text" id="machineIdInput" class="form-control" 
                               placeholder="例如：MACHINE-A1B2C3D4"
                               style="width: 100%; margin-bottom: 10px;">
                        <div id="machineIdError" style="color: red; display: none; margin-bottom: 10px;"></div>
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button class="btn btn-secondary" onclick="closeMachineIdDialog()">取消</button>
                        <button class="btn btn-primary" onclick="validateAndDownload('${softwareId}', '${licenseKey}')">
                            下载激活文件
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(dialog);
        }

        // 关闭机器码对话框
        function closeMachineIdDialog() {
            const dialog = document.querySelector('.modal');
            if (dialog) {
                dialog.remove();
            }
        }

        // 验证机器码格式
        function validateMachineId(machineId) {
            // 基本格式验证：MACHINE-{hash}
            const pattern = /^MACHINE-[A-F0-9]{8}$/;
            return pattern.test(machineId);
        }

        // 验证并下载
        async function validateAndDownload(softwareId, licenseKey) {
            const machineIdInput = document.getElementById('machineIdInput');
            const machineIdError = document.getElementById('machineIdError');
            const machineId = machineIdInput.value.trim();

            // 验证机器码
            if (!machineId) {
                machineIdError.textContent = '请输入机器码';
                machineIdError.style.display = 'block';
                return;
            }

            if (!validateMachineId(machineId)) {
                machineIdError.textContent = '机器码格式无效，请确保从Unity软件中复制完整的机器码';
                machineIdError.style.display = 'block';
                return;
            }

            // 隐藏错误信息
            machineIdError.style.display = 'none';

            // 关闭对话框
            closeMachineIdDialog();

            // 下载激活文件
            await downloadActivationFile(softwareId, licenseKey, machineId);
        }

        // 下载激活文件
        async function downloadActivationFile(softwareId, licenseKey, machineId) {
            try {
                const response = await fetch(`/api/user/activation-file/${licenseKey}/${machineId}`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                // 处理认证错误
                if (response.status === 401 || response.status === 403) {
                    console.log('认证失败，清除本地存储并跳转到登录页面');
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('currentUser');
                    currentToken = null;
                    currentUser = null;
                    showAuthPage();
                    alert('登录已过期，请重新登录');
                    return;
                }
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `activation_${licenseKey}.json`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    const errorData = await response.json();
                    alert('下载失败：' + errorData.message);
                }
            } catch (error) {
                alert('下载失败：' + error.message);
            }
        }

        // 显示软件列表
        function showSoftwareList() {
            document.getElementById('softwareSection').classList.remove('hidden');
            document.getElementById('licensesSection').classList.add('hidden');
        }

        // 显示我的许可证
        function showMyLicenses() {
            document.getElementById('softwareSection').classList.add('hidden');
            document.getElementById('licensesSection').classList.remove('hidden');
            loadMyLicenses();
        }

        // 加载我的许可证
        async function loadMyLicenses() {
            try {
                document.getElementById('licensesLoading').style.display = 'block';
                document.getElementById('licensesGrid').innerHTML = '';
                
                const data = await apiCall('/api/user/licenses');
                
                if (!data.success) {
                    throw new Error(data.message || '获取许可证列表失败');
                }
                
                const licenses = data.data;
                
                const grid = document.getElementById('licensesGrid');
                grid.innerHTML = '';
                
                if (licenses.length === 0) {
                    grid.innerHTML = '<div style="text-align: center; color: white; padding: 40px;">您还没有任何许可证</div>';
                    return;
                }
                
                licenses.forEach(license => {
                    const isExpired = license.expiresAt && new Date(license.expiresAt) < new Date();
                    const statusClass = license.isRevoked ? 'status-unlicensed' : 
                                      isExpired ? 'status-trial' : 'status-licensed';
                    const statusText = license.isRevoked ? '已撤销' : 
                                     isExpired ? '已过期' : '有效';
                    
                    const card = document.createElement('div');
                    card.className = 'software-card';
                    card.innerHTML = `
                        <div class="software-header">
                            <div class="software-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="software-info">
                                <h3>${license.softwareName}</h3>
                                <p>许可证: ${license.licenseKey}</p>
                            </div>
                        </div>
                        <div class="software-description">
                            <strong>激活类型:</strong> ${license.activationType === 'online' ? '在线激活' : '离线激活'}<br>
                            <strong>激活时间:</strong> ${license.activatedAt ? formatDateTime(license.activatedAt) : '未激活'}<br>
                            <strong>过期时间:</strong> ${license.expiresAt ? formatDateTime(license.expiresAt) : '永久有效'}<br>
                            ${license.isTrial ? '<strong style="color: #f59e0b;">试用版本</strong>' : ''}
                        </div>
                        <div class="license-status ${statusClass}">
                            <i class="fas fa-info-circle"></i>
                            <span style="margin-left: 8px;">${statusText}</span>
                        </div>
                        <div class="software-actions">
                            ${!license.isRevoked && !isExpired ? 
                                `<button class="btn btn-success" onclick="showMachineIdDialog('${license.softwareId}', '${license.licenseKey}')">
                                    <i class="fas fa-download"></i> 下载激活文件
                                </button>` : ''
                            }
                        </div>
                    `;
                    
                    grid.appendChild(card);
                });
                
            } catch (error) {
                console.error('加载许可证失败:', error);
                document.getElementById('licensesGrid').innerHTML = 
                    `<div class="error-message">加载失败：${error.message}</div>`;
            } finally {
                document.getElementById('licensesLoading').style.display = 'none';
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('userToken');
            localStorage.removeItem('currentUser');
            currentToken = null;
            currentUser = null;
            showAuthPage();
        }
    </script>
</body>
</html> 