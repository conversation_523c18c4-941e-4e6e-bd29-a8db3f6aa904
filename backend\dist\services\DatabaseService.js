"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class DatabaseService {
    constructor() {
        this.dbPath = path.join(process.cwd(), 'data', 'license_management.db');
        this.schemaPath = path.join(__dirname, '../../src/database/schema.sql');
        // 确保data目录存在
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
    }
    static getInstance() {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3_1.default.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('[DatabaseService] 数据库连接失败:', err.message);
                    reject(err);
                }
                else {
                    console.log('[DatabaseService] 数据库连接成功:', this.dbPath);
                    this.initializeSchema()
                        .then(() => resolve())
                        .catch(reject);
                }
            });
        });
    }
    async initializeSchema() {
        return new Promise((resolve, reject) => {
            if (!fs.existsSync(this.schemaPath)) {
                reject(new Error(`Schema file not found: ${this.schemaPath}`));
                return;
            }
            const schema = fs.readFileSync(this.schemaPath, 'utf8');
            this.db.exec(schema, (err) => {
                if (err) {
                    console.error('[DatabaseService] 数据库初始化失败:', err.message);
                    reject(err);
                }
                else {
                    console.log('[DatabaseService] 数据库表结构初始化成功');
                    resolve();
                }
            });
        });
    }
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('[DatabaseService] 关闭数据库失败:', err.message);
                    }
                    else {
                        console.log('[DatabaseService] 数据库连接已关闭');
                    }
                    resolve();
                });
            }
            else {
                resolve();
            }
        });
    }
    // 用户相关操作
    async getUsers() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM users ORDER BY id', (err, rows) => {
                if (err) {
                    reject(err);
                }
                else {
                    const users = rows.map(row => ({
                        id: row.id,
                        username: row.username,
                        email: row.email,
                        passwordHash: row.password_hash,
                        roles: JSON.parse(row.roles),
                        createdAt: row.created_at
                    }));
                    resolve(users);
                }
            });
        });
    }
    async getUserById(id) {
        return new Promise((resolve, reject) => {
            this.db.get('SELECT * FROM users WHERE id = ?', [id], (err, row) => {
                if (err) {
                    reject(err);
                }
                else if (row) {
                    resolve({
                        id: row.id,
                        username: row.username,
                        email: row.email,
                        passwordHash: row.password_hash,
                        roles: JSON.parse(row.roles),
                        createdAt: row.created_at
                    });
                }
                else {
                    resolve(null);
                }
            });
        });
    }
    async getUserByUsername(username) {
        return new Promise((resolve, reject) => {
            this.db.get('SELECT * FROM users WHERE username = ?', [username], (err, row) => {
                if (err) {
                    reject(err);
                }
                else if (row) {
                    resolve({
                        id: row.id,
                        username: row.username,
                        email: row.email,
                        passwordHash: row.password_hash,
                        roles: JSON.parse(row.roles),
                        createdAt: row.created_at
                    });
                }
                else {
                    resolve(null);
                }
            });
        });
    }
    async createUser(user) {
        return new Promise((resolve, reject) => {
            const stmt = this.db.prepare(`
        INSERT INTO users (username, email, password_hash, roles, created_at) 
        VALUES (?, ?, ?, ?, ?)
      `);
            stmt.run([
                user.username,
                user.email,
                user.passwordHash,
                JSON.stringify(user.roles),
                user.createdAt
            ], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.lastID);
                }
            });
            stmt.finalize();
        });
    }
    async updateUser(id, updates) {
        return new Promise((resolve, reject) => {
            const fields = [];
            const values = [];
            if (updates.username !== undefined) {
                fields.push('username = ?');
                values.push(updates.username);
            }
            if (updates.email !== undefined) {
                fields.push('email = ?');
                values.push(updates.email);
            }
            if (updates.passwordHash !== undefined) {
                fields.push('password_hash = ?');
                values.push(updates.passwordHash);
            }
            if (updates.roles !== undefined) {
                fields.push('roles = ?');
                values.push(JSON.stringify(updates.roles));
            }
            if (fields.length === 0) {
                resolve(false);
                return;
            }
            values.push(id);
            const sql = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
            this.db.run(sql, values, function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.changes > 0);
                }
            });
        });
    }
    async deleteUser(id) {
        return new Promise((resolve, reject) => {
            this.db.run('DELETE FROM users WHERE id = ?', [id], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.changes > 0);
                }
            });
        });
    }
    // 软件相关操作
    async getSoftware() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM software ORDER BY id', (err, rows) => {
                if (err) {
                    reject(err);
                }
                else {
                    const software = rows.map(row => ({
                        id: row.id,
                        name: row.name,
                        productKey: row.product_key,
                        description: row.description,
                        isActive: !!row.is_active,
                        price: row.price,
                        trialDays: row.trial_days,
                        createdAt: row.created_at
                    }));
                    resolve(software);
                }
            });
        });
    }
    async createSoftware(software) {
        return new Promise((resolve, reject) => {
            const stmt = this.db.prepare(`
        INSERT INTO software (name, product_key, description, is_active, price, trial_days, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
            stmt.run([
                software.name,
                software.productKey,
                software.description,
                software.isActive ? 1 : 0,
                software.price,
                software.trialDays,
                software.createdAt
            ], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.lastID);
                }
            });
            stmt.finalize();
        });
    }
    async updateSoftware(id, updates) {
        return new Promise((resolve, reject) => {
            const fields = [];
            const values = [];
            if (updates.name !== undefined) {
                fields.push('name = ?');
                values.push(updates.name);
            }
            if (updates.productKey !== undefined) {
                fields.push('product_key = ?');
                values.push(updates.productKey);
            }
            if (updates.description !== undefined) {
                fields.push('description = ?');
                values.push(updates.description);
            }
            if (updates.isActive !== undefined) {
                fields.push('is_active = ?');
                values.push(updates.isActive ? 1 : 0);
            }
            if (updates.price !== undefined) {
                fields.push('price = ?');
                values.push(updates.price);
            }
            if (updates.trialDays !== undefined) {
                fields.push('trial_days = ?');
                values.push(updates.trialDays);
            }
            if (fields.length === 0) {
                resolve(false);
                return;
            }
            values.push(id);
            const sql = `UPDATE software SET ${fields.join(', ')} WHERE id = ?`;
            this.db.run(sql, values, function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.changes > 0);
                }
            });
        });
    }
    async deleteSoftware(id) {
        return new Promise((resolve, reject) => {
            this.db.run('DELETE FROM software WHERE id = ?', [id], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.changes > 0);
                }
            });
        });
    }
    // 许可证相关操作
    async getLicenses() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM licenses ORDER BY id', (err, rows) => {
                if (err) {
                    reject(err);
                }
                else {
                    const licenses = rows.map(row => ({
                        id: row.id,
                        licenseKey: row.license_key,
                        softwareId: row.software_id,
                        userId: row.user_id,
                        activationType: row.activation_type,
                        activatedAt: row.activated_at,
                        expiresAt: row.expires_at,
                        machineId: row.machine_id,
                        isRevoked: !!row.is_revoked,
                        isTrial: !!row.is_trial,
                        createdAt: row.created_at
                    }));
                    resolve(licenses);
                }
            });
        });
    }
    async createLicense(license) {
        return new Promise((resolve, reject) => {
            const stmt = this.db.prepare(`
        INSERT INTO licenses (license_key, software_id, user_id, activation_type, activated_at, expires_at, machine_id, is_revoked, is_trial, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
            stmt.run([
                license.licenseKey,
                license.softwareId,
                license.userId,
                license.activationType,
                license.activatedAt,
                license.expiresAt,
                license.machineId,
                license.isRevoked ? 1 : 0,
                license.isTrial ? 1 : 0,
                license.createdAt
            ], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.lastID);
                }
            });
            stmt.finalize();
        });
    }
    async updateLicense(id, updates) {
        return new Promise((resolve, reject) => {
            const fields = [];
            const values = [];
            if (updates.activatedAt !== undefined) {
                fields.push('activated_at = ?');
                values.push(updates.activatedAt);
            }
            if (updates.expiresAt !== undefined) {
                fields.push('expires_at = ?');
                values.push(updates.expiresAt);
            }
            if (updates.machineId !== undefined) {
                fields.push('machine_id = ?');
                values.push(updates.machineId);
            }
            if (updates.isRevoked !== undefined) {
                fields.push('is_revoked = ?');
                values.push(updates.isRevoked ? 1 : 0);
            }
            if (fields.length === 0) {
                resolve(false);
                return;
            }
            values.push(id);
            const sql = `UPDATE licenses SET ${fields.join(', ')} WHERE id = ?`;
            this.db.run(sql, values, function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.changes > 0);
                }
            });
        });
    }
    async deleteLicense(id) {
        return new Promise((resolve, reject) => {
            this.db.run('DELETE FROM licenses WHERE id = ?', [id], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.changes > 0);
                }
            });
        });
    }
    // 日志相关操作
    async createLog(log) {
        return new Promise((resolve, reject) => {
            const stmt = this.db.prepare(`
        INSERT INTO logs (user_id, username, action, object, result, details, timestamp) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
            // 使用ISO 8601格式的时间戳，确保前端能正确解析
            const timestamp = new Date().toISOString();
            stmt.run([
                log.userId || null,
                log.username,
                log.action,
                log.object || null,
                log.result,
                log.details || null,
                timestamp
            ], function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this.lastID);
                }
            });
            stmt.finalize();
        });
    }
    async getLogs(limit = 100) {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM logs ORDER BY timestamp DESC LIMIT ?', [limit], (err, rows) => {
                if (err) {
                    reject(err);
                }
                else {
                    const logs = rows.map(row => ({
                        id: row.id,
                        userId: row.user_id,
                        username: row.username,
                        action: row.action,
                        object: row.object,
                        result: row.result,
                        details: row.details,
                        timestamp: row.timestamp
                    }));
                    resolve(logs);
                }
            });
        });
    }
}
exports.DatabaseService = DatabaseService;
//# sourceMappingURL=DatabaseService.js.map