export declare class UserService {
    private static readonly JWT_SECRET;
    private static readonly JWT_EXPIRES_IN;
    private static readonly BCRYPT_ROUNDS;
    private static readonly SIGNATURE_SECRET;
    /**
     * 哈希密码
     */
    static hashPassword(password: string): Promise<string>;
    /**
     * 验证密码
     */
    static verifyPassword(password: string, hash: string): Promise<boolean>;
    /**
     * 生成JWT令牌
     */
    static generateToken(payload: object): string;
    /**
     * 验证JWT令牌
     */
    static verifyToken(token: string): any;
    /**
     * 生成数字签名
     */
    static generateSignature(data: string): string;
    /**
     * 验证数字签名
     */
    static verifySignature(data: string, signature: string): boolean;
    /**
     * 生成用户登录响应
     */
    static createLoginResponse(success: boolean, data?: any, message?: string): any;
}
//# sourceMappingURL=UserService.d.ts.map