{"name": "license-management-backend", "version": "1.0.0", "description": "软件权限管理系统后端", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all", "lint": "eslint src/**/*.ts", "test": "jest"}, "keywords": ["license", "management", "authentication", "nodejs"], "author": "License Management System", "license": "MIT", "dependencies": {"@types/sqlite3": "^5.1.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "sequelize": "^6.32.1", "sequelize-typescript": "^2.1.5", "sqlite3": "^5.1.7", "uuid": "^9.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.4.7", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.46.0", "jest": "^29.6.2", "sequelize-cli": "^6.6.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}}