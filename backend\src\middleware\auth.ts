import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/UserService';

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

/**
 * JWT认证中间件
 */
export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    res.status(401).json({
      success: false,
      message: '访问令牌缺失'
    });
    return;
  }

  try {
    const decoded = UserService.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(403).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
};

/**
 * 管理员权限验证中间件
 */
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: '未认证的用户'
    });
    return;
  }

  if (!req.user.roles || !req.user.roles.includes('admin')) {
    res.status(403).json({
      success: false,
      message: '需要管理员权限'
    });
    return;
  }

  next();
};

/**
 * 管理员或经理权限验证中间件
 */
export const requireManagerOrAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: '未认证的用户'
    });
    return;
  }

  const allowedRoles = ['admin', 'manager'];
  const hasPermission = req.user.roles && req.user.roles.some((role: string) => allowedRoles.includes(role));

  if (!hasPermission) {
    res.status(403).json({
      success: false,
      message: '权限不足'
    });
    return;
  }

  next();
}; 