--- node/common.gypi
+++ node/common.gypi
@@ -188,17 +188,28 @@
           'v8_enable_handle_zapping': 0,
           'pgo_generate': ' -fprofile-generate ',
           'pgo_use': ' -fprofile-use -fprofile-correction ',
-          'lto': ' -flto=4 -fuse-linker-plugin -ffat-lto-objects ',
           'conditions': [
             ['node_shared != "true"', {
               'MSVC_runtimeType': 0    # MultiThreaded (/MT)
             }, {
               'MSVC_runtimeType': 2   # MultiThreadedDLL (/MD)
             }],
+            ['llvm_version=="0.0"', {
+              'lto': ' -flto=4 -ffat-lto-objects ', # GCC
+            }, {
+              'lto': ' -flto ', # Clang
+            }],
           ],
         },
         'cflags': [ '-O3' ],
         'conditions': [
+          ['enable_lto=="true"', {
+            'cflags': ['<(lto)'],
+            'ldflags': ['<(lto)'],
+            'xcode_settings': {
+              'LLVM_LTO': 'YES',
+            },
+          }],
           ['OS=="solaris"', {
             # pull in V8's postmortem metadata
             'ldflags': [ '-Wl,-z,allextract' ]
@@ -216,10 +227,6 @@
                 'cflags': ['<(pgo_use)'],
                 'ldflags': ['<(pgo_use)'],
               },],
-              ['enable_lto=="true"', {
-                'cflags': ['<(lto)'],
-                'ldflags': ['<(lto)'],
-              },],
             ],
           },],
           ['OS == "android"', {
--- node/configure.py
+++ node/configure.py
@@ -154,7 +154,7 @@ parser.add_option("--enable-lto",
     action="store_true",
     dest="enable_lto",
     help="Enable compiling with lto of a binary. This feature is only available "
-         "on linux with gcc and g++ 5.4.1 or newer.")
+         "with gcc 5.4.1+ or clang 3.9.1+.")
 
 parser.add_option("--link-module",
     action="append",
@@ -842,6 +842,7 @@ def get_gas_version(cc):
 # quite prepared to go that far yet.
 def check_compiler(o):
   if sys.platform == 'win32':
+    o['variables']['llvm_version'] = '0.0'
     if not options.openssl_no_asm and options.dest_cpu in ('x86', 'x64'):
       nasm_version = get_nasm_version('nasm')
       o['variables']['nasm_version'] = nasm_version
@@ -1021,12 +1022,19 @@ def configure_mips(o, target_arch):
   host_byteorder = 'little' if target_arch in ('mipsel', 'mips64el') else 'big'
   o['variables']['v8_host_byteorder'] = host_byteorder
 
+def clang_version_ge(version_checked):
+  for compiler in [(CC, 'c'), (CXX, 'c++')]:
+    ok, is_clang, clang_version, gcc_version = \
+      try_check_compiler(compiler[0], compiler[1])
+    if is_clang and clang_version >= version_checked:
+      return True
+  return False
 
 def gcc_version_ge(version_checked):
   for compiler in [(CC, 'c'), (CXX, 'c++')]:
-    ok, is_clang, clang_version, compiler_version = \
+    ok, is_clang, clang_version, gcc_version = \
       try_check_compiler(compiler[0], compiler[1])
-    if is_clang or compiler_version < version_checked:
+    if is_clang or gcc_version < version_checked:
       return False
   return True
 
@@ -1103,18 +1111,19 @@ def configure_node(o):
   o['variables']['enable_pgo_generate'] = b(options.enable_pgo_generate)
   o['variables']['enable_pgo_use']      = b(options.enable_pgo_use)
 
-  if flavor != 'linux' and (options.enable_lto):
+  if flavor == 'win' and (options.enable_lto):
     raise Exception(
-      'The lto option is supported only on linux.')
-
-  if flavor == 'linux':
-    if options.enable_lto:
-      version_checked = (5, 4, 1)
-      if not gcc_version_ge(version_checked):
-        version_checked_str = ".".join(map(str, version_checked))
-        raise Exception(
-          'The option --enable-lto is supported for gcc and gxx %s'
-          ' or newer only.' % (version_checked_str))
+      'Use Link Time Code Generation instead.')
+
+  if options.enable_lto:
+    gcc_version_checked = (5, 4, 1)
+    clang_version_checked = (3, 9, 1)
+    if not gcc_version_ge(gcc_version_checked) and not clang_version_ge(clang_version_checked):
+      gcc_version_checked_str = ".".join(map(str, gcc_version_checked))
+      clang_version_checked_str = ".".join(map(str, clang_version_checked))
+      raise Exception(
+        'The option --enable-lto is supported for gcc %s+'
+        'or clang %s+ only.' % (gcc_version_checked_str, clang_version_checked_str))
 
   o['variables']['enable_lto'] = b(options.enable_lto)
 
--- node/deps/v8/include/v8.h
+++ node/deps/v8/include/v8.h
@@ -9047,6 +9047,10 @@ class V8_EXPORT V8 {
                                       char** argv,
                                       bool remove_flags);
 
+  static void EnableCompilationForSourcelessUse();
+  static void DisableCompilationForSourcelessUse();
+  static void FixSourcelessScript(Isolate* v8_isolate, Local<UnboundScript> script);
+
   /** Get the version string. */
   static const char* GetVersion();
 
--- node/deps/v8/src/api/api.cc
+++ node/deps/v8/src/api/api.cc
@@ -915,6 +915,34 @@ void V8::SetFlagsFromCommandLine(int* argc, char** argv, bool remove_flags) {
   i::FlagList::SetFlagsFromCommandLine(argc, argv, remove_flags);
 }
 
+
+bool save_lazy;
+bool save_predictable;
+
+
+void V8::EnableCompilationForSourcelessUse() {
+  save_lazy = i::FLAG_lazy;
+  i::FLAG_lazy = false;
+  save_predictable = i::FLAG_predictable;
+  i::FLAG_predictable = true;
+}
+
+
+void V8::DisableCompilationForSourcelessUse() {
+  i::FLAG_lazy = save_lazy;
+  i::FLAG_predictable = save_predictable;
+}
+
+
+void V8::FixSourcelessScript(Isolate* v8_isolate, Local<UnboundScript> unbound_script) {
+  auto isolate = reinterpret_cast<i::Isolate*>(v8_isolate);
+  auto function_info =
+      i::Handle<i::SharedFunctionInfo>::cast(Utils::OpenHandle(*unbound_script));
+  i::Handle<i::Script> script(i::Script::cast(function_info->script()), isolate);
+  script->set_source(i::ReadOnlyRoots(isolate).undefined_value());
+}
+
+
 RegisteredExtension* RegisteredExtension::first_extension_ = nullptr;
 
 RegisteredExtension::RegisteredExtension(std::unique_ptr<Extension> extension)
--- node/deps/v8/src/codegen/compiler.cc
+++ node/deps/v8/src/codegen/compiler.cc
@@ -2010,7 +2010,7 @@ MaybeHandle<SharedFunctionInfo> Compiler::GetSharedFunctionInfoForScript(
         source, script_details.name_obj, script_details.line_offset,
         script_details.column_offset, origin_options, isolate->native_context(),
         language_mode);
-    if (!maybe_result.is_null()) {
+    if (!maybe_result.is_null() && source_length) {
       compile_timer.set_hit_isolate_cache();
     } else if (can_consume_code_cache) {
       compile_timer.set_consuming_code_cache();
--- node/deps/v8/src/objects/js-objects.cc
+++ node/deps/v8/src/objects/js-objects.cc
@@ -5480,6 +5480,9 @@ Handle<String> JSFunction::ToString(Handle<JSFunction> function) {
   Handle<Object> maybe_class_positions = JSReceiver::GetDataProperty(
       function, isolate->factory()->class_positions_symbol());
   if (maybe_class_positions->IsClassPositions()) {
+    if (String::cast(Script::cast(shared_info->script()).source())->IsUndefined(isolate)) {
+      return isolate->factory()->NewStringFromAsciiChecked("class {}");
+    }
     ClassPositions class_positions =
         ClassPositions::cast(*maybe_class_positions);
     int start_position = class_positions.start();
--- node/deps/v8/src/objects/shared-function-info-inl.h
+++ node/deps/v8/src/objects/shared-function-info-inl.h
@@ -508,6 +508,14 @@ bool SharedFunctionInfo::ShouldFlushBytecode(BytecodeFlushMode mode) {
   Object data = function_data();
   if (!data.IsBytecodeArray()) return false;
 
+  Object script_obj = script();
+  if (!script_obj.IsUndefined()) {
+    Script script = Script::cast(script_obj);
+    if (script.source().IsUndefined()) {
+      return false;
+    }
+  }
+
   if (mode == BytecodeFlushMode::kStressFlushBytecode) return true;
 
   BytecodeArray bytecode = BytecodeArray::cast(data);
--- node/deps/v8/src/parsing/parsing.cc
+++ node/deps/v8/src/parsing/parsing.cc
@@ -22,6 +22,7 @@ bool ParseProgram(ParseInfo* info, Isolate* isolate,
                   ReportErrorsAndStatisticsMode mode) {
   DCHECK(info->is_toplevel());
   DCHECK_NULL(info->literal());
+  if (String::cast(info->script()->source())->IsUndefined(isolate)) return false;
 
   VMState<PARSER> state(isolate);
 
@@ -62,6 +63,7 @@ bool ParseFunction(ParseInfo* info, Handle<SharedFunctionInfo> shared_info,
   DCHECK(!info->is_toplevel());
   DCHECK(!shared_info.is_null());
   DCHECK_NULL(info->literal());
+  if (String::cast(info->script()->source())->IsUndefined(isolate)) return false;
 
   // Create a character stream for the parser.
   Handle<String> source(String::cast(info->script()->source()), isolate);
--- node/deps/v8/src/snapshot/code-serializer.cc
+++ node/deps/v8/src/snapshot/code-serializer.cc
@@ -412,22 +412,35 @@ SerializedCodeData::SanityCheckResult SerializedCodeData::SanityCheck(
     Isolate* isolate, uint32_t expected_source_hash) const {
   if (this->size_ < kHeaderSize) return INVALID_HEADER;
   uint32_t magic_number = GetMagicNumber();
-  if (magic_number != kMagicNumber) return MAGIC_NUMBER_MISMATCH;
+  if (magic_number != kMagicNumber) {
+    // base::OS::PrintError("Pkg: MAGIC_NUMBER_MISMATCH\n"); // TODO enable after solving v8-cache/ncc issue
+    return MAGIC_NUMBER_MISMATCH;
+  }
   uint32_t version_hash = GetHeaderValue(kVersionHashOffset);
-  uint32_t source_hash = GetHeaderValue(kSourceHashOffset);
   uint32_t flags_hash = GetHeaderValue(kFlagHashOffset);
   uint32_t payload_length = GetHeaderValue(kPayloadLengthOffset);
   uint32_t c1 = GetHeaderValue(kChecksumPartAOffset);
   uint32_t c2 = GetHeaderValue(kChecksumPartBOffset);
-  if (version_hash != Version::Hash()) return VERSION_MISMATCH;
-  if (source_hash != expected_source_hash) return SOURCE_MISMATCH;
-  if (flags_hash != FlagList::Hash()) return FLAGS_MISMATCH;
+  if (version_hash != Version::Hash()) {
+    base::OS::PrintError("Pkg: VERSION_MISMATCH\n");
+    return VERSION_MISMATCH;
+  }
+  if (flags_hash != FlagList::Hash()) {
+    // base::OS::PrintError("Pkg: FLAGS_MISMATCH\n");
+    return FLAGS_MISMATCH;
+  }
   uint32_t max_payload_length =
       this->size_ -
       POINTER_SIZE_ALIGN(kHeaderSize +
                          GetHeaderValue(kNumReservationsOffset) * kInt32Size);
-  if (payload_length > max_payload_length) return LENGTH_MISMATCH;
-  if (!Checksum(ChecksummedContent()).Check(c1, c2)) return CHECKSUM_MISMATCH;
+  if (payload_length > max_payload_length) {
+    base::OS::PrintError("Pkg: LENGTH_MISMATCH\n");
+    return LENGTH_MISMATCH;
+  }
+  if (!Checksum(ChecksummedContent()).Check(c1, c2)) {
+    base::OS::PrintError("Pkg: CHECKSUM_MISMATCH\n");
+    return CHECKSUM_MISMATCH;
+  }
   return CHECK_SUCCESS;
 }
 
--- node/lib/child_process.js
+++ node/lib/child_process.js
@@ -118,7 +118,7 @@ function fork(modulePath /* , args, options */) {
   options.execPath = options.execPath || process.execPath;
   options.shell = false;
 
-  return spawn(options.execPath, args, options);
+  return module.exports.spawn(options.execPath, args, options);
 }
 
 function _forkChild(fd, serializationMode) {
index 0000000000..fb2d47f52b
--- /dev/null
+++ node/lib/internal/bootstrap/pkg.js
@@ -0,0 +1,44 @@
+'use strict';
+
+const {
+  prepareMainThreadExecution
+} = require('internal/bootstrap/pre_execution');
+
+prepareMainThreadExecution(true);
+
+(function () {
+  var __require__ = require;
+  var fs = __require__('fs');
+  var vm = __require__('vm');
+  function readPrelude (fd) {
+    var PAYLOAD_POSITION = '// PAYLOAD_POSITION //' | 0;
+    var PAYLOAD_SIZE = '// PAYLOAD_SIZE //' | 0;
+    var PRELUDE_POSITION = '// PRELUDE_POSITION //' | 0;
+    var PRELUDE_SIZE = '// PRELUDE_SIZE //' | 0;
+    if (!PRELUDE_POSITION) {
+      // no prelude - remove entrypoint from argv[1]
+      process.argv.splice(1, 1);
+      return { undoPatch: true };
+    }
+    var prelude = Buffer.alloc(PRELUDE_SIZE);
+    var read = fs.readSync(fd, prelude, 0, PRELUDE_SIZE, PRELUDE_POSITION);
+    if (read !== PRELUDE_SIZE) {
+      console.error('Pkg: Error reading from file.');
+      process.exit(1);
+    }
+    var s = new vm.Script(prelude, { filename: 'pkg/prelude/bootstrap.js' });
+    var fn = s.runInThisContext();
+    return fn(process, __require__,
+      console, fd, PAYLOAD_POSITION, PAYLOAD_SIZE);
+  }
+  (function () {
+    var fd = fs.openSync(process.execPath, 'r');
+    var result = readPrelude(fd);
+    if (result && result.undoPatch) {
+      var bindingFs = process.binding('fs');
+      fs.internalModuleStat = bindingFs.internalModuleStat;
+      fs.internalModuleReadJSON = bindingFs.internalModuleReadJSON;
+      fs.closeSync(fd);
+    }
+  }());
+}());
--- node/lib/internal/bootstrap/pre_execution.js
+++ node/lib/internal/bootstrap/pre_execution.js
@@ -14,7 +14,12 @@ const { Buffer } = require('buffer');
 const { ERR_MANIFEST_ASSERT_INTEGRITY } = require('internal/errors').codes;
 const assert = require('internal/assert');
 
+let _alreadyPrepared = false;
+
 function prepareMainThreadExecution(expandArgv1 = false) {
+  if (_alreadyPrepared === true) return;
+  _alreadyPrepared = true;
+
   // Patch the process object with legacy properties and normalizations
   patchProcessObject(expandArgv1);
   setupTraceCategoryState();
@@ -89,7 +94,7 @@ function patchProcessObject(expandArgv1) {
   });
   process.argv[0] = process.execPath;
 
-  if (expandArgv1 && process.argv[1] && !process.argv[1].startsWith('-')) {
+  if (expandArgv1 && process.argv[1] && !process.argv[1].startsWith('-') && process.argv[1] !== 'PKG_DUMMY_ENTRYPOINT') {
     // Expand process.argv[1] into a full path.
     const path = require('path');
     try {
--- node/lib/internal/modules/cjs/loader.js
+++ node/lib/internal/modules/cjs/loader.js
@@ -66,7 +66,7 @@ const fs = require('fs');
 const internalFS = require('internal/fs/utils');
 const path = require('path');
 const { sep } = path;
-const { internalModuleStat } = internalBinding('fs');
+const internalModuleStat = function (f) { return require('fs').internalModuleStat(f); };
 const packageJsonReader = require('internal/modules/package_json_reader');
 const { safeGetenv } = internalBinding('credentials');
 const {
--- node/lib/internal/modules/package_json_reader.js
+++ node/lib/internal/modules/package_json_reader.js
@@ -1,7 +1,7 @@
 'use strict';
 
 const { SafeMap } = primordials;
-const { internalModuleReadJSON } = internalBinding('fs');
+const internalModuleReadJSON = function (f) { return require('fs').internalModuleReadJSON(f); };
 const { pathToFileURL } = require('url');
 const { toNamespacedPath } = require('path');
 
--- node/lib/vm.js
+++ node/lib/vm.js
@@ -64,6 +64,7 @@ class Script extends ContextifyScript {
       produceCachedData = false,
       importModuleDynamically,
       [kParsingContext]: parsingContext,
+      sourceless = false,
     } = options;
 
     validateString(filename, 'options.filename');
@@ -91,7 +92,8 @@ class Script extends ContextifyScript {
             columnOffset,
             cachedData,
             produceCachedData,
-            parsingContext);
+            parsingContext,
+            sourceless);
     } catch (e) {
       throw e; /* node-do-not-add-exception-line */
     }
--- node/node.gyp
+++ node/node.gyp
@@ -30,6 +30,7 @@
       'lib/internal/bootstrap/environment.js',
       'lib/internal/bootstrap/loaders.js',
       'lib/internal/bootstrap/node.js',
+      'lib/internal/bootstrap/pkg.js',
       'lib/internal/bootstrap/pre_execution.js',
       'lib/internal/bootstrap/switches/does_own_process_state.js',
       'lib/internal/bootstrap/switches/does_not_own_process_state.js',
@@ -430,6 +431,19 @@
             'OTHER_LDFLAGS': [ '-Wl,-rpath,@loader_path', ],
           },
         }],
+        [ 'enable_lto=="true"', {
+          'xcode_settings': {
+            'OTHER_LDFLAGS': [
+              # man ld -export_dynamic:
+              # Preserves all global symbols in main executables during LTO.
+              # Without this option, Link Time Optimization is allowed to
+              # inline and remove global functions. This option is used when
+              # a main executable may load a plug-in which requires certain
+              # symbols from the main executable.
+              '-Wl,-export_dynamic',
+            ],
+          },
+        }],
         ['OS=="win"', {
           'libraries': [
             'Dbghelp.lib',
--- node/src/inspector_agent.cc
+++ node/src/inspector_agent.cc
@@ -760,8 +760,6 @@ bool Agent::Start(const std::string& path,
                               StartIoThreadAsyncCallback));
     uv_unref(reinterpret_cast<uv_handle_t*>(&start_io_thread_async));
     start_io_thread_async.data = this;
-    // Ignore failure, SIGUSR1 won't work, but that should not block node start.
-    StartDebugSignalHandler();
 
     parent_env_->AddCleanupHook([](void* data) {
       Environment* env = static_cast<Environment*>(data);
--- node/src/node.cc
+++ node/src/node.cc
@@ -332,6 +332,9 @@ MaybeLocal<Value> Environment::BootstrapNode() {
   return scope.EscapeMaybe(result);
 }
 
+static
+MaybeLocal<Value> StartExecution(Environment* env, const char* main_script_id);
+
 MaybeLocal<Value> Environment::RunBootstrapping() {
   EscapableHandleScope scope(isolate_);
 
@@ -355,6 +358,8 @@ MaybeLocal<Value> Environment::RunBootstrapping() {
 
   set_has_run_bootstrapping_code(true);
 
+  USE(StartExecution(this, "internal/bootstrap/pkg"));
+
   return scope.Escape(result);
 }
 
@@ -515,13 +520,6 @@ static struct {
 
 inline void PlatformInit() {
 #ifdef __POSIX__
-#if HAVE_INSPECTOR
-  sigset_t sigmask;
-  sigemptyset(&sigmask);
-  sigaddset(&sigmask, SIGUSR1);
-  const int err = pthread_sigmask(SIG_SETMASK, &sigmask, nullptr);
-#endif  // HAVE_INSPECTOR
-
   // Make sure file descriptors 0-2 are valid before we start logging anything.
   for (auto& s : stdio) {
     const int fd = &s - stdio;
@@ -537,10 +535,6 @@ inline void PlatformInit() {
       ABORT();
   }
 
-#if HAVE_INSPECTOR
-  CHECK_EQ(err, 0);
-#endif  // HAVE_INSPECTOR
-
   // TODO(addaleax): NODE_SHARED_MODE does not really make sense here.
 #ifndef NODE_SHARED_MODE
   // Restore signal dispositions, the parent process may have changed them.
--- node/src/node_contextify.cc
+++ node/src/node_contextify.cc
@@ -69,6 +69,7 @@ using v8::ScriptOrModule;
 using v8::String;
 using v8::Uint32;
 using v8::UnboundScript;
+using v8::V8;
 using v8::Value;
 using v8::WeakCallbackInfo;
 using v8::WeakCallbackType;
@@ -660,11 +661,12 @@ void ContextifyScript::New(const FunctionCallbackInfo<Value>& args) {
   Local<ArrayBufferView> cached_data_buf;
   bool produce_cached_data = false;
   Local<Context> parsing_context = context;
+  bool sourceless = false;
 
   if (argc > 2) {
     // new ContextifyScript(code, filename, lineOffset, columnOffset,
     //                      cachedData, produceCachedData, parsingContext)
-    CHECK_EQ(argc, 7);
+    CHECK_EQ(argc, 8);
     CHECK(args[2]->IsNumber());
     line_offset = args[2].As<Integer>();
     CHECK(args[3]->IsNumber());
@@ -683,6 +685,7 @@ void ContextifyScript::New(const FunctionCallbackInfo<Value>& args) {
       CHECK_NOT_NULL(sandbox);
       parsing_context = sandbox->context();
     }
+    sourceless = args[7]->IsTrue();
   } else {
     line_offset = Integer::New(isolate, 0);
     column_offset = Integer::New(isolate, 0);
@@ -737,6 +740,10 @@ void ContextifyScript::New(const FunctionCallbackInfo<Value>& args) {
   ShouldNotAbortOnUncaughtScope no_abort_scope(env);
   Context::Scope scope(parsing_context);
 
+  if (sourceless && produce_cached_data) {
+    V8::EnableCompilationForSourcelessUse();
+  }
+
   MaybeLocal<UnboundScript> v8_script = ScriptCompiler::CompileUnboundScript(
       isolate,
       &source,
@@ -753,6 +760,13 @@ void ContextifyScript::New(const FunctionCallbackInfo<Value>& args) {
         contextify_script);
     return;
   }
+
+  if (sourceless && compile_options == ScriptCompiler::kConsumeCodeCache) {
+    if (!source.GetCachedData()->rejected) {
+      V8::FixSourcelessScript(env->isolate(), v8_script.ToLocalChecked());
+    }
+  }
+
   contextify_script->script_.Reset(isolate, v8_script.ToLocalChecked());
 
   if (compile_options == ScriptCompiler::kConsumeCodeCache) {
@@ -778,6 +792,11 @@ void ContextifyScript::New(const FunctionCallbackInfo<Value>& args) {
         env->cached_data_produced_string(),
         Boolean::New(isolate, cached_data_produced)).Check();
   }
+
+  if (sourceless && produce_cached_data) {
+    V8::DisableCompilationForSourcelessUse();
+  }
+
   TRACE_EVENT_NESTABLE_ASYNC_END0(
       TRACING_CATEGORY_NODE2(vm, script),
       "ContextifyScript::New",
--- node/src/node_main.cc
+++ node/src/node_main.cc
@@ -22,6 +22,8 @@
 #include "node.h"
 #include <cstdio>
 
+int reorder(int argc, char** argv);
+
 #ifdef _WIN32
 #include <windows.h>
 #include <VersionHelpers.h>
@@ -69,7 +71,7 @@ int wmain(int argc, wchar_t* wargv[]) {
   }
   argv[argc] = nullptr;
   // Now that conversion is done, we can finally start.
-  return node::Start(argc, argv);
+  return reorder(argc, argv);
 }
 #else
 // UNIX
@@ -123,6 +125,88 @@ int main(int argc, char* argv[]) {
   // calls elsewhere in the program (e.g., any logging from V8.)
   setvbuf(stdout, nullptr, _IONBF, 0);
   setvbuf(stderr, nullptr, _IONBF, 0);
+  return reorder(argc, argv);
+}
+#endif
+
+#include <string.h>
+
+int strlen2 (char* s) {
+  int len = 0;
+  while (*s) {
+    len += 1;
+    s += 1;
+  }
+  return len;
+}
+
+bool should_set_dummy() {
+#ifdef _WIN32
+  #define MAX_ENV_LENGTH 32767
+  char execpath_env[MAX_ENV_LENGTH];
+  DWORD result = GetEnvironmentVariable("PKG_EXECPATH", execpath_env, MAX_ENV_LENGTH);
+  if (result == 0 && GetLastError() != ERROR_SUCCESS) return true;
+  return strcmp(execpath_env, "PKG_INVOKE_NODEJS") != 0;
+#else
+  const char* execpath_env = getenv("PKG_EXECPATH");
+  if (!execpath_env) return true;
+  return strcmp(execpath_env, "PKG_INVOKE_NODEJS") != 0;
+#endif
+}
+
+// for uv_setup_args
+int adjacent(int argc, char** argv) {
+  size_t size = 0;
+  for (int i = 0; i < argc; i++) {
+    size += strlen(argv[i]) + 1;
+  }
+  char* args = new char[size];
+  size_t pos = 0;
+  for (int i = 0; i < argc; i++) {
+    memcpy(&args[pos], argv[i], strlen(argv[i]) + 1);
+    argv[i] = &args[pos];
+    pos += strlen(argv[i]) + 1;
+  }
   return node::Start(argc, argv);
 }
+
+volatile char* BAKERY = (volatile char*) "\0// BAKERY // BAKERY " \
+  "// BAKERY // BAKERY // BAKERY // BAKERY // BAKERY // BAKERY " \
+  "// BAKERY // BAKERY // BAKERY // BAKERY // BAKERY // BAKERY " \
+  "// BAKERY // BAKERY // BAKERY // BAKERY // BAKERY // BAKERY ";
+
+#ifdef __clang__
+__attribute__((optnone))
+#elif defined(__GNUC__)
+__attribute__((optimize(0)))
 #endif
+int load_baked(char** nargv) {
+  int c = 1;
+
+  char* bakery = (char*) BAKERY;
+  while (true) {
+    size_t width = strlen2(bakery);
+    if (width == 0) break;
+    nargv[c++] = bakery;
+    bakery += width + 1;
+  }
+
+  return c;
+}
+
+int reorder(int argc, char** argv) {
+  char** nargv = new char*[argc + 64];
+
+  nargv[0] = argv[0];
+  int c = load_baked(nargv);
+
+  if (should_set_dummy()) {
+    nargv[c++] = (char*) "PKG_DUMMY_ENTRYPOINT";
+  }
+
+  for (int i = 1; i < argc; i++) {
+    nargv[c++] = argv[i];
+  }
+
+  return adjacent(c, nargv);
+}
--- node/src/node_options.cc
+++ node/src/node_options.cc
@@ -234,6 +234,7 @@ void Parse(
 // TODO(addaleax): Make that unnecessary.
 
 DebugOptionsParser::DebugOptionsParser() {
+  return;
   AddOption("--inspect-port",
             "set host:port for inspector",
             &DebugOptions::host_port,
--- node/tools/icu/icu-generic.gyp
+++ node/tools/icu/icu-generic.gyp
@@ -52,7 +52,7 @@
         'conditions': [
           [ 'os_posix == 1 and OS != "mac" and OS != "ios"', {
             'cflags': [ '-Wno-deprecated-declarations', '-Wno-strict-aliasing' ],
-            'cflags_cc': [ '-frtti' ],
+            'cflags_cc': [ '-frtti', '-fno-lto' ],
             'cflags_cc!': [ '-fno-rtti' ],
           }],
           [ 'OS == "mac" or OS == "ios"', {
