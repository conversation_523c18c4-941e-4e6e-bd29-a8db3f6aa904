<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件权限管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 1.1rem;
            margin: 10px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .features {
            text-align: left;
            margin-top: 40px;
        }
        .feature {
            margin: 15px 0;
            font-size: 1rem;
        }
        .feature i {
            margin-right: 10px;
            color: #fff;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-shield-alt"></i> 软件权限管理系统</h1>
        <p>完整的软件许可证管理解决方案</p>
        
        <a href="/user" class="btn">
            <i class="fas fa-user"></i> 用户中心
        </a>
        
        <a href="/admin" class="btn">
            <i class="fas fa-tachometer-alt"></i> 管理后台
        </a>
        
        <a href="/api/docs" class="btn">
            <i class="fas fa-book"></i> API 文档
        </a>

        <div class="features">
            <h3><i class="fas fa-star"></i> 主要功能</h3>
            <div class="feature">
                <i class="fas fa-users"></i> 用户管理 - 完整的用户增删改查功能
            </div>
            <div class="feature">
                <i class="fas fa-box"></i> 软件管理 - 软件产品和密钥管理
            </div>
            <div class="feature">
                <i class="fas fa-key"></i> 许可证管理 - 生成、激活、撤销许可证
            </div>
            <div class="feature">
                <i class="fas fa-chart-bar"></i> 数据统计 - 实时仪表板和统计
            </div>
            <div class="feature">
                <i class="fas fa-list"></i> 操作日志 - 完整的操作记录追踪
            </div>
            <div class="feature">
                <i class="fas fa-shield-alt"></i> 安全认证 - JWT令牌和角色权限
            </div>
        </div>

        <div style="margin-top: 40px; font-size: 0.9rem; opacity: 0.8;">
            <p>🔐 默认管理员账户</p>
            <p>用户名: <strong>admin</strong> | 密码: <strong>admin123</strong></p>
        </div>
    </div>

    <script>
        // 自动检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('/health');
                if (response.ok) {
                    console.log('服务器状态正常');
                }
            } catch (error) {
                console.warn('服务器连接检查失败');
            }
        }
        
        checkServerStatus();
    </script>
</body>
</html> 