export declare class CryptoUtils {
    private static readonly ALGORITHM;
    private static readonly IV_LENGTH;
    /**
     * 生成随机许可证密钥
     */
    static generateLicenseKey(): string;
    /**
     * 生成产品密钥
     */
    static generateProductKey(): string;
    /**
     * 加密数据
     */
    static encrypt(text: string, key: string): string;
    /**
     * 解密数据
     */
    static decrypt(encryptedData: string, key: string): string;
    /**
     * 生成哈希值
     */
    static hash(data: string): string;
    /**
     * 验证哈希值
     */
    static verifyHash(data: string, hash: string): boolean;
    /**
     * 生成JWT密钥
     */
    static generateJWTSecret(): string;
}
//# sourceMappingURL=crypto.d.ts.map