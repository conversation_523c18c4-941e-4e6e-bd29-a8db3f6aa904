"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireManagerOrAdmin = exports.requireAdmin = exports.authenticateToken = void 0;
const UserService_1 = require("../services/UserService");
/**
 * JWT认证中间件
 */
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    if (!token) {
        res.status(401).json({
            success: false,
            message: '访问令牌缺失'
        });
        return;
    }
    try {
        const decoded = UserService_1.UserService.verifyToken(token);
        req.user = decoded;
        next();
    }
    catch (error) {
        res.status(403).json({
            success: false,
            message: '无效的访问令牌'
        });
    }
};
exports.authenticateToken = authenticateToken;
/**
 * 管理员权限验证中间件
 */
const requireAdmin = (req, res, next) => {
    if (!req.user) {
        res.status(401).json({
            success: false,
            message: '未认证的用户'
        });
        return;
    }
    if (!req.user.roles || !req.user.roles.includes('admin')) {
        res.status(403).json({
            success: false,
            message: '需要管理员权限'
        });
        return;
    }
    next();
};
exports.requireAdmin = requireAdmin;
/**
 * 管理员或经理权限验证中间件
 */
const requireManagerOrAdmin = (req, res, next) => {
    if (!req.user) {
        res.status(401).json({
            success: false,
            message: '未认证的用户'
        });
        return;
    }
    const allowedRoles = ['admin', 'manager'];
    const hasPermission = req.user.roles && req.user.roles.some((role) => allowedRoles.includes(role));
    if (!hasPermission) {
        res.status(403).json({
            success: false,
            message: '权限不足'
        });
        return;
    }
    next();
};
exports.requireManagerOrAdmin = requireManagerOrAdmin;
//# sourceMappingURL=auth.js.map