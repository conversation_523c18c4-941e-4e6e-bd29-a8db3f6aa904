"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
// 简化的用户服务类
class UserService {
    /**
     * 哈希密码
     */
    static async hashPassword(password) {
        return await bcrypt_1.default.hash(password, UserService.BCRYPT_ROUNDS);
    }
    /**
     * 验证密码
     */
    static async verifyPassword(password, hash) {
        return await bcrypt_1.default.compare(password, hash);
    }
    /**
     * 生成JWT令牌
     */
    static generateToken(payload) {
        return jsonwebtoken_1.default.sign(payload, UserService.JWT_SECRET, {
            expiresIn: UserService.JWT_EXPIRES_IN
        });
    }
    /**
     * 验证JWT令牌
     */
    static verifyToken(token) {
        try {
            return jsonwebtoken_1.default.verify(token, UserService.JWT_SECRET);
        }
        catch (error) {
            throw new Error('令牌无效');
        }
    }
    /**
     * 生成数字签名
     */
    static generateSignature(data) {
        return crypto_1.default
            .createHmac('sha256', UserService.SIGNATURE_SECRET)
            .update(data)
            .digest('hex');
    }
    /**
     * 验证数字签名
     */
    static verifySignature(data, signature) {
        const expectedSignature = UserService.generateSignature(data);
        return crypto_1.default.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
    }
    /**
     * 生成用户登录响应
     */
    static createLoginResponse(success, data, message) {
        return {
            success,
            ...(data && { token: data.token, user: data.user }),
            ...(message && { message }),
        };
    }
}
exports.UserService = UserService;
UserService.JWT_SECRET = process.env.JWT_SECRET || 'default-secret';
UserService.JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
UserService.BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');
UserService.SIGNATURE_SECRET = process.env.SIGNATURE_SECRET || 'license-signature-secret';
//# sourceMappingURL=UserService.js.map