import { Model, Table, Column, DataType, HasMany } from 'sequelize-typescript';
import { License } from './License';

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends Model<User> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
    },
  })
  username!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  passwordHash!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
    },
  })
  email!: string;

  @Column({
    type: DataType.JSON,
    defaultValue: ['user'],
  })
  roles!: string[];

  @HasMany(() => License)
  licenses!: License[];
} 