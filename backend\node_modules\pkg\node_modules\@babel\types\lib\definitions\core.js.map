{"version": 3, "names": ["defineType", "defineAliasedType", "fields", "elements", "validate", "chain", "assertValueType", "assertEach", "assertNodeOrValueType", "default", "process", "env", "BABEL_TYPES_8_BREAKING", "undefined", "visitor", "aliases", "operator", "identifier", "assertOneOf", "ASSIGNMENT_OPERATORS", "pattern", "node", "key", "val", "validator", "is", "left", "assertNodeType", "right", "builder", "BINARY_OPERATORS", "expression", "inOp", "Object", "assign", "oneOfNodeTypes", "value", "directives", "body", "label", "optional", "callee", "arguments", "typeArguments", "typeParameters", "param", "test", "consequent", "alternate", "program", "comments", "each", "tokens", "type", "init", "update", "functionCommon", "params", "generator", "async", "functionTypeAnnotationCommon", "returnType", "functionDeclaration<PERSON>ommon", "declare", "id", "predicate", "parent", "inherits", "patternLikeCommon", "typeAnnotation", "decorators", "name", "isValidIdentifier", "TypeError", "match", "exec", "parent<PERSON><PERSON>", "nonComp", "computed", "imported", "meta", "isKeyword", "isReservedWord", "depre<PERSON><PERSON><PERSON><PERSON>", "flags", "invalid", "LOGICAL_OPERATORS", "object", "property", "normal", "sourceFile", "sourceType", "interpreter", "properties", "kind", "shorthand", "argument", "Error", "<PERSON><PERSON><PERSON>", "index", "length", "expressions", "discriminant", "cases", "block", "handler", "finalizer", "prefix", "UNARY_OPERATORS", "UPDATE_OPERATORS", "declarations", "without", "definite", "superClass", "superTypeParameters", "implements", "mixins", "abstract", "source", "exportKind", "validateOptional", "assertions", "declaration", "specifiers", "sourced", "sourceless", "local", "exported", "lval", "await", "importKind", "classMethodOrPropertyCommon", "accessibility", "static", "override", "classMethodOrDeclareMethodCommon", "access", "tag", "quasi", "assertShape", "raw", "cooked", "templateElementCookedValidator", "str", "containsInvalid", "unterminatedCalled", "error", "readStringContents", "unterminated", "strictNumericEscape", "invalidEscapeSequence", "numericSeparatorInEscapeSequence", "unexpectedNumericSeparator", "invalidDigit", "invalidCodePoint", "tail", "quasis", "delegate", "assertOptionalChainStart", "readonly", "variance"], "sources": ["../../src/definitions/core.ts"], "sourcesContent": ["import is from \"../validators/is\";\nimport isValidIdentifier from \"../validators/isValidIdentifier\";\nimport { isKeyword, isReservedWord } from \"@babel/helper-validator-identifier\";\nimport type * as t from \"..\";\nimport { readStringContents } from \"@babel/helper-string-parser\";\n\nimport {\n  BINARY_OPERATORS,\n  LOGICAL_OPERATORS,\n  ASSIGNMENT_OPERATORS,\n  UNARY_OPERATORS,\n  UPDATE_OPERATORS,\n} from \"../constants\";\n\nimport {\n  defineAliasedType,\n  assertShape,\n  assertOptionalChainStart,\n  assertValueType,\n  assertNodeType,\n  assertNodeOrValueType,\n  assertEach,\n  chain,\n  assertOneOf,\n  validateOptional,\n  type Validator,\n} from \"./utils\";\n\nconst defineType = defineAliasedType(\"Standardized\");\n\ndefineType(\"ArrayExpression\", {\n  fields: {\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeOrValueType(\"null\", \"Expression\", \"SpreadElement\"),\n        ),\n      ),\n      default: !process.env.BABEL_TYPES_8_BREAKING ? [] : undefined,\n    },\n  },\n  visitor: [\"elements\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"AssignmentExpression\", {\n  fields: {\n    operator: {\n      validate: (function () {\n        if (!process.env.BABEL_TYPES_8_BREAKING) {\n          return assertValueType(\"string\");\n        }\n\n        const identifier = assertOneOf(...ASSIGNMENT_OPERATORS);\n        const pattern = assertOneOf(\"=\");\n\n        return function (node: t.AssignmentExpression, key, val) {\n          const validator = is(\"Pattern\", node.left) ? pattern : identifier;\n          validator(node, key, val);\n        };\n      })(),\n    },\n    left: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"LVal\")\n        : assertNodeType(\n            \"Identifier\",\n            \"MemberExpression\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"TSAsExpression\",\n            \"TSTypeAssertion\",\n            \"TSNonNullExpression\",\n          ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  builder: [\"operator\", \"left\", \"right\"],\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"BinaryExpression\", {\n  builder: [\"operator\", \"left\", \"right\"],\n  fields: {\n    operator: {\n      validate: assertOneOf(...BINARY_OPERATORS),\n    },\n    left: {\n      validate: (function () {\n        const expression = assertNodeType(\"Expression\");\n        const inOp = assertNodeType(\"Expression\", \"PrivateName\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.BinaryExpression, key, val) {\n            const validator = node.operator === \"in\" ? inOp : expression;\n            validator(node, key, val);\n          } as Validator,\n          // todo(ts): can be discriminated union by `operator` property\n          { oneOfNodeTypes: [\"Expression\", \"PrivateName\"] },\n        );\n        return validator;\n      })(),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Binary\", \"Expression\"],\n});\n\ndefineType(\"InterpreterDirective\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"Directive\", {\n  visitor: [\"value\"],\n  fields: {\n    value: {\n      validate: assertNodeType(\"DirectiveLiteral\"),\n    },\n  },\n});\n\ndefineType(\"DirectiveLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"BlockStatement\", {\n  builder: [\"body\", \"directives\"],\n  visitor: [\"directives\", \"body\"],\n  fields: {\n    directives: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Directive\")),\n      ),\n      default: [],\n    },\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"Block\", \"Statement\"],\n});\n\ndefineType(\"BreakStatement\", {\n  visitor: [\"label\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n  },\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n});\n\ndefineType(\"CallExpression\", {\n  visitor: [\"callee\", \"arguments\", \"typeParameters\", \"typeArguments\"],\n  builder: [\"callee\", \"arguments\"],\n  aliases: [\"Expression\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\", \"Super\", \"V8IntrinsicIdentifier\"),\n    },\n    arguments: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            \"SpreadElement\",\n            \"JSXNamespacedName\",\n            \"ArgumentPlaceholder\",\n          ),\n        ),\n      ),\n    },\n    ...(!process.env.BABEL_TYPES_8_BREAKING\n      ? {\n          optional: {\n            validate: assertOneOf(true, false),\n            optional: true,\n          },\n        }\n      : {}),\n    typeArguments: {\n      validate: assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    typeParameters: {\n      validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"CatchClause\", {\n  visitor: [\"param\", \"body\"],\n  fields: {\n    param: {\n      validate: assertNodeType(\"Identifier\", \"ArrayPattern\", \"ObjectPattern\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\"],\n});\n\ndefineType(\"ConditionalExpression\", {\n  visitor: [\"test\", \"consequent\", \"alternate\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    consequent: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    alternate: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\", \"Conditional\"],\n});\n\ndefineType(\"ContinueStatement\", {\n  visitor: [\"label\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n  },\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n});\n\ndefineType(\"DebuggerStatement\", {\n  aliases: [\"Statement\"],\n});\n\ndefineType(\"DoWhileStatement\", {\n  visitor: [\"test\", \"body\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n  aliases: [\"Statement\", \"BlockParent\", \"Loop\", \"While\", \"Scopable\"],\n});\n\ndefineType(\"EmptyStatement\", {\n  aliases: [\"Statement\"],\n});\n\ndefineType(\"ExpressionStatement\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Statement\", \"ExpressionWrapper\"],\n});\n\ndefineType(\"File\", {\n  builder: [\"program\", \"comments\", \"tokens\"],\n  visitor: [\"program\"],\n  fields: {\n    program: {\n      validate: assertNodeType(\"Program\"),\n    },\n    comments: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? Object.assign(() => {}, {\n            each: { oneOfNodeTypes: [\"CommentBlock\", \"CommentLine\"] },\n          })\n        : assertEach(assertNodeType(\"CommentBlock\", \"CommentLine\")),\n      optional: true,\n    },\n    tokens: {\n      // todo(ts): add Token type\n      validate: assertEach(Object.assign(() => {}, { type: \"any\" })),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ForInStatement\", {\n  visitor: [\"left\", \"right\", \"body\"],\n  aliases: [\n    \"Scopable\",\n    \"Statement\",\n    \"For\",\n    \"BlockParent\",\n    \"Loop\",\n    \"ForXStatement\",\n  ],\n  fields: {\n    left: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"VariableDeclaration\", \"LVal\")\n        : assertNodeType(\n            \"VariableDeclaration\",\n            \"Identifier\",\n            \"MemberExpression\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"TSAsExpression\",\n            \"TSTypeAssertion\",\n            \"TSNonNullExpression\",\n          ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"ForStatement\", {\n  visitor: [\"init\", \"test\", \"update\", \"body\"],\n  aliases: [\"Scopable\", \"Statement\", \"For\", \"BlockParent\", \"Loop\"],\n  fields: {\n    init: {\n      validate: assertNodeType(\"VariableDeclaration\", \"Expression\"),\n      optional: true,\n    },\n    test: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    update: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\nexport const functionCommon = () => ({\n  params: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(assertNodeType(\"Identifier\", \"Pattern\", \"RestElement\")),\n    ),\n  },\n  generator: {\n    default: false,\n  },\n  async: {\n    default: false,\n  },\n});\n\nexport const functionTypeAnnotationCommon = () => ({\n  returnType: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n      : assertNodeType(\n          \"TypeAnnotation\",\n          \"TSTypeAnnotation\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n  typeParameters: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeParameterDeclaration\", \"TSTypeParameterDeclaration\")\n      : assertNodeType(\n          \"TypeParameterDeclaration\",\n          \"TSTypeParameterDeclaration\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n});\n\nexport const functionDeclarationCommon = () => ({\n  ...functionCommon(),\n  declare: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  id: {\n    validate: assertNodeType(\"Identifier\"),\n    optional: true, // May be null for `export default function`\n  },\n});\n\ndefineType(\"FunctionDeclaration\", {\n  builder: [\"id\", \"params\", \"body\", \"generator\", \"async\"],\n  visitor: [\"id\", \"params\", \"body\", \"returnType\", \"typeParameters\"],\n  fields: {\n    ...functionDeclarationCommon(),\n    ...functionTypeAnnotationCommon(),\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Statement\",\n    \"Pureish\",\n    \"Declaration\",\n  ],\n  validate: (function () {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return () => {};\n\n    const identifier = assertNodeType(\"Identifier\");\n\n    return function (parent, key, node) {\n      if (!is(\"ExportDefaultDeclaration\", parent)) {\n        identifier(node, \"id\", node.id);\n      }\n    };\n  })(),\n});\n\ndefineType(\"FunctionExpression\", {\n  inherits: \"FunctionDeclaration\",\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Expression\",\n    \"Pureish\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n});\n\nexport const patternLikeCommon = () => ({\n  typeAnnotation: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n      : assertNodeType(\n          \"TypeAnnotation\",\n          \"TSTypeAnnotation\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n  decorators: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(assertNodeType(\"Decorator\")),\n    ),\n    optional: true,\n  },\n});\n\ndefineType(\"Identifier\", {\n  builder: [\"name\"],\n  visitor: [\"typeAnnotation\", \"decorators\" /* for legacy param decorators */],\n  aliases: [\"Expression\", \"PatternLike\", \"LVal\", \"TSEntityName\"],\n  fields: {\n    ...patternLikeCommon(),\n    name: {\n      validate: chain(\n        assertValueType(\"string\"),\n        Object.assign(\n          function (node, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            if (!isValidIdentifier(val, false)) {\n              throw new TypeError(`\"${val}\" is not a valid identifier name`);\n            }\n          } as Validator,\n          { type: \"string\" },\n        ),\n      ),\n    },\n    optional: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n  },\n  validate(parent, key, node) {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n    const match = /\\.(\\w+)$/.exec(key);\n    if (!match) return;\n\n    const [, parentKey] = match;\n    const nonComp = { computed: false };\n\n    // We can't check if `parent.property === node`, because nodes are validated\n    // before replacing them in the AST.\n    if (parentKey === \"property\") {\n      if (is(\"MemberExpression\", parent, nonComp)) return;\n      if (is(\"OptionalMemberExpression\", parent, nonComp)) return;\n    } else if (parentKey === \"key\") {\n      if (is(\"Property\", parent, nonComp)) return;\n      if (is(\"Method\", parent, nonComp)) return;\n    } else if (parentKey === \"exported\") {\n      if (is(\"ExportSpecifier\", parent)) return;\n    } else if (parentKey === \"imported\") {\n      if (is(\"ImportSpecifier\", parent, { imported: node })) return;\n    } else if (parentKey === \"meta\") {\n      if (is(\"MetaProperty\", parent, { meta: node })) return;\n    }\n\n    if (\n      // Ideally we should call isStrictReservedWord if this node is a descendant\n      // of a block in strict mode. Also, we should pass the inModule option so\n      // we can disable \"await\" in module.\n      (isKeyword(node.name) || isReservedWord(node.name, false)) &&\n      // Even if \"this\" is a keyword, we are using the Identifier\n      // node to represent it.\n      node.name !== \"this\"\n    ) {\n      throw new TypeError(`\"${node.name}\" is not a valid identifier`);\n    }\n  },\n});\n\ndefineType(\"IfStatement\", {\n  visitor: [\"test\", \"consequent\", \"alternate\"],\n  aliases: [\"Statement\", \"Conditional\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    consequent: {\n      validate: assertNodeType(\"Statement\"),\n    },\n    alternate: {\n      optional: true,\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"LabeledStatement\", {\n  visitor: [\"label\", \"body\"],\n  aliases: [\"Statement\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"StringLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"NumericLiteral\", {\n  builder: [\"value\"],\n  deprecatedAlias: \"NumberLiteral\",\n  fields: {\n    value: {\n      validate: assertValueType(\"number\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"NullLiteral\", {\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"BooleanLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"boolean\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"RegExpLiteral\", {\n  builder: [\"pattern\", \"flags\"],\n  deprecatedAlias: \"RegexLiteral\",\n  aliases: [\"Expression\", \"Pureish\", \"Literal\"],\n  fields: {\n    pattern: {\n      validate: assertValueType(\"string\"),\n    },\n    flags: {\n      validate: chain(\n        assertValueType(\"string\"),\n        Object.assign(\n          function (node, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            const invalid = /[^gimsuy]/.exec(val);\n            if (invalid) {\n              throw new TypeError(`\"${invalid[0]}\" is not a valid RegExp flag`);\n            }\n          } as Validator,\n          { type: \"string\" },\n        ),\n      ),\n      default: \"\",\n    },\n  },\n});\n\ndefineType(\"LogicalExpression\", {\n  builder: [\"operator\", \"left\", \"right\"],\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Binary\", \"Expression\"],\n  fields: {\n    operator: {\n      validate: assertOneOf(...LOGICAL_OPERATORS),\n    },\n    left: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"MemberExpression\", {\n  builder: [\n    \"object\",\n    \"property\",\n    \"computed\",\n    ...(!process.env.BABEL_TYPES_8_BREAKING ? [\"optional\"] : []),\n  ],\n  visitor: [\"object\", \"property\"],\n  aliases: [\"Expression\", \"LVal\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\", \"Super\"),\n    },\n    property: {\n      validate: (function () {\n        const normal = assertNodeType(\"Identifier\", \"PrivateName\");\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = function (\n          node: t.MemberExpression,\n          key,\n          val,\n        ) {\n          const validator: Validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n        // @ts-expect-error todo(ts): can be discriminated union by `computed` property\n        validator.oneOfNodeTypes = [\"Expression\", \"Identifier\", \"PrivateName\"];\n        return validator;\n      })(),\n    },\n    computed: {\n      default: false,\n    },\n    ...(!process.env.BABEL_TYPES_8_BREAKING\n      ? {\n          optional: {\n            validate: assertOneOf(true, false),\n            optional: true,\n          },\n        }\n      : {}),\n  },\n});\n\ndefineType(\"NewExpression\", { inherits: \"CallExpression\" });\n\ndefineType(\"Program\", {\n  // Note: We explicitly leave 'interpreter' out here because it is\n  // conceptually comment-like, and Babel does not traverse comments either.\n  visitor: [\"directives\", \"body\"],\n  builder: [\"body\", \"directives\", \"sourceType\", \"interpreter\"],\n  fields: {\n    sourceFile: {\n      validate: assertValueType(\"string\"),\n    },\n    sourceType: {\n      validate: assertOneOf(\"script\", \"module\"),\n      default: \"script\",\n    },\n    interpreter: {\n      validate: assertNodeType(\"InterpreterDirective\"),\n      default: null,\n      optional: true,\n    },\n    directives: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Directive\")),\n      ),\n      default: [],\n    },\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"Block\"],\n});\n\ndefineType(\"ObjectExpression\", {\n  visitor: [\"properties\"],\n  aliases: [\"Expression\"],\n  fields: {\n    properties: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\"ObjectMethod\", \"ObjectProperty\", \"SpreadElement\"),\n        ),\n      ),\n    },\n  },\n});\n\ndefineType(\"ObjectMethod\", {\n  builder: [\"kind\", \"key\", \"params\", \"body\", \"computed\", \"generator\", \"async\"],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    kind: {\n      validate: assertOneOf(\"method\", \"get\", \"set\"),\n      ...(!process.env.BABEL_TYPES_8_BREAKING ? { default: \"method\" } : {}),\n    },\n    computed: {\n      default: false,\n    },\n    key: {\n      validate: (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = function (node: t.ObjectMethod, key, val) {\n          const validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n        // @ts-expect-error todo(ts): can be discriminated union by `computed` property\n        validator.oneOfNodeTypes = [\n          \"Expression\",\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        ];\n        return validator;\n      })(),\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n  visitor: [\n    \"key\",\n    \"params\",\n    \"body\",\n    \"decorators\",\n    \"returnType\",\n    \"typeParameters\",\n  ],\n  aliases: [\n    \"UserWhitespacable\",\n    \"Function\",\n    \"Scopable\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Method\",\n    \"ObjectMember\",\n  ],\n});\n\ndefineType(\"ObjectProperty\", {\n  builder: [\n    \"key\",\n    \"value\",\n    \"computed\",\n    \"shorthand\",\n    ...(!process.env.BABEL_TYPES_8_BREAKING ? [\"decorators\"] : []),\n  ],\n  fields: {\n    computed: {\n      default: false,\n    },\n    key: {\n      validate: (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n          \"DecimalLiteral\",\n          \"PrivateName\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.ObjectProperty, key, val) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          } as Validator,\n          {\n            // todo(ts): can be discriminated union by `computed` property\n            oneOfNodeTypes: [\n              \"Expression\",\n              \"Identifier\",\n              \"StringLiteral\",\n              \"NumericLiteral\",\n              \"BigIntLiteral\",\n              \"DecimalLiteral\",\n              \"PrivateName\",\n            ],\n          },\n        );\n        return validator;\n      })(),\n    },\n    value: {\n      // Value may be PatternLike if this is an AssignmentProperty\n      // https://github.com/babel/babylon/issues/434\n      validate: assertNodeType(\"Expression\", \"PatternLike\"),\n    },\n    shorthand: {\n      validate: chain(\n        assertValueType(\"boolean\"),\n        Object.assign(\n          function (node: t.ObjectProperty, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            if (val && node.computed) {\n              throw new TypeError(\n                \"Property shorthand of ObjectProperty cannot be true if computed is true\",\n              );\n            }\n          } as Validator,\n          { type: \"boolean\" },\n        ),\n        function (node: t.ObjectProperty, key, val) {\n          if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n          if (val && !is(\"Identifier\", node.key)) {\n            throw new TypeError(\n              \"Property shorthand of ObjectProperty cannot be true if key is not an Identifier\",\n            );\n          }\n        } as Validator,\n      ),\n      default: false,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n  },\n  visitor: [\"key\", \"value\", \"decorators\"],\n  aliases: [\"UserWhitespacable\", \"Property\", \"ObjectMember\"],\n  validate: (function () {\n    const pattern = assertNodeType(\n      \"Identifier\",\n      \"Pattern\",\n      \"TSAsExpression\",\n      \"TSNonNullExpression\",\n      \"TSTypeAssertion\",\n    );\n    const expression = assertNodeType(\"Expression\");\n\n    return function (parent, key, node) {\n      if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n      const validator = is(\"ObjectPattern\", parent) ? pattern : expression;\n      validator(node, \"value\", node.value);\n    };\n  })(),\n});\n\ndefineType(\"RestElement\", {\n  visitor: [\"argument\", \"typeAnnotation\"],\n  builder: [\"argument\"],\n  aliases: [\"LVal\", \"PatternLike\"],\n  deprecatedAlias: \"RestProperty\",\n  fields: {\n    ...patternLikeCommon(),\n    argument: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"LVal\")\n        : assertNodeType(\n            \"Identifier\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"MemberExpression\",\n            \"TSAsExpression\",\n            \"TSTypeAssertion\",\n            \"TSNonNullExpression\",\n          ),\n    },\n    // For Flow\n    optional: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n  },\n  validate(parent: t.ArrayPattern | t.ObjectPattern, key) {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n    const match = /(\\w+)\\[(\\d+)\\]/.exec(key);\n    if (!match) throw new Error(\"Internal Babel error: malformed key.\");\n\n    const [, listKey, index] = match as unknown as [\n      string,\n      keyof typeof parent,\n      string,\n    ];\n    if ((parent[listKey] as t.Node[]).length > +index + 1) {\n      throw new TypeError(`RestElement must be last element of ${listKey}`);\n    }\n  },\n});\n\ndefineType(\"ReturnStatement\", {\n  visitor: [\"argument\"],\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"SequenceExpression\", {\n  visitor: [\"expressions\"],\n  fields: {\n    expressions: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Expression\")),\n      ),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"ParenthesizedExpression\", {\n  visitor: [\"expression\"],\n  aliases: [\"Expression\", \"ExpressionWrapper\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"SwitchCase\", {\n  visitor: [\"test\", \"consequent\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    consequent: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"SwitchStatement\", {\n  visitor: [\"discriminant\", \"cases\"],\n  aliases: [\"Statement\", \"BlockParent\", \"Scopable\"],\n  fields: {\n    discriminant: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    cases: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"SwitchCase\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"ThisExpression\", {\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"ThrowStatement\", {\n  visitor: [\"argument\"],\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"TryStatement\", {\n  visitor: [\"block\", \"handler\", \"finalizer\"],\n  aliases: [\"Statement\"],\n  fields: {\n    block: {\n      validate: chain(\n        assertNodeType(\"BlockStatement\"),\n        Object.assign(\n          function (node: t.TryStatement) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            // This validator isn't put at the top level because we can run it\n            // even if this node doesn't have a parent.\n\n            if (!node.handler && !node.finalizer) {\n              throw new TypeError(\n                \"TryStatement expects either a handler or finalizer, or both\",\n              );\n            }\n          } as Validator,\n          {\n            oneOfNodeTypes: [\"BlockStatement\"],\n          },\n        ),\n      ),\n    },\n    handler: {\n      optional: true,\n      validate: assertNodeType(\"CatchClause\"),\n    },\n    finalizer: {\n      optional: true,\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"UnaryExpression\", {\n  builder: [\"operator\", \"argument\", \"prefix\"],\n  fields: {\n    prefix: {\n      default: true,\n    },\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    operator: {\n      validate: assertOneOf(...UNARY_OPERATORS),\n    },\n  },\n  visitor: [\"argument\"],\n  aliases: [\"UnaryLike\", \"Expression\"],\n});\n\ndefineType(\"UpdateExpression\", {\n  builder: [\"operator\", \"argument\", \"prefix\"],\n  fields: {\n    prefix: {\n      default: false,\n    },\n    argument: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"Expression\")\n        : assertNodeType(\"Identifier\", \"MemberExpression\"),\n    },\n    operator: {\n      validate: assertOneOf(...UPDATE_OPERATORS),\n    },\n  },\n  visitor: [\"argument\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"VariableDeclaration\", {\n  builder: [\"kind\", \"declarations\"],\n  visitor: [\"declarations\"],\n  aliases: [\"Statement\", \"Declaration\"],\n  fields: {\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    kind: {\n      validate: assertOneOf(\"var\", \"let\", \"const\"),\n    },\n    declarations: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"VariableDeclarator\")),\n      ),\n    },\n  },\n  validate(parent, key, node) {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n    if (!is(\"ForXStatement\", parent, { left: node })) return;\n    if (node.declarations.length !== 1) {\n      throw new TypeError(\n        `Exactly one VariableDeclarator is required in the VariableDeclaration of a ${parent.type}`,\n      );\n    }\n  },\n});\n\ndefineType(\"VariableDeclarator\", {\n  visitor: [\"id\", \"init\"],\n  fields: {\n    id: {\n      validate: (function () {\n        if (!process.env.BABEL_TYPES_8_BREAKING) {\n          return assertNodeType(\"LVal\");\n        }\n\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"ArrayPattern\",\n          \"ObjectPattern\",\n        );\n        const without = assertNodeType(\"Identifier\");\n\n        return function (node: t.VariableDeclarator, key, val) {\n          const validator = node.init ? normal : without;\n          validator(node, key, val);\n        };\n      })(),\n    },\n    definite: {\n      optional: true,\n      validate: assertValueType(\"boolean\"),\n    },\n    init: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"WhileStatement\", {\n  visitor: [\"test\", \"body\"],\n  aliases: [\"Statement\", \"BlockParent\", \"Loop\", \"While\", \"Scopable\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"WithStatement\", {\n  visitor: [\"object\", \"body\"],\n  aliases: [\"Statement\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\n// --- ES2015 ---\ndefineType(\"AssignmentPattern\", {\n  visitor: [\"left\", \"right\", \"decorators\" /* for legacy param decorators */],\n  builder: [\"left\", \"right\"],\n  aliases: [\"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    left: {\n      validate: assertNodeType(\n        \"Identifier\",\n        \"ObjectPattern\",\n        \"ArrayPattern\",\n        \"MemberExpression\",\n        \"TSAsExpression\",\n        \"TSTypeAssertion\",\n        \"TSNonNullExpression\",\n      ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    // For TypeScript\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ArrayPattern\", {\n  visitor: [\"elements\", \"typeAnnotation\"],\n  builder: [\"elements\"],\n  aliases: [\"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeOrValueType(\"null\", \"PatternLike\", \"LVal\")),\n      ),\n    },\n    // For TypeScript\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    optional: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ArrowFunctionExpression\", {\n  builder: [\"params\", \"body\", \"async\"],\n  visitor: [\"params\", \"body\", \"returnType\", \"typeParameters\"],\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Expression\",\n    \"Pureish\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    expression: {\n      // https://github.com/babel/babylon/issues/505\n      validate: assertValueType(\"boolean\"),\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\", \"Expression\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassBody\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"ClassMethod\",\n            \"ClassPrivateMethod\",\n            \"ClassProperty\",\n            \"ClassPrivateProperty\",\n            \"ClassAccessorProperty\",\n            \"TSDeclareMethod\",\n            \"TSIndexSignature\",\n            \"StaticBlock\",\n          ),\n        ),\n      ),\n    },\n  },\n});\n\ndefineType(\"ClassExpression\", {\n  builder: [\"id\", \"superClass\", \"body\", \"decorators\"],\n  visitor: [\n    \"id\",\n    \"body\",\n    \"superClass\",\n    \"mixins\",\n    \"typeParameters\",\n    \"superTypeParameters\",\n    \"implements\",\n    \"decorators\",\n  ],\n  aliases: [\"Scopable\", \"Class\", \"Expression\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      // In declarations, this is missing if this is the\n      // child of an ExportDefaultDeclaration.\n      optional: true,\n    },\n    typeParameters: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n          )\n        : assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"ClassBody\"),\n    },\n    superClass: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n    superTypeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n    implements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\"TSExpressionWithTypeArguments\", \"ClassImplements\"),\n        ),\n      ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    mixins: {\n      validate: assertNodeType(\"InterfaceExtends\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassDeclaration\", {\n  inherits: \"ClassExpression\",\n  aliases: [\"Scopable\", \"Class\", \"Statement\", \"Declaration\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    typeParameters: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n          )\n        : assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"ClassBody\"),\n    },\n    superClass: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n    superTypeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n    implements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\"TSExpressionWithTypeArguments\", \"ClassImplements\"),\n        ),\n      ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    mixins: {\n      validate: assertNodeType(\"InterfaceExtends\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    abstract: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n  },\n  validate: (function () {\n    const identifier = assertNodeType(\"Identifier\");\n\n    return function (parent, key, node) {\n      if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n      if (!is(\"ExportDefaultDeclaration\", parent)) {\n        identifier(node, \"id\", node.id);\n      }\n    };\n  })(),\n});\n\ndefineType(\"ExportAllDeclaration\", {\n  visitor: [\"source\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ModuleDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n    exportKind: validateOptional(assertOneOf(\"type\", \"value\")),\n    assertions: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"ExportDefaultDeclaration\", {\n  visitor: [\"declaration\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ModuleDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    declaration: {\n      validate: assertNodeType(\n        \"TSDeclareFunction\",\n        \"FunctionDeclaration\",\n        \"ClassDeclaration\",\n        \"Expression\",\n      ),\n    },\n    exportKind: validateOptional(assertOneOf(\"value\")),\n  },\n});\n\ndefineType(\"ExportNamedDeclaration\", {\n  visitor: [\"declaration\", \"specifiers\", \"source\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ModuleDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    declaration: {\n      optional: true,\n      validate: chain(\n        assertNodeType(\"Declaration\"),\n        Object.assign(\n          function (node: t.ExportNamedDeclaration, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            // This validator isn't put at the top level because we can run it\n            // even if this node doesn't have a parent.\n\n            if (val && node.specifiers.length) {\n              throw new TypeError(\n                \"Only declaration or specifiers is allowed on ExportNamedDeclaration\",\n              );\n            }\n          } as Validator,\n          { oneOfNodeTypes: [\"Declaration\"] },\n        ),\n        function (node: t.ExportNamedDeclaration, key, val) {\n          if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n          // This validator isn't put at the top level because we can run it\n          // even if this node doesn't have a parent.\n\n          if (val && node.source) {\n            throw new TypeError(\"Cannot export a declaration from a source\");\n          }\n        },\n      ),\n    },\n    assertions: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    specifiers: {\n      default: [],\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          (function () {\n            const sourced = assertNodeType(\n              \"ExportSpecifier\",\n              \"ExportDefaultSpecifier\",\n              \"ExportNamespaceSpecifier\",\n            );\n            const sourceless = assertNodeType(\"ExportSpecifier\");\n\n            if (!process.env.BABEL_TYPES_8_BREAKING) return sourced;\n\n            return function (node: t.ExportNamedDeclaration, key, val) {\n              const validator = node.source ? sourced : sourceless;\n              validator(node, key, val);\n            } as Validator;\n          })(),\n        ),\n      ),\n    },\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n      optional: true,\n    },\n    exportKind: validateOptional(assertOneOf(\"type\", \"value\")),\n  },\n});\n\ndefineType(\"ExportSpecifier\", {\n  visitor: [\"local\", \"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    exported: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    exportKind: {\n      // And TypeScript's \"export { type foo } from\"\n      validate: assertOneOf(\"type\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ForOfStatement\", {\n  visitor: [\"left\", \"right\", \"body\"],\n  builder: [\"left\", \"right\", \"body\", \"await\"],\n  aliases: [\n    \"Scopable\",\n    \"Statement\",\n    \"For\",\n    \"BlockParent\",\n    \"Loop\",\n    \"ForXStatement\",\n  ],\n  fields: {\n    left: {\n      validate: (function () {\n        if (!process.env.BABEL_TYPES_8_BREAKING) {\n          return assertNodeType(\"VariableDeclaration\", \"LVal\");\n        }\n\n        const declaration = assertNodeType(\"VariableDeclaration\");\n        const lval = assertNodeType(\n          \"Identifier\",\n          \"MemberExpression\",\n          \"ArrayPattern\",\n          \"ObjectPattern\",\n          \"TSAsExpression\",\n          \"TSTypeAssertion\",\n          \"TSNonNullExpression\",\n        );\n\n        return function (node, key, val) {\n          if (is(\"VariableDeclaration\", val)) {\n            declaration(node, key, val);\n          } else {\n            lval(node, key, val);\n          }\n        };\n      })(),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n    await: {\n      default: false,\n    },\n  },\n});\n\ndefineType(\"ImportDeclaration\", {\n  visitor: [\"specifiers\", \"source\"],\n  aliases: [\"Statement\", \"Declaration\", \"ModuleDeclaration\"],\n  fields: {\n    assertions: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    specifiers: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"ImportSpecifier\",\n            \"ImportDefaultSpecifier\",\n            \"ImportNamespaceSpecifier\",\n          ),\n        ),\n      ),\n    },\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n    importKind: {\n      // Handle TypeScript/Flowtype's extension \"import type foo from\"\n      // TypeScript doesn't support typeof\n      validate: assertOneOf(\"type\", \"typeof\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ImportDefaultSpecifier\", {\n  visitor: [\"local\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"ImportNamespaceSpecifier\", {\n  visitor: [\"local\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"ImportSpecifier\", {\n  visitor: [\"local\", \"imported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    imported: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    importKind: {\n      // Handle Flowtype's extension \"import {typeof foo} from\"\n      // And TypeScript's \"import { type foo } from\"\n      validate: assertOneOf(\"type\", \"typeof\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"MetaProperty\", {\n  visitor: [\"meta\", \"property\"],\n  aliases: [\"Expression\"],\n  fields: {\n    meta: {\n      validate: chain(\n        assertNodeType(\"Identifier\"),\n        Object.assign(\n          function (node: t.MetaProperty, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            let property;\n            switch (val.name) {\n              case \"function\":\n                property = \"sent\";\n                break;\n              case \"new\":\n                property = \"target\";\n                break;\n              case \"import\":\n                property = \"meta\";\n                break;\n            }\n            if (!is(\"Identifier\", node.property, { name: property })) {\n              throw new TypeError(\"Unrecognised MetaProperty\");\n            }\n          } as Validator,\n          { oneOfNodeTypes: [\"Identifier\"] },\n        ),\n      ),\n    },\n    property: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\nexport const classMethodOrPropertyCommon = () => ({\n  abstract: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  accessibility: {\n    validate: assertOneOf(\"public\", \"private\", \"protected\"),\n    optional: true,\n  },\n  static: {\n    default: false,\n  },\n  override: {\n    default: false,\n  },\n  computed: {\n    default: false,\n  },\n  optional: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  key: {\n    validate: chain(\n      (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        return function (node: any, key: string, val: any) {\n          const validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n      })(),\n      assertNodeType(\n        \"Identifier\",\n        \"StringLiteral\",\n        \"NumericLiteral\",\n        \"BigIntLiteral\",\n        \"Expression\",\n      ),\n    ),\n  },\n});\n\nexport const classMethodOrDeclareMethodCommon = () => ({\n  ...functionCommon(),\n  ...classMethodOrPropertyCommon(),\n  params: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(\n        assertNodeType(\n          \"Identifier\",\n          \"Pattern\",\n          \"RestElement\",\n          \"TSParameterProperty\",\n        ),\n      ),\n    ),\n  },\n  kind: {\n    validate: assertOneOf(\"get\", \"set\", \"method\", \"constructor\"),\n    default: \"method\",\n  },\n  access: {\n    validate: chain(\n      assertValueType(\"string\"),\n      assertOneOf(\"public\", \"private\", \"protected\"),\n    ),\n    optional: true,\n  },\n  decorators: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(assertNodeType(\"Decorator\")),\n    ),\n    optional: true,\n  },\n});\n\ndefineType(\"ClassMethod\", {\n  aliases: [\"Function\", \"Scopable\", \"BlockParent\", \"FunctionParent\", \"Method\"],\n  builder: [\n    \"kind\",\n    \"key\",\n    \"params\",\n    \"body\",\n    \"computed\",\n    \"static\",\n    \"generator\",\n    \"async\",\n  ],\n  visitor: [\n    \"key\",\n    \"params\",\n    \"body\",\n    \"decorators\",\n    \"returnType\",\n    \"typeParameters\",\n  ],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...functionTypeAnnotationCommon(),\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"ObjectPattern\", {\n  visitor: [\n    \"properties\",\n    \"typeAnnotation\",\n    \"decorators\" /* for legacy param decorators */,\n  ],\n  builder: [\"properties\"],\n  aliases: [\"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    properties: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"RestElement\", \"ObjectProperty\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"SpreadElement\", {\n  visitor: [\"argument\"],\n  aliases: [\"UnaryLike\"],\n  deprecatedAlias: \"SpreadProperty\",\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\n  \"Super\",\n  process.env.BABEL_8_BREAKING\n    ? undefined\n    : {\n        aliases: [\"Expression\"],\n      },\n);\n\ndefineType(\"TaggedTemplateExpression\", {\n  visitor: [\"tag\", \"quasi\", \"typeParameters\"],\n  builder: [\"tag\", \"quasi\"],\n  aliases: [\"Expression\"],\n  fields: {\n    tag: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    quasi: {\n      validate: assertNodeType(\"TemplateLiteral\"),\n    },\n    typeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TemplateElement\", {\n  builder: [\"value\", \"tail\"],\n  fields: {\n    value: {\n      validate: chain(\n        assertShape({\n          raw: {\n            validate: assertValueType(\"string\"),\n          },\n          cooked: {\n            validate: assertValueType(\"string\"),\n            optional: true,\n          },\n        }),\n        function templateElementCookedValidator(node: t.TemplateElement) {\n          const raw = node.value.raw;\n\n          let str,\n            containsInvalid,\n            unterminatedCalled = false;\n          try {\n            const error = () => {\n              throw new Error();\n            };\n            ({ str, containsInvalid } = readStringContents(\n              \"template\",\n              raw,\n              0,\n              0,\n              0,\n              {\n                unterminated() {\n                  unterminatedCalled = true;\n                },\n                strictNumericEscape: error,\n                invalidEscapeSequence: error,\n                numericSeparatorInEscapeSequence: error,\n                unexpectedNumericSeparator: error,\n                invalidDigit: error,\n                invalidCodePoint: error,\n              },\n            ));\n          } catch {\n            // TODO: When https://github.com/babel/babel/issues/14775 is fixed\n            // we can remove the try/catch block.\n            unterminatedCalled = true;\n            containsInvalid = true;\n          }\n          if (!unterminatedCalled) throw new Error(\"Invalid raw\");\n\n          node.value.cooked = containsInvalid ? null : str;\n        },\n      ),\n    },\n    tail: {\n      default: false,\n    },\n  },\n});\n\ndefineType(\"TemplateLiteral\", {\n  visitor: [\"quasis\", \"expressions\"],\n  aliases: [\"Expression\", \"Literal\"],\n  fields: {\n    quasis: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"TemplateElement\")),\n      ),\n    },\n    expressions: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            // For TypeScript template literal types\n            \"TSType\",\n          ),\n        ),\n        function (node: t.TemplateLiteral, key, val) {\n          if (node.quasis.length !== val.length + 1) {\n            throw new TypeError(\n              `Number of ${\n                node.type\n              } quasis should be exactly one more than the number of expressions.\\nExpected ${\n                val.length + 1\n              } quasis but got ${node.quasis.length}`,\n            );\n          }\n        } as Validator,\n      ),\n    },\n  },\n});\n\ndefineType(\"YieldExpression\", {\n  builder: [\"argument\", \"delegate\"],\n  visitor: [\"argument\"],\n  aliases: [\"Expression\", \"Terminatorless\"],\n  fields: {\n    delegate: {\n      validate: chain(\n        assertValueType(\"boolean\"),\n        Object.assign(\n          function (node: t.YieldExpression, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            if (val && !node.argument) {\n              throw new TypeError(\n                \"Property delegate of YieldExpression cannot be true if there is no argument\",\n              );\n            }\n          } as Validator,\n          { type: \"boolean\" },\n        ),\n      ),\n      default: false,\n    },\n    argument: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\n// --- ES2017 ---\ndefineType(\"AwaitExpression\", {\n  builder: [\"argument\"],\n  visitor: [\"argument\"],\n  aliases: [\"Expression\", \"Terminatorless\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\n// --- ES2019 ---\ndefineType(\"Import\", {\n  aliases: [\"Expression\"],\n});\n\n// --- ES2020 ---\ndefineType(\"BigIntLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"ExportNamespaceSpecifier\", {\n  visitor: [\"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    exported: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"OptionalMemberExpression\", {\n  builder: [\"object\", \"property\", \"computed\", \"optional\"],\n  visitor: [\"object\", \"property\"],\n  aliases: [\"Expression\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    property: {\n      validate: (function () {\n        const normal = assertNodeType(\"Identifier\");\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.OptionalMemberExpression, key, val) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          } as Validator,\n          // todo(ts): can be discriminated union by `computed` property\n          { oneOfNodeTypes: [\"Expression\", \"Identifier\"] },\n        );\n        return validator;\n      })(),\n    },\n    computed: {\n      default: false,\n    },\n    optional: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertValueType(\"boolean\")\n        : chain(assertValueType(\"boolean\"), assertOptionalChainStart()),\n    },\n  },\n});\n\ndefineType(\"OptionalCallExpression\", {\n  visitor: [\"callee\", \"arguments\", \"typeParameters\", \"typeArguments\"],\n  builder: [\"callee\", \"arguments\", \"optional\"],\n  aliases: [\"Expression\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    arguments: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            \"SpreadElement\",\n            \"JSXNamespacedName\",\n            \"ArgumentPlaceholder\",\n          ),\n        ),\n      ),\n    },\n    optional: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertValueType(\"boolean\")\n        : chain(assertValueType(\"boolean\"), assertOptionalChainStart()),\n    },\n    typeArguments: {\n      validate: assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    typeParameters: {\n      validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n      optional: true,\n    },\n  },\n});\n\n// --- ES2022 ---\ndefineType(\"ClassProperty\", {\n  visitor: [\"key\", \"value\", \"typeAnnotation\", \"decorators\"],\n  builder: [\n    \"key\",\n    \"value\",\n    \"typeAnnotation\",\n    \"decorators\",\n    \"computed\",\n    \"static\",\n  ],\n  aliases: [\"Property\"],\n  fields: {\n    ...classMethodOrPropertyCommon(),\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassAccessorProperty\", {\n  visitor: [\"key\", \"value\", \"typeAnnotation\", \"decorators\"],\n  builder: [\n    \"key\",\n    \"value\",\n    \"typeAnnotation\",\n    \"decorators\",\n    \"computed\",\n    \"static\",\n  ],\n  aliases: [\"Property\", \"Accessor\"],\n  fields: {\n    ...classMethodOrPropertyCommon(),\n    key: {\n      validate: chain(\n        (function () {\n          const normal = assertNodeType(\n            \"Identifier\",\n            \"StringLiteral\",\n            \"NumericLiteral\",\n            \"BigIntLiteral\",\n            \"PrivateName\",\n          );\n          const computed = assertNodeType(\"Expression\");\n\n          return function (node: any, key: string, val: any) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          };\n        })(),\n        assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n          \"Expression\",\n          \"PrivateName\",\n        ),\n      ),\n    },\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassPrivateProperty\", {\n  visitor: [\"key\", \"value\", \"decorators\", \"typeAnnotation\"],\n  builder: [\"key\", \"value\", \"decorators\", \"static\"],\n  aliases: [\"Property\", \"Private\"],\n  fields: {\n    key: {\n      validate: assertNodeType(\"PrivateName\"),\n    },\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    static: {\n      validate: assertValueType(\"boolean\"),\n      default: false,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassPrivateMethod\", {\n  builder: [\"kind\", \"key\", \"params\", \"body\", \"static\"],\n  visitor: [\n    \"key\",\n    \"params\",\n    \"body\",\n    \"decorators\",\n    \"returnType\",\n    \"typeParameters\",\n  ],\n  aliases: [\n    \"Function\",\n    \"Scopable\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Method\",\n    \"Private\",\n  ],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...functionTypeAnnotationCommon(),\n    kind: {\n      validate: assertOneOf(\"get\", \"set\", \"method\"),\n      default: \"method\",\n    },\n    key: {\n      validate: assertNodeType(\"PrivateName\"),\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"PrivateName\", {\n  visitor: [\"id\"],\n  aliases: [\"Private\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"StaticBlock\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"FunctionParent\"],\n});\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAEA;;AAEA;;AAQA;;AAcA,MAAMA,UAAU,GAAG,IAAAC,wBAAA,EAAkB,cAAlB,CAAnB;AAEAD,UAAU,CAAC,iBAAD,EAAoB;EAC5BE,MAAM,EAAE;IACNC,QAAQ,EAAE;MACRC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAC,4BAAA,EAAsB,MAAtB,EAA8B,YAA9B,EAA4C,eAA5C,CADF,CAFQ,CADF;MAORC,OAAO,EAAE,CAACC,OAAO,CAACC,GAAR,CAAYC,sBAAb,GAAsC,EAAtC,GAA2CC;IAP5C;EADJ,CADoB;EAY5BC,OAAO,EAAE,CAAC,UAAD,CAZmB;EAa5BC,OAAO,EAAE,CAAC,YAAD;AAbmB,CAApB,CAAV;AAgBAf,UAAU,CAAC,sBAAD,EAAyB;EACjCE,MAAM,EAAE;IACNc,QAAQ,EAAE;MACRZ,QAAQ,EAAG,YAAY;QACrB,IAAI,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;UACvC,OAAO,IAAAN,sBAAA,EAAgB,QAAhB,CAAP;QACD;;QAED,MAAMW,UAAU,GAAG,IAAAC,kBAAA,EAAY,GAAGC,+BAAf,CAAnB;QACA,MAAMC,OAAO,GAAG,IAAAF,kBAAA,EAAY,GAAZ,CAAhB;QAEA,OAAO,UAAUG,IAAV,EAAwCC,GAAxC,EAA6CC,GAA7C,EAAkD;UACvD,MAAMC,SAAS,GAAG,IAAAC,WAAA,EAAG,SAAH,EAAcJ,IAAI,CAACK,IAAnB,IAA2BN,OAA3B,GAAqCH,UAAvD;UACAO,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAHD;MAID,CAZS;IADF,CADJ;IAgBNG,IAAI,EAAE;MACJtB,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACN,IAAAe,qBAAA,EAAe,MAAf,CADM,GAEN,IAAAA,qBAAA,EACE,YADF,EAEE,kBAFF,EAGE,cAHF,EAIE,eAJF,EAKE,gBALF,EAME,iBANF,EAOE,qBAPF;IAHA,CAhBA;IA6BNC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL;EA7BD,CADyB;EAkCjCE,OAAO,EAAE,CAAC,UAAD,EAAa,MAAb,EAAqB,OAArB,CAlCwB;EAmCjCf,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CAnCwB;EAoCjCC,OAAO,EAAE,CAAC,YAAD;AApCwB,CAAzB,CAAV;AAuCAf,UAAU,CAAC,kBAAD,EAAqB;EAC7B6B,OAAO,EAAE,CAAC,UAAD,EAAa,MAAb,EAAqB,OAArB,CADoB;EAE7B3B,MAAM,EAAE;IACNc,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAA,EAAY,GAAGY,2BAAf;IADF,CADJ;IAINJ,IAAI,EAAE;MACJtB,QAAQ,EAAG,YAAY;QACrB,MAAM2B,UAAU,GAAG,IAAAJ,qBAAA,EAAe,YAAf,CAAnB;QACA,MAAMK,IAAI,GAAG,IAAAL,qBAAA,EAAe,YAAf,EAA6B,aAA7B,CAAb;QAEA,MAAMH,SAAoB,GAAGS,MAAM,CAACC,MAAP,CAC3B,UAAUb,IAAV,EAAoCC,GAApC,EAAyCC,GAAzC,EAA8C;UAC5C,MAAMC,SAAS,GAAGH,IAAI,CAACL,QAAL,KAAkB,IAAlB,GAAyBgB,IAAzB,GAAgCD,UAAlD;UACAP,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAJ0B,EAM3B;UAAEY,cAAc,EAAE,CAAC,YAAD,EAAe,aAAf;QAAlB,CAN2B,CAA7B;QAQA,OAAOX,SAAP;MACD,CAbS;IADN,CAJA;IAoBNI,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL;EApBD,CAFqB;EA0B7Bb,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CA1BoB;EA2B7BC,OAAO,EAAE,CAAC,QAAD,EAAW,YAAX;AA3BoB,CAArB,CAAV;AA8BAf,UAAU,CAAC,sBAAD,EAAyB;EACjC6B,OAAO,EAAE,CAAC,OAAD,CADwB;EAEjC3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADL;EADD;AAFyB,CAAzB,CAAV;AASAN,UAAU,CAAC,WAAD,EAAc;EACtBc,OAAO,EAAE,CAAC,OAAD,CADa;EAEtBZ,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,kBAAf;IADL;EADD;AAFc,CAAd,CAAV;AASA3B,UAAU,CAAC,kBAAD,EAAqB;EAC7B6B,OAAO,EAAE,CAAC,OAAD,CADoB;EAE7B3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADL;EADD;AAFqB,CAArB,CAAV;AASAN,UAAU,CAAC,gBAAD,EAAmB;EAC3B6B,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,CADkB;EAE3Bf,OAAO,EAAE,CAAC,YAAD,EAAe,MAAf,CAFkB;EAG3BZ,MAAM,EAAE;IACNmC,UAAU,EAAE;MACVjC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVlB,OAAO,EAAE;IALC,CADN;IAQN6B,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ;IADN;EARA,CAHmB;EAkB3BZ,OAAO,EAAE,CAAC,UAAD,EAAa,aAAb,EAA4B,OAA5B,EAAqC,WAArC;AAlBkB,CAAnB,CAAV;AAqBAf,UAAU,CAAC,gBAAD,EAAmB;EAC3Bc,OAAO,EAAE,CAAC,OAAD,CADkB;EAE3BZ,MAAM,EAAE;IACNqC,KAAK,EAAE;MACLnC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADL;MAELa,QAAQ,EAAE;IAFL;EADD,CAFmB;EAQ3BzB,OAAO,EAAE,CAAC,WAAD,EAAc,gBAAd,EAAgC,qBAAhC;AARkB,CAAnB,CAAV;AAWAf,UAAU,CAAC,gBAAD,EAAmB;EAC3Bc,OAAO,EAAE,CAAC,QAAD,EAAW,WAAX,EAAwB,gBAAxB,EAA0C,eAA1C,CADkB;EAE3Be,OAAO,EAAE,CAAC,QAAD,EAAW,WAAX,CAFkB;EAG3Bd,OAAO,EAAE,CAAC,YAAD,CAHkB;EAI3Bb,MAAM;IACJuC,MAAM,EAAE;MACNrC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,EAA6B,OAA7B,EAAsC,uBAAtC;IADJ,CADJ;IAIJe,SAAS,EAAE;MACTtC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EACE,YADF,EAEE,eAFF,EAGE,mBAHF,EAIE,qBAJF,CADF,CAFQ;IADD;EAJP,GAiBA,CAACjB,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACA;IACE4B,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAc,kBAAA,EAAY,IAAZ,EAAkB,KAAlB,CADF;MAERsB,QAAQ,EAAE;IAFF;EADZ,CADA,GAOA,EAxBA;IAyBJG,aAAa,EAAE;MACbvC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,4BAAf,CADG;MAEba,QAAQ,EAAE;IAFG,CAzBX;IA6BJI,cAAc,EAAE;MACdxC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,8BAAf,CADI;MAEda,QAAQ,EAAE;IAFI;EA7BZ;AAJqB,CAAnB,CAAV;AAwCAxC,UAAU,CAAC,aAAD,EAAgB;EACxBc,OAAO,EAAE,CAAC,OAAD,EAAU,MAAV,CADe;EAExBZ,MAAM,EAAE;IACN2C,KAAK,EAAE;MACLzC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,EAA6B,cAA7B,EAA6C,eAA7C,CADL;MAELa,QAAQ,EAAE;IAFL,CADD;IAKNF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IADN;EALA,CAFgB;EAWxBZ,OAAO,EAAE,CAAC,UAAD,EAAa,aAAb;AAXe,CAAhB,CAAV;AAcAf,UAAU,CAAC,uBAAD,EAA0B;EAClCc,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,EAAuB,WAAvB,CADyB;EAElCZ,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADN,CADA;IAINoB,UAAU,EAAE;MACV3C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADA,CAJN;IAONqB,SAAS,EAAE;MACT5C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADD;EAPL,CAF0B;EAalCZ,OAAO,EAAE,CAAC,YAAD,EAAe,aAAf;AAbyB,CAA1B,CAAV;AAgBAf,UAAU,CAAC,mBAAD,EAAsB;EAC9Bc,OAAO,EAAE,CAAC,OAAD,CADqB;EAE9BZ,MAAM,EAAE;IACNqC,KAAK,EAAE;MACLnC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADL;MAELa,QAAQ,EAAE;IAFL;EADD,CAFsB;EAQ9BzB,OAAO,EAAE,CAAC,WAAD,EAAc,gBAAd,EAAgC,qBAAhC;AARqB,CAAtB,CAAV;AAWAf,UAAU,CAAC,mBAAD,EAAsB;EAC9Be,OAAO,EAAE,CAAC,WAAD;AADqB,CAAtB,CAAV;AAIAf,UAAU,CAAC,kBAAD,EAAqB;EAC7Bc,OAAO,EAAE,CAAC,MAAD,EAAS,MAAT,CADoB;EAE7BZ,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADN,CADA;IAINW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN;EAJA,CAFqB;EAU7BZ,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,EAA6B,MAA7B,EAAqC,OAArC,EAA8C,UAA9C;AAVoB,CAArB,CAAV;AAaAf,UAAU,CAAC,gBAAD,EAAmB;EAC3Be,OAAO,EAAE,CAAC,WAAD;AADkB,CAAnB,CAAV;AAIAf,UAAU,CAAC,qBAAD,EAAwB;EAChCc,OAAO,EAAE,CAAC,YAAD,CADuB;EAEhCZ,MAAM,EAAE;IACN6B,UAAU,EAAE;MACV3B,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADA;EADN,CAFwB;EAOhCZ,OAAO,EAAE,CAAC,WAAD,EAAc,mBAAd;AAPuB,CAAxB,CAAV;AAUAf,UAAU,CAAC,MAAD,EAAS;EACjB6B,OAAO,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,QAAxB,CADQ;EAEjBf,OAAO,EAAE,CAAC,SAAD,CAFQ;EAGjBZ,MAAM,EAAE;IACN+C,OAAO,EAAE;MACP7C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,SAAf;IADH,CADH;IAINuB,QAAQ,EAAE;MACR9C,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACNqB,MAAM,CAACC,MAAP,CAAc,MAAM,CAAE,CAAtB,EAAwB;QACtBiB,IAAI,EAAE;UAAEhB,cAAc,EAAE,CAAC,cAAD,EAAiB,aAAjB;QAAlB;MADgB,CAAxB,CADM,GAIN,IAAA5B,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,cAAf,EAA+B,aAA/B,CAAX,CALI;MAMRa,QAAQ,EAAE;IANF,CAJJ;IAYNY,MAAM,EAAE;MAENhD,QAAQ,EAAE,IAAAG,iBAAA,EAAW0B,MAAM,CAACC,MAAP,CAAc,MAAM,CAAE,CAAtB,EAAwB;QAAEmB,IAAI,EAAE;MAAR,CAAxB,CAAX,CAFJ;MAGNb,QAAQ,EAAE;IAHJ;EAZF;AAHS,CAAT,CAAV;AAuBAxC,UAAU,CAAC,gBAAD,EAAmB;EAC3Bc,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,EAAkB,MAAlB,CADkB;EAE3BC,OAAO,EAAE,CACP,UADO,EAEP,WAFO,EAGP,KAHO,EAIP,aAJO,EAKP,MALO,EAMP,eANO,CAFkB;EAU3Bb,MAAM,EAAE;IACNwB,IAAI,EAAE;MACJtB,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACN,IAAAe,qBAAA,EAAe,qBAAf,EAAsC,MAAtC,CADM,GAEN,IAAAA,qBAAA,EACE,qBADF,EAEE,YAFF,EAGE,kBAHF,EAIE,cAJF,EAKE,eALF,EAME,gBANF,EAOE,iBAPF,EAQE,qBARF;IAHA,CADA;IAeNC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL,CAfD;IAkBNW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN;EAlBA;AAVmB,CAAnB,CAAV;AAkCA3B,UAAU,CAAC,cAAD,EAAiB;EACzBc,OAAO,EAAE,CAAC,MAAD,EAAS,MAAT,EAAiB,QAAjB,EAA2B,MAA3B,CADgB;EAEzBC,OAAO,EAAE,CAAC,UAAD,EAAa,WAAb,EAA0B,KAA1B,EAAiC,aAAjC,EAAgD,MAAhD,CAFgB;EAGzBb,MAAM,EAAE;IACNoD,IAAI,EAAE;MACJlD,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,qBAAf,EAAsC,YAAtC,CADN;MAEJa,QAAQ,EAAE;IAFN,CADA;IAKNM,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADN;MAEJa,QAAQ,EAAE;IAFN,CALA;IASNe,MAAM,EAAE;MACNnD,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADJ;MAENa,QAAQ,EAAE;IAFJ,CATF;IAaNF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN;EAbA;AAHiB,CAAjB,CAAV;;AAsBO,MAAM6B,cAAc,GAAG,OAAO;EACnCC,MAAM,EAAE;IACNrD,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,YAAf,EAA6B,SAA7B,EAAwC,aAAxC,CAAX,CAFQ;EADJ,CAD2B;EAOnC+B,SAAS,EAAE;IACTjD,OAAO,EAAE;EADA,CAPwB;EAUnCkD,KAAK,EAAE;IACLlD,OAAO,EAAE;EADJ;AAV4B,CAAP,CAAvB;;;;AAeA,MAAMmD,4BAA4B,GAAG,OAAO;EACjDC,UAAU,EAAE;IACVzD,QAAQ,EAEJ,IAAAuB,qBAAA,EACE,gBADF,EAEE,kBAFF,EAIE,MAJF,CAHM;IASVa,QAAQ,EAAE;EATA,CADqC;EAYjDI,cAAc,EAAE;IACdxC,QAAQ,EAEJ,IAAAuB,qBAAA,EACE,0BADF,EAEE,4BAFF,EAIE,MAJF,CAHU;IASda,QAAQ,EAAE;EATI;AAZiC,CAAP,CAArC;;;;AAyBA,MAAMsB,yBAAyB,GAAG,wBACpCN,cAAc,EADsB;EAEvCO,OAAO,EAAE;IACP3D,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADH;IAEPkC,QAAQ,EAAE;EAFH,CAF8B;EAMvCwB,EAAE,EAAE;IACF5D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADR;IAEFa,QAAQ,EAAE;EAFR;AANmC,EAAlC;;;AAYPxC,UAAU,CAAC,qBAAD,EAAwB;EAChC6B,OAAO,EAAE,CAAC,IAAD,EAAO,QAAP,EAAiB,MAAjB,EAAyB,WAAzB,EAAsC,OAAtC,CADuB;EAEhCf,OAAO,EAAE,CAAC,IAAD,EAAO,QAAP,EAAiB,MAAjB,EAAyB,YAAzB,EAAuC,gBAAvC,CAFuB;EAGhCZ,MAAM,oBACD4D,yBAAyB,EADxB,EAEDF,4BAA4B,EAF3B;IAGJtB,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IADN,CAHF;IAMJsC,SAAS,EAAE;MACT7D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,mBAAf,EAAoC,mBAApC,CADD;MAETa,QAAQ,EAAE;IAFD;EANP,EAH0B;EAchCzB,OAAO,EAAE,CACP,UADO,EAEP,UAFO,EAGP,aAHO,EAIP,gBAJO,EAKP,WALO,EAMP,SANO,EAOP,aAPO,CAduB;EAuBhCX,QAAQ,EAAG,YAAY;IACrB,IAAI,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC,OAAO,MAAM,CAAE,CAAf;IAEzC,MAAMK,UAAU,GAAG,IAAAU,qBAAA,EAAe,YAAf,CAAnB;IAEA,OAAO,UAAUuC,MAAV,EAAkB5C,GAAlB,EAAuBD,IAAvB,EAA6B;MAClC,IAAI,CAAC,IAAAI,WAAA,EAAG,0BAAH,EAA+ByC,MAA/B,CAAL,EAA6C;QAC3CjD,UAAU,CAACI,IAAD,EAAO,IAAP,EAAaA,IAAI,CAAC2C,EAAlB,CAAV;MACD;IACF,CAJD;EAKD,CAVS;AAvBsB,CAAxB,CAAV;AAoCAhE,UAAU,CAAC,oBAAD,EAAuB;EAC/BmE,QAAQ,EAAE,qBADqB;EAE/BpD,OAAO,EAAE,CACP,UADO,EAEP,UAFO,EAGP,aAHO,EAIP,gBAJO,EAKP,YALO,EAMP,SANO,CAFsB;EAU/Bb,MAAM,oBACDsD,cAAc,EADb,EAEDI,4BAA4B,EAF3B;IAGJI,EAAE,EAAE;MACF5D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADR;MAEFa,QAAQ,EAAE;IAFR,CAHA;IAOJF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IADN,CAPF;IAUJsC,SAAS,EAAE;MACT7D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,mBAAf,EAAoC,mBAApC,CADD;MAETa,QAAQ,EAAE;IAFD;EAVP;AAVyB,CAAvB,CAAV;;AA2BO,MAAM4B,iBAAiB,GAAG,OAAO;EACtCC,cAAc,EAAE;IACdjE,QAAQ,EAEJ,IAAAuB,qBAAA,EACE,gBADF,EAEE,kBAFF,EAIE,MAJF,CAHU;IASda,QAAQ,EAAE;EATI,CADsB;EAYtC8B,UAAU,EAAE;IACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;IAKVa,QAAQ,EAAE;EALA;AAZ0B,CAAP,CAA1B;;;AAqBPxC,UAAU,CAAC,YAAD,EAAe;EACvB6B,OAAO,EAAE,CAAC,MAAD,CADc;EAEvBf,OAAO,EAAE,CAAC,gBAAD,EAAmB,YAAnB,CAFc;EAGvBC,OAAO,EAAE,CAAC,YAAD,EAAe,aAAf,EAA8B,MAA9B,EAAsC,cAAtC,CAHc;EAIvBb,MAAM,oBACDkE,iBAAiB,EADhB;IAEJG,IAAI,EAAE;MACJnE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,QAAhB,CADQ,EAER2B,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAAgBC,GAAhB,EAAqBC,GAArB,EAA0B;QACxB,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAEzC,IAAI,CAAC,IAAA4D,0BAAA,EAAkBjD,GAAlB,EAAuB,KAAvB,CAAL,EAAoC;UAClC,MAAM,IAAIkD,SAAJ,CAAe,IAAGlD,GAAI,kCAAtB,CAAN;QACD;MACF,CAPH,EAQE;QAAE8B,IAAI,EAAE;MAAR,CARF,CAFQ;IADN,CAFF;IAiBJb,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF;EAjBN,EAJiB;;EA0BvBpC,QAAQ,CAAC8D,MAAD,EAAS5C,GAAT,EAAcD,IAAd,EAAoB;IAC1B,IAAI,CAACX,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;IAEzC,MAAM8D,KAAK,GAAG,WAAWC,IAAX,CAAgBrD,GAAhB,CAAd;IACA,IAAI,CAACoD,KAAL,EAAY;IAEZ,MAAM,GAAGE,SAAH,IAAgBF,KAAtB;IACA,MAAMG,OAAO,GAAG;MAAEC,QAAQ,EAAE;IAAZ,CAAhB;;IAIA,IAAIF,SAAS,KAAK,UAAlB,EAA8B;MAC5B,IAAI,IAAAnD,WAAA,EAAG,kBAAH,EAAuByC,MAAvB,EAA+BW,OAA/B,CAAJ,EAA6C;MAC7C,IAAI,IAAApD,WAAA,EAAG,0BAAH,EAA+ByC,MAA/B,EAAuCW,OAAvC,CAAJ,EAAqD;IACtD,CAHD,MAGO,IAAID,SAAS,KAAK,KAAlB,EAAyB;MAC9B,IAAI,IAAAnD,WAAA,EAAG,UAAH,EAAeyC,MAAf,EAAuBW,OAAvB,CAAJ,EAAqC;MACrC,IAAI,IAAApD,WAAA,EAAG,QAAH,EAAayC,MAAb,EAAqBW,OAArB,CAAJ,EAAmC;IACpC,CAHM,MAGA,IAAID,SAAS,KAAK,UAAlB,EAA8B;MACnC,IAAI,IAAAnD,WAAA,EAAG,iBAAH,EAAsByC,MAAtB,CAAJ,EAAmC;IACpC,CAFM,MAEA,IAAIU,SAAS,KAAK,UAAlB,EAA8B;MACnC,IAAI,IAAAnD,WAAA,EAAG,iBAAH,EAAsByC,MAAtB,EAA8B;QAAEa,QAAQ,EAAE1D;MAAZ,CAA9B,CAAJ,EAAuD;IACxD,CAFM,MAEA,IAAIuD,SAAS,KAAK,MAAlB,EAA0B;MAC/B,IAAI,IAAAnD,WAAA,EAAG,cAAH,EAAmByC,MAAnB,EAA2B;QAAEc,IAAI,EAAE3D;MAAR,CAA3B,CAAJ,EAAgD;IACjD;;IAED,IAIE,CAAC,IAAA4D,oCAAA,EAAU5D,IAAI,CAACkD,IAAf,KAAwB,IAAAW,yCAAA,EAAe7D,IAAI,CAACkD,IAApB,EAA0B,KAA1B,CAAzB,KAGAlD,IAAI,CAACkD,IAAL,KAAc,MAPhB,EAQE;MACA,MAAM,IAAIE,SAAJ,CAAe,IAAGpD,IAAI,CAACkD,IAAK,6BAA5B,CAAN;IACD;EACF;;AA9DsB,CAAf,CAAV;AAiEAvE,UAAU,CAAC,aAAD,EAAgB;EACxBc,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,EAAuB,WAAvB,CADe;EAExBC,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CAFe;EAGxBb,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADN,CADA;IAINoB,UAAU,EAAE;MACV3C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADA,CAJN;IAONqB,SAAS,EAAE;MACTR,QAAQ,EAAE,IADD;MAETpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IAFD;EAPL;AAHgB,CAAhB,CAAV;AAiBA3B,UAAU,CAAC,kBAAD,EAAqB;EAC7Bc,OAAO,EAAE,CAAC,OAAD,EAAU,MAAV,CADoB;EAE7BC,OAAO,EAAE,CAAC,WAAD,CAFoB;EAG7Bb,MAAM,EAAE;IACNqC,KAAK,EAAE;MACLnC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL,CADD;IAINW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN;EAJA;AAHqB,CAArB,CAAV;AAaA3B,UAAU,CAAC,eAAD,EAAkB;EAC1B6B,OAAO,EAAE,CAAC,OAAD,CADiB;EAE1B3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADL;EADD,CAFkB;EAO1BS,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,EAAqC,WAArC;AAPiB,CAAlB,CAAV;AAUAf,UAAU,CAAC,gBAAD,EAAmB;EAC3B6B,OAAO,EAAE,CAAC,OAAD,CADkB;EAE3BsD,eAAe,EAAE,eAFU;EAG3BjF,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADL;EADD,CAHmB;EAQ3BS,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,EAAqC,WAArC;AARkB,CAAnB,CAAV;AAWAf,UAAU,CAAC,aAAD,EAAgB;EACxBe,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,EAAqC,WAArC;AADe,CAAhB,CAAV;AAIAf,UAAU,CAAC,gBAAD,EAAmB;EAC3B6B,OAAO,EAAE,CAAC,OAAD,CADkB;EAE3B3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB;IADL;EADD,CAFmB;EAO3BS,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,EAAqC,WAArC;AAPkB,CAAnB,CAAV;AAUAf,UAAU,CAAC,eAAD,EAAkB;EAC1B6B,OAAO,EAAE,CAAC,SAAD,EAAY,OAAZ,CADiB;EAE1BsD,eAAe,EAAE,cAFS;EAG1BpE,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,CAHiB;EAI1Bb,MAAM,EAAE;IACNkB,OAAO,EAAE;MACPhB,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADH,CADH;IAIN8E,KAAK,EAAE;MACLhF,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,QAAhB,CADQ,EAER2B,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAAgBC,GAAhB,EAAqBC,GAArB,EAA0B;QACxB,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;QAEzC,MAAMyE,OAAO,GAAG,YAAYV,IAAZ,CAAiBpD,GAAjB,CAAhB;;QACA,IAAI8D,OAAJ,EAAa;UACX,MAAM,IAAIZ,SAAJ,CAAe,IAAGY,OAAO,CAAC,CAAD,CAAI,8BAA7B,CAAN;QACD;MACF,CARH,EASE;QAAEhC,IAAI,EAAE;MAAR,CATF,CAFQ,CADL;MAeL5C,OAAO,EAAE;IAfJ;EAJD;AAJkB,CAAlB,CAAV;AA4BAT,UAAU,CAAC,mBAAD,EAAsB;EAC9B6B,OAAO,EAAE,CAAC,UAAD,EAAa,MAAb,EAAqB,OAArB,CADqB;EAE9Bf,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CAFqB;EAG9BC,OAAO,EAAE,CAAC,QAAD,EAAW,YAAX,CAHqB;EAI9Bb,MAAM,EAAE;IACNc,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAA,EAAY,GAAGoE,4BAAf;IADF,CADJ;IAIN5D,IAAI,EAAE;MACJtB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADN,CAJA;IAONC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL;EAPD;AAJsB,CAAtB,CAAV;AAiBA3B,UAAU,CAAC,kBAAD,EAAqB;EAC7B6B,OAAO,EAAE,CACP,QADO,EAEP,UAFO,EAGP,UAHO,EAIP,IAAI,CAACnB,OAAO,CAACC,GAAR,CAAYC,sBAAb,GAAsC,CAAC,UAAD,CAAtC,GAAqD,EAAzD,CAJO,CADoB;EAO7BE,OAAO,EAAE,CAAC,QAAD,EAAW,UAAX,CAPoB;EAQ7BC,OAAO,EAAE,CAAC,YAAD,EAAe,MAAf,CARoB;EAS7Bb,MAAM;IACJqF,MAAM,EAAE;MACNnF,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,EAA6B,OAA7B;IADJ,CADJ;IAIJ6D,QAAQ,EAAE;MACRpF,QAAQ,EAAG,YAAY;QACrB,MAAMqF,MAAM,GAAG,IAAA9D,qBAAA,EAAe,YAAf,EAA6B,aAA7B,CAAf;QACA,MAAMmD,QAAQ,GAAG,IAAAnD,qBAAA,EAAe,YAAf,CAAjB;;QAEA,MAAMH,SAAoB,GAAG,UAC3BH,IAD2B,EAE3BC,GAF2B,EAG3BC,GAH2B,EAI3B;UACA,MAAMC,SAAoB,GAAGH,IAAI,CAACyD,QAAL,GAAgBA,QAAhB,GAA2BW,MAAxD;UACAjE,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAPD;;QASAC,SAAS,CAACW,cAAV,GAA2B,CAAC,YAAD,EAAe,YAAf,EAA6B,aAA7B,CAA3B;QACA,OAAOX,SAAP;MACD,CAfS;IADF,CAJN;IAsBJsD,QAAQ,EAAE;MACRrE,OAAO,EAAE;IADD;EAtBN,GAyBA,CAACC,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACA;IACE4B,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAc,kBAAA,EAAY,IAAZ,EAAkB,KAAlB,CADF;MAERsB,QAAQ,EAAE;IAFF;EADZ,CADA,GAOA,EAhCA;AATuB,CAArB,CAAV;AA6CAxC,UAAU,CAAC,eAAD,EAAkB;EAAEmE,QAAQ,EAAE;AAAZ,CAAlB,CAAV;AAEAnE,UAAU,CAAC,SAAD,EAAY;EAGpBc,OAAO,EAAE,CAAC,YAAD,EAAe,MAAf,CAHW;EAIpBe,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,EAAuB,YAAvB,EAAqC,aAArC,CAJW;EAKpB3B,MAAM,EAAE;IACNwF,UAAU,EAAE;MACVtF,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADA,CADN;IAINqF,UAAU,EAAE;MACVvF,QAAQ,EAAE,IAAAc,kBAAA,EAAY,QAAZ,EAAsB,QAAtB,CADA;MAEVT,OAAO,EAAE;IAFC,CAJN;IAQNmF,WAAW,EAAE;MACXxF,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,sBAAf,CADC;MAEXlB,OAAO,EAAE,IAFE;MAGX+B,QAAQ,EAAE;IAHC,CARP;IAaNH,UAAU,EAAE;MACVjC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVlB,OAAO,EAAE;IALC,CAbN;IAoBN6B,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ;IADN;EApBA,CALY;EAgCpBZ,OAAO,EAAE,CAAC,UAAD,EAAa,aAAb,EAA4B,OAA5B;AAhCW,CAAZ,CAAV;AAmCAf,UAAU,CAAC,kBAAD,EAAqB;EAC7Bc,OAAO,EAAE,CAAC,YAAD,CADoB;EAE7BC,OAAO,EAAE,CAAC,YAAD,CAFoB;EAG7Bb,MAAM,EAAE;IACN2F,UAAU,EAAE;MACVzF,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EAAe,cAAf,EAA+B,gBAA/B,EAAiD,eAAjD,CADF,CAFQ;IADA;EADN;AAHqB,CAArB,CAAV;AAeA3B,UAAU,CAAC,cAAD,EAAiB;EACzB6B,OAAO,EAAE,CAAC,MAAD,EAAS,KAAT,EAAgB,QAAhB,EAA0B,MAA1B,EAAkC,UAAlC,EAA8C,WAA9C,EAA2D,OAA3D,CADgB;EAEzB3B,MAAM,oBACDsD,cAAc,EADb,EAEDI,4BAA4B,EAF3B;IAGJkC,IAAI;MACF1F,QAAQ,EAAE,IAAAc,kBAAA,EAAY,QAAZ,EAAsB,KAAtB,EAA6B,KAA7B;IADR,GAEE,CAACR,OAAO,CAACC,GAAR,CAAYC,sBAAb,GAAsC;MAAEH,OAAO,EAAE;IAAX,CAAtC,GAA8D,EAFhE,CAHA;IAOJqE,QAAQ,EAAE;MACRrE,OAAO,EAAE;IADD,CAPN;IAUJa,GAAG,EAAE;MACHlB,QAAQ,EAAG,YAAY;QACrB,MAAMqF,MAAM,GAAG,IAAA9D,qBAAA,EACb,YADa,EAEb,eAFa,EAGb,gBAHa,EAIb,eAJa,CAAf;QAMA,MAAMmD,QAAQ,GAAG,IAAAnD,qBAAA,EAAe,YAAf,CAAjB;;QAEA,MAAMH,SAAoB,GAAG,UAAUH,IAAV,EAAgCC,GAAhC,EAAqCC,GAArC,EAA0C;UACrE,MAAMC,SAAS,GAAGH,IAAI,CAACyD,QAAL,GAAgBA,QAAhB,GAA2BW,MAA7C;UACAjE,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAHD;;QAKAC,SAAS,CAACW,cAAV,GAA2B,CACzB,YADyB,EAEzB,YAFyB,EAGzB,eAHyB,EAIzB,gBAJyB,EAKzB,eALyB,CAA3B;QAOA,OAAOX,SAAP;MACD,CAtBS;IADP,CAVD;IAmCJ8C,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CAnCR;IA0CJF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IADN;EA1CF,EAFmB;EAgDzBb,OAAO,EAAE,CACP,KADO,EAEP,QAFO,EAGP,MAHO,EAIP,YAJO,EAKP,YALO,EAMP,gBANO,CAhDgB;EAwDzBC,OAAO,EAAE,CACP,mBADO,EAEP,UAFO,EAGP,UAHO,EAIP,aAJO,EAKP,gBALO,EAMP,QANO,EAOP,cAPO;AAxDgB,CAAjB,CAAV;AAmEAf,UAAU,CAAC,gBAAD,EAAmB;EAC3B6B,OAAO,EAAE,CACP,KADO,EAEP,OAFO,EAGP,UAHO,EAIP,WAJO,EAKP,IAAI,CAACnB,OAAO,CAACC,GAAR,CAAYC,sBAAb,GAAsC,CAAC,YAAD,CAAtC,GAAuD,EAA3D,CALO,CADkB;EAQ3BV,MAAM,EAAE;IACN4E,QAAQ,EAAE;MACRrE,OAAO,EAAE;IADD,CADJ;IAINa,GAAG,EAAE;MACHlB,QAAQ,EAAG,YAAY;QACrB,MAAMqF,MAAM,GAAG,IAAA9D,qBAAA,EACb,YADa,EAEb,eAFa,EAGb,gBAHa,EAIb,eAJa,EAKb,gBALa,EAMb,aANa,CAAf;QAQA,MAAMmD,QAAQ,GAAG,IAAAnD,qBAAA,EAAe,YAAf,CAAjB;QAEA,MAAMH,SAAoB,GAAGS,MAAM,CAACC,MAAP,CAC3B,UAAUb,IAAV,EAAkCC,GAAlC,EAAuCC,GAAvC,EAA4C;UAC1C,MAAMC,SAAS,GAAGH,IAAI,CAACyD,QAAL,GAAgBA,QAAhB,GAA2BW,MAA7C;UACAjE,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAJ0B,EAK3B;UAEEY,cAAc,EAAE,CACd,YADc,EAEd,YAFc,EAGd,eAHc,EAId,gBAJc,EAKd,eALc,EAMd,gBANc,EAOd,aAPc;QAFlB,CAL2B,CAA7B;QAkBA,OAAOX,SAAP;MACD,CA9BS;IADP,CAJC;IAqCNY,KAAK,EAAE;MAGLhC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,EAA6B,aAA7B;IAHL,CArCD;IA0CNoE,SAAS,EAAE;MACT3F,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,SAAhB,CADQ,EAER2B,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAAkCC,GAAlC,EAAuCC,GAAvC,EAA4C;QAC1C,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAEzC,IAAIW,GAAG,IAAIF,IAAI,CAACyD,QAAhB,EAA0B;UACxB,MAAM,IAAIL,SAAJ,CACJ,yEADI,CAAN;QAGD;MACF,CATH,EAUE;QAAEpB,IAAI,EAAE;MAAR,CAVF,CAFQ,EAcR,UAAUhC,IAAV,EAAkCC,GAAlC,EAAuCC,GAAvC,EAA4C;QAC1C,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAEzC,IAAIW,GAAG,IAAI,CAAC,IAAAE,WAAA,EAAG,YAAH,EAAiBJ,IAAI,CAACC,GAAtB,CAAZ,EAAwC;UACtC,MAAM,IAAImD,SAAJ,CACJ,iFADI,CAAN;QAGD;MACF,CAtBO,CADD;MAyBThE,OAAO,EAAE;IAzBA,CA1CL;IAqEN6D,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA;EArEN,CARmB;EAqF3B1B,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,YAAjB,CArFkB;EAsF3BC,OAAO,EAAE,CAAC,mBAAD,EAAsB,UAAtB,EAAkC,cAAlC,CAtFkB;EAuF3BX,QAAQ,EAAG,YAAY;IACrB,MAAMgB,OAAO,GAAG,IAAAO,qBAAA,EACd,YADc,EAEd,SAFc,EAGd,gBAHc,EAId,qBAJc,EAKd,iBALc,CAAhB;IAOA,MAAMI,UAAU,GAAG,IAAAJ,qBAAA,EAAe,YAAf,CAAnB;IAEA,OAAO,UAAUuC,MAAV,EAAkB5C,GAAlB,EAAuBD,IAAvB,EAA6B;MAClC,IAAI,CAACX,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;MAEzC,MAAMY,SAAS,GAAG,IAAAC,WAAA,EAAG,eAAH,EAAoByC,MAApB,IAA8B9C,OAA9B,GAAwCW,UAA1D;MACAP,SAAS,CAACH,IAAD,EAAO,OAAP,EAAgBA,IAAI,CAACe,KAArB,CAAT;IACD,CALD;EAMD,CAhBS;AAvFiB,CAAnB,CAAV;AA0GApC,UAAU,CAAC,aAAD,EAAgB;EACxBc,OAAO,EAAE,CAAC,UAAD,EAAa,gBAAb,CADe;EAExBe,OAAO,EAAE,CAAC,UAAD,CAFe;EAGxBd,OAAO,EAAE,CAAC,MAAD,EAAS,aAAT,CAHe;EAIxBoE,eAAe,EAAE,cAJO;EAKxBjF,MAAM,oBACDkE,iBAAiB,EADhB;IAEJ4B,QAAQ,EAAE;MACR5F,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACN,IAAAe,qBAAA,EAAe,MAAf,CADM,GAEN,IAAAA,qBAAA,EACE,YADF,EAEE,cAFF,EAGE,eAHF,EAIE,kBAJF,EAKE,gBALF,EAME,iBANF,EAOE,qBAPF;IAHI,CAFN;IAgBJa,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF;EAhBN,EALkB;;EA0BxBpC,QAAQ,CAAC8D,MAAD,EAA2C5C,GAA3C,EAAgD;IACtD,IAAI,CAACZ,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;IAEzC,MAAM8D,KAAK,GAAG,iBAAiBC,IAAjB,CAAsBrD,GAAtB,CAAd;IACA,IAAI,CAACoD,KAAL,EAAY,MAAM,IAAIuB,KAAJ,CAAU,sCAAV,CAAN;IAEZ,MAAM,GAAGC,OAAH,EAAYC,KAAZ,IAAqBzB,KAA3B;;IAKA,IAAKR,MAAM,CAACgC,OAAD,CAAP,CAA8BE,MAA9B,GAAuC,CAACD,KAAD,GAAS,CAApD,EAAuD;MACrD,MAAM,IAAI1B,SAAJ,CAAe,uCAAsCyB,OAAQ,EAA7D,CAAN;IACD;EACF;;AAxCuB,CAAhB,CAAV;AA2CAlG,UAAU,CAAC,iBAAD,EAAoB;EAC5Bc,OAAO,EAAE,CAAC,UAAD,CADmB;EAE5BC,OAAO,EAAE,CAAC,WAAD,EAAc,gBAAd,EAAgC,qBAAhC,CAFmB;EAG5Bb,MAAM,EAAE;IACN8F,QAAQ,EAAE;MACR5F,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADF;MAERa,QAAQ,EAAE;IAFF;EADJ;AAHoB,CAApB,CAAV;AAWAxC,UAAU,CAAC,oBAAD,EAAuB;EAC/Bc,OAAO,EAAE,CAAC,aAAD,CADsB;EAE/BZ,MAAM,EAAE;IACNmG,WAAW,EAAE;MACXjG,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,YAAf,CAAX,CAFQ;IADC;EADP,CAFuB;EAU/BZ,OAAO,EAAE,CAAC,YAAD;AAVsB,CAAvB,CAAV;AAaAf,UAAU,CAAC,yBAAD,EAA4B;EACpCc,OAAO,EAAE,CAAC,YAAD,CAD2B;EAEpCC,OAAO,EAAE,CAAC,YAAD,EAAe,mBAAf,CAF2B;EAGpCb,MAAM,EAAE;IACN6B,UAAU,EAAE;MACV3B,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADA;EADN;AAH4B,CAA5B,CAAV;AAUA3B,UAAU,CAAC,YAAD,EAAe;EACvBc,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,CADc;EAEvBZ,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADN;MAEJa,QAAQ,EAAE;IAFN,CADA;IAKNO,UAAU,EAAE;MACV3C,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ;IADA;EALN;AAFe,CAAf,CAAV;AAgBA3B,UAAU,CAAC,iBAAD,EAAoB;EAC5Bc,OAAO,EAAE,CAAC,cAAD,EAAiB,OAAjB,CADmB;EAE5BC,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,EAA6B,UAA7B,CAFmB;EAG5Bb,MAAM,EAAE;IACNoG,YAAY,EAAE;MACZlG,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADE,CADR;IAIN4E,KAAK,EAAE;MACLnG,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,YAAf,CAAX,CAFQ;IADL;EAJD;AAHoB,CAApB,CAAV;AAgBA3B,UAAU,CAAC,gBAAD,EAAmB;EAC3Be,OAAO,EAAE,CAAC,YAAD;AADkB,CAAnB,CAAV;AAIAf,UAAU,CAAC,gBAAD,EAAmB;EAC3Bc,OAAO,EAAE,CAAC,UAAD,CADkB;EAE3BC,OAAO,EAAE,CAAC,WAAD,EAAc,gBAAd,EAAgC,qBAAhC,CAFkB;EAG3Bb,MAAM,EAAE;IACN8F,QAAQ,EAAE;MACR5F,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADF;EADJ;AAHmB,CAAnB,CAAV;AAUA3B,UAAU,CAAC,cAAD,EAAiB;EACzBc,OAAO,EAAE,CAAC,OAAD,EAAU,SAAV,EAAqB,WAArB,CADgB;EAEzBC,OAAO,EAAE,CAAC,WAAD,CAFgB;EAGzBb,MAAM,EAAE;IACNsG,KAAK,EAAE;MACLpG,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAsB,qBAAA,EAAe,gBAAf,CADQ,EAERM,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAAgC;QAC9B,IAAI,CAACX,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAKzC,IAAI,CAACS,IAAI,CAACoF,OAAN,IAAiB,CAACpF,IAAI,CAACqF,SAA3B,EAAsC;UACpC,MAAM,IAAIjC,SAAJ,CACJ,6DADI,CAAN;QAGD;MACF,CAZH,EAaE;QACEtC,cAAc,EAAE,CAAC,gBAAD;MADlB,CAbF,CAFQ;IADL,CADD;IAuBNsE,OAAO,EAAE;MACPjE,QAAQ,EAAE,IADH;MAEPpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,aAAf;IAFH,CAvBH;IA2BN+E,SAAS,EAAE;MACTlE,QAAQ,EAAE,IADD;MAETpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IAFD;EA3BL;AAHiB,CAAjB,CAAV;AAqCA3B,UAAU,CAAC,iBAAD,EAAoB;EAC5B6B,OAAO,EAAE,CAAC,UAAD,EAAa,UAAb,EAAyB,QAAzB,CADmB;EAE5B3B,MAAM,EAAE;IACNyG,MAAM,EAAE;MACNlG,OAAO,EAAE;IADH,CADF;IAINuF,QAAQ,EAAE;MACR5F,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADF,CAJJ;IAONX,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAA,EAAY,GAAG0F,0BAAf;IADF;EAPJ,CAFoB;EAa5B9F,OAAO,EAAE,CAAC,UAAD,CAbmB;EAc5BC,OAAO,EAAE,CAAC,WAAD,EAAc,YAAd;AAdmB,CAApB,CAAV;AAiBAf,UAAU,CAAC,kBAAD,EAAqB;EAC7B6B,OAAO,EAAE,CAAC,UAAD,EAAa,UAAb,EAAyB,QAAzB,CADoB;EAE7B3B,MAAM,EAAE;IACNyG,MAAM,EAAE;MACNlG,OAAO,EAAE;IADH,CADF;IAINuF,QAAQ,EAAE;MACR5F,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACN,IAAAe,qBAAA,EAAe,YAAf,CADM,GAEN,IAAAA,qBAAA,EAAe,YAAf,EAA6B,kBAA7B;IAHI,CAJJ;IASNX,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAA,EAAY,GAAG2F,2BAAf;IADF;EATJ,CAFqB;EAe7B/F,OAAO,EAAE,CAAC,UAAD,CAfoB;EAgB7BC,OAAO,EAAE,CAAC,YAAD;AAhBoB,CAArB,CAAV;AAmBAf,UAAU,CAAC,qBAAD,EAAwB;EAChC6B,OAAO,EAAE,CAAC,MAAD,EAAS,cAAT,CADuB;EAEhCf,OAAO,EAAE,CAAC,cAAD,CAFuB;EAGhCC,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CAHuB;EAIhCb,MAAM,EAAE;IACN6D,OAAO,EAAE;MACP3D,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADH;MAEPkC,QAAQ,EAAE;IAFH,CADH;IAKNsD,IAAI,EAAE;MACJ1F,QAAQ,EAAE,IAAAc,kBAAA,EAAY,KAAZ,EAAmB,KAAnB,EAA0B,OAA1B;IADN,CALA;IAQN4F,YAAY,EAAE;MACZ1G,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,oBAAf,CAAX,CAFQ;IADE;EARR,CAJwB;;EAmBhCvB,QAAQ,CAAC8D,MAAD,EAAS5C,GAAT,EAAcD,IAAd,EAAoB;IAC1B,IAAI,CAACX,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;IAEzC,IAAI,CAAC,IAAAa,WAAA,EAAG,eAAH,EAAoByC,MAApB,EAA4B;MAAExC,IAAI,EAAEL;IAAR,CAA5B,CAAL,EAAkD;;IAClD,IAAIA,IAAI,CAACyF,YAAL,CAAkBV,MAAlB,KAA6B,CAAjC,EAAoC;MAClC,MAAM,IAAI3B,SAAJ,CACH,8EAA6EP,MAAM,CAACb,IAAK,EADtF,CAAN;IAGD;EACF;;AA5B+B,CAAxB,CAAV;AA+BArD,UAAU,CAAC,oBAAD,EAAuB;EAC/Bc,OAAO,EAAE,CAAC,IAAD,EAAO,MAAP,CADsB;EAE/BZ,MAAM,EAAE;IACN8D,EAAE,EAAE;MACF5D,QAAQ,EAAG,YAAY;QACrB,IAAI,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;UACvC,OAAO,IAAAe,qBAAA,EAAe,MAAf,CAAP;QACD;;QAED,MAAM8D,MAAM,GAAG,IAAA9D,qBAAA,EACb,YADa,EAEb,cAFa,EAGb,eAHa,CAAf;QAKA,MAAMoF,OAAO,GAAG,IAAApF,qBAAA,EAAe,YAAf,CAAhB;QAEA,OAAO,UAAUN,IAAV,EAAsCC,GAAtC,EAA2CC,GAA3C,EAAgD;UACrD,MAAMC,SAAS,GAAGH,IAAI,CAACiC,IAAL,GAAYmC,MAAZ,GAAqBsB,OAAvC;UACAvF,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAHD;MAID,CAhBS;IADR,CADE;IAoBNyF,QAAQ,EAAE;MACRxE,QAAQ,EAAE,IADF;MAERpC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB;IAFF,CApBJ;IAwBNgD,IAAI,EAAE;MACJd,QAAQ,EAAE,IADN;MAEJpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IAFN;EAxBA;AAFuB,CAAvB,CAAV;AAiCA3B,UAAU,CAAC,gBAAD,EAAmB;EAC3Bc,OAAO,EAAE,CAAC,MAAD,EAAS,MAAT,CADkB;EAE3BC,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,EAA6B,MAA7B,EAAqC,OAArC,EAA8C,UAA9C,CAFkB;EAG3Bb,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADN,CADA;IAINW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN;EAJA;AAHmB,CAAnB,CAAV;AAaA3B,UAAU,CAAC,eAAD,EAAkB;EAC1Bc,OAAO,EAAE,CAAC,QAAD,EAAW,MAAX,CADiB;EAE1BC,OAAO,EAAE,CAAC,WAAD,CAFiB;EAG1Bb,MAAM,EAAE;IACNqF,MAAM,EAAE;MACNnF,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADJ,CADF;IAINW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN;EAJA;AAHkB,CAAlB,CAAV;AAcA3B,UAAU,CAAC,mBAAD,EAAsB;EAC9Bc,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,EAAkB,YAAlB,CADqB;EAE9Be,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CAFqB;EAG9Bd,OAAO,EAAE,CAAC,SAAD,EAAY,aAAZ,EAA2B,MAA3B,CAHqB;EAI9Bb,MAAM,oBACDkE,iBAAiB,EADhB;IAEJ1C,IAAI,EAAE;MACJtB,QAAQ,EAAE,IAAAuB,qBAAA,EACR,YADQ,EAER,eAFQ,EAGR,cAHQ,EAIR,kBAJQ,EAKR,gBALQ,EAMR,iBANQ,EAOR,qBAPQ;IADN,CAFF;IAaJC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL,CAbH;IAiBJ2C,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA;EAjBR;AAJwB,CAAtB,CAAV;AA+BAxC,UAAU,CAAC,cAAD,EAAiB;EACzBc,OAAO,EAAE,CAAC,UAAD,EAAa,gBAAb,CADgB;EAEzBe,OAAO,EAAE,CAAC,UAAD,CAFgB;EAGzBd,OAAO,EAAE,CAAC,SAAD,EAAY,aAAZ,EAA2B,MAA3B,CAHgB;EAIzBb,MAAM,oBACDkE,iBAAiB,EADhB;IAEJjE,QAAQ,EAAE;MACRC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAC,4BAAA,EAAsB,MAAtB,EAA8B,aAA9B,EAA6C,MAA7C,CAAX,CAFQ;IADF,CAFN;IASJ8D,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CATR;IAgBJA,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF;EAhBN;AAJmB,CAAjB,CAAV;AA2BAxC,UAAU,CAAC,yBAAD,EAA4B;EACpC6B,OAAO,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,CAD2B;EAEpCf,OAAO,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,YAAnB,EAAiC,gBAAjC,CAF2B;EAGpCC,OAAO,EAAE,CACP,UADO,EAEP,UAFO,EAGP,aAHO,EAIP,gBAJO,EAKP,YALO,EAMP,SANO,CAH2B;EAWpCb,MAAM,oBACDsD,cAAc,EADb,EAEDI,4BAA4B,EAF3B;IAGJ7B,UAAU,EAAE;MAEV3B,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB;IAFA,CAHR;IAOJgC,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf,EAAiC,YAAjC;IADN,CAPF;IAUJsC,SAAS,EAAE;MACT7D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,mBAAf,EAAoC,mBAApC,CADD;MAETa,QAAQ,EAAE;IAFD;EAVP;AAX8B,CAA5B,CAAV;AA4BAxC,UAAU,CAAC,WAAD,EAAc;EACtBc,OAAO,EAAE,CAAC,MAAD,CADa;EAEtBZ,MAAM,EAAE;IACNoC,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EACE,aADF,EAEE,oBAFF,EAGE,eAHF,EAIE,sBAJF,EAKE,uBALF,EAME,iBANF,EAOE,kBAPF,EAQE,aARF,CADF,CAFQ;IADN;EADA;AAFc,CAAd,CAAV;AAuBA3B,UAAU,CAAC,iBAAD,EAAoB;EAC5B6B,OAAO,EAAE,CAAC,IAAD,EAAO,YAAP,EAAqB,MAArB,EAA6B,YAA7B,CADmB;EAE5Bf,OAAO,EAAE,CACP,IADO,EAEP,MAFO,EAGP,YAHO,EAIP,QAJO,EAKP,gBALO,EAMP,qBANO,EAOP,YAPO,EAQP,YARO,CAFmB;EAY5BC,OAAO,EAAE,CAAC,UAAD,EAAa,OAAb,EAAsB,YAAtB,CAZmB;EAa5Bb,MAAM,EAAE;IACN8D,EAAE,EAAE;MACF5D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADR;MAIFa,QAAQ,EAAE;IAJR,CADE;IAONI,cAAc,EAAE;MACdxC,QAAQ,EAKJ,IAAAuB,qBAAA,EACE,0BADF,EAEE,4BAFF,EAIE,MAJF,CANU;MAYda,QAAQ,EAAE;IAZI,CAPV;IAqBNF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN,CArBA;IAwBNsF,UAAU,EAAE;MACVzE,QAAQ,EAAE,IADA;MAEVpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IAFA,CAxBN;IA4BNuF,mBAAmB,EAAE;MACnB9G,QAAQ,EAAE,IAAAuB,qBAAA,EACR,4BADQ,EAER,8BAFQ,CADS;MAKnBa,QAAQ,EAAE;IALS,CA5Bf;IAmCN2E,UAAU,EAAE;MACV/G,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EAAe,+BAAf,EAAgD,iBAAhD,CADF,CAFQ,CADA;MAOVa,QAAQ,EAAE;IAPA,CAnCN;IA4CN8B,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CA5CN;IAmDN4E,MAAM,EAAE;MACNhH,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,kBAAf,CADJ;MAENa,QAAQ,EAAE;IAFJ;EAnDF;AAboB,CAApB,CAAV;AAuEAxC,UAAU,CAAC,kBAAD,EAAqB;EAC7BmE,QAAQ,EAAE,iBADmB;EAE7BpD,OAAO,EAAE,CAAC,UAAD,EAAa,OAAb,EAAsB,WAAtB,EAAmC,aAAnC,CAFoB;EAG7Bb,MAAM,EAAE;IACN8D,EAAE,EAAE;MACF5D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADR,CADE;IAINiB,cAAc,EAAE;MACdxC,QAAQ,EAKJ,IAAAuB,qBAAA,EACE,0BADF,EAEE,4BAFF,EAIE,MAJF,CANU;MAYda,QAAQ,EAAE;IAZI,CAJV;IAkBNF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN,CAlBA;IAqBNsF,UAAU,EAAE;MACVzE,QAAQ,EAAE,IADA;MAEVpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IAFA,CArBN;IAyBNuF,mBAAmB,EAAE;MACnB9G,QAAQ,EAAE,IAAAuB,qBAAA,EACR,4BADQ,EAER,8BAFQ,CADS;MAKnBa,QAAQ,EAAE;IALS,CAzBf;IAgCN2E,UAAU,EAAE;MACV/G,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EAAe,+BAAf,EAAgD,iBAAhD,CADF,CAFQ,CADA;MAOVa,QAAQ,EAAE;IAPA,CAhCN;IAyCN8B,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CAzCN;IAgDN4E,MAAM,EAAE;MACNhH,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,kBAAf,CADJ;MAENa,QAAQ,EAAE;IAFJ,CAhDF;IAoDNuB,OAAO,EAAE;MACP3D,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADH;MAEPkC,QAAQ,EAAE;IAFH,CApDH;IAwDN6E,QAAQ,EAAE;MACRjH,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF;EAxDJ,CAHqB;EAgE7BpC,QAAQ,EAAG,YAAY;IACrB,MAAMa,UAAU,GAAG,IAAAU,qBAAA,EAAe,YAAf,CAAnB;IAEA,OAAO,UAAUuC,MAAV,EAAkB5C,GAAlB,EAAuBD,IAAvB,EAA6B;MAClC,IAAI,CAACX,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;MAEzC,IAAI,CAAC,IAAAa,WAAA,EAAG,0BAAH,EAA+ByC,MAA/B,CAAL,EAA6C;QAC3CjD,UAAU,CAACI,IAAD,EAAO,IAAP,EAAaA,IAAI,CAAC2C,EAAlB,CAAV;MACD;IACF,CAND;EAOD,CAVS;AAhEmB,CAArB,CAAV;AA6EAhE,UAAU,CAAC,sBAAD,EAAyB;EACjCc,OAAO,EAAE,CAAC,QAAD,CADwB;EAEjCC,OAAO,EAAE,CACP,WADO,EAEP,aAFO,EAGP,mBAHO,EAIP,mBAJO,CAFwB;EAQjCb,MAAM,EAAE;IACNoH,MAAM,EAAE;MACNlH,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,eAAf;IADJ,CADF;IAIN4F,UAAU,EAAE,IAAAC,uBAAA,EAAiB,IAAAtG,kBAAA,EAAY,MAAZ,EAAoB,OAApB,CAAjB,CAJN;IAKNuG,UAAU,EAAE;MACVjF,QAAQ,EAAE,IADA;MAEVpC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,iBAAf,CAAX,CAFQ;IAFA;EALN;AARyB,CAAzB,CAAV;AAuBA3B,UAAU,CAAC,0BAAD,EAA6B;EACrCc,OAAO,EAAE,CAAC,aAAD,CAD4B;EAErCC,OAAO,EAAE,CACP,WADO,EAEP,aAFO,EAGP,mBAHO,EAIP,mBAJO,CAF4B;EAQrCb,MAAM,EAAE;IACNwH,WAAW,EAAE;MACXtH,QAAQ,EAAE,IAAAuB,qBAAA,EACR,mBADQ,EAER,qBAFQ,EAGR,kBAHQ,EAIR,YAJQ;IADC,CADP;IASN4F,UAAU,EAAE,IAAAC,uBAAA,EAAiB,IAAAtG,kBAAA,EAAY,OAAZ,CAAjB;EATN;AAR6B,CAA7B,CAAV;AAqBAlB,UAAU,CAAC,wBAAD,EAA2B;EACnCc,OAAO,EAAE,CAAC,aAAD,EAAgB,YAAhB,EAA8B,QAA9B,CAD0B;EAEnCC,OAAO,EAAE,CACP,WADO,EAEP,aAFO,EAGP,mBAHO,EAIP,mBAJO,CAF0B;EAQnCb,MAAM,EAAE;IACNwH,WAAW,EAAE;MACXlF,QAAQ,EAAE,IADC;MAEXpC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAsB,qBAAA,EAAe,aAAf,CADQ,EAERM,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAA0CC,GAA1C,EAA+CC,GAA/C,EAAoD;QAClD,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAKzC,IAAIW,GAAG,IAAIF,IAAI,CAACsG,UAAL,CAAgBvB,MAA3B,EAAmC;UACjC,MAAM,IAAI3B,SAAJ,CACJ,qEADI,CAAN;QAGD;MACF,CAZH,EAaE;QAAEtC,cAAc,EAAE,CAAC,aAAD;MAAlB,CAbF,CAFQ,EAiBR,UAAUd,IAAV,EAA0CC,GAA1C,EAA+CC,GAA/C,EAAoD;QAClD,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAKzC,IAAIW,GAAG,IAAIF,IAAI,CAACiG,MAAhB,EAAwB;UACtB,MAAM,IAAI7C,SAAJ,CAAc,2CAAd,CAAN;QACD;MACF,CA1BO;IAFC,CADP;IAgCNgD,UAAU,EAAE;MACVjF,QAAQ,EAAE,IADA;MAEVpC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,iBAAf,CAAX,CAFQ;IAFA,CAhCN;IAuCNgG,UAAU,EAAE;MACVlH,OAAO,EAAE,EADC;MAEVL,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACG,YAAY;QACX,MAAMqH,OAAO,GAAG,IAAAjG,qBAAA,EACd,iBADc,EAEd,wBAFc,EAGd,0BAHc,CAAhB;QAKA,MAAMkG,UAAU,GAAG,IAAAlG,qBAAA,EAAe,iBAAf,CAAnB;QAEA,IAAI,CAACjB,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC,OAAOgH,OAAP;QAEzC,OAAO,UAAUvG,IAAV,EAA0CC,GAA1C,EAA+CC,GAA/C,EAAoD;UACzD,MAAMC,SAAS,GAAGH,IAAI,CAACiG,MAAL,GAAcM,OAAd,GAAwBC,UAA1C;UACArG,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAHD;MAID,CAdD,EADF,CAFQ;IAFA,CAvCN;IA8DN+F,MAAM,EAAE;MACNlH,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,eAAf,CADJ;MAENa,QAAQ,EAAE;IAFJ,CA9DF;IAkEN+E,UAAU,EAAE,IAAAC,uBAAA,EAAiB,IAAAtG,kBAAA,EAAY,MAAZ,EAAoB,OAApB,CAAjB;EAlEN;AAR2B,CAA3B,CAAV;AA8EAlB,UAAU,CAAC,iBAAD,EAAoB;EAC5Bc,OAAO,EAAE,CAAC,OAAD,EAAU,UAAV,CADmB;EAE5BC,OAAO,EAAE,CAAC,iBAAD,CAFmB;EAG5Bb,MAAM,EAAE;IACN4H,KAAK,EAAE;MACL1H,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL,CADD;IAINoG,QAAQ,EAAE;MACR3H,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,EAA6B,eAA7B;IADF,CAJJ;IAON4F,UAAU,EAAE;MAEVnH,QAAQ,EAAE,IAAAc,kBAAA,EAAY,MAAZ,EAAoB,OAApB,CAFA;MAGVsB,QAAQ,EAAE;IAHA;EAPN;AAHoB,CAApB,CAAV;AAkBAxC,UAAU,CAAC,gBAAD,EAAmB;EAC3Bc,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,EAAkB,MAAlB,CADkB;EAE3Be,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,EAAkB,MAAlB,EAA0B,OAA1B,CAFkB;EAG3Bd,OAAO,EAAE,CACP,UADO,EAEP,WAFO,EAGP,KAHO,EAIP,aAJO,EAKP,MALO,EAMP,eANO,CAHkB;EAW3Bb,MAAM,EAAE;IACNwB,IAAI,EAAE;MACJtB,QAAQ,EAAG,YAAY;QACrB,IAAI,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;UACvC,OAAO,IAAAe,qBAAA,EAAe,qBAAf,EAAsC,MAAtC,CAAP;QACD;;QAED,MAAM+F,WAAW,GAAG,IAAA/F,qBAAA,EAAe,qBAAf,CAApB;QACA,MAAMqG,IAAI,GAAG,IAAArG,qBAAA,EACX,YADW,EAEX,kBAFW,EAGX,cAHW,EAIX,eAJW,EAKX,gBALW,EAMX,iBANW,EAOX,qBAPW,CAAb;QAUA,OAAO,UAAUN,IAAV,EAAgBC,GAAhB,EAAqBC,GAArB,EAA0B;UAC/B,IAAI,IAAAE,WAAA,EAAG,qBAAH,EAA0BF,GAA1B,CAAJ,EAAoC;YAClCmG,WAAW,CAACrG,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAX;UACD,CAFD,MAEO;YACLyG,IAAI,CAAC3G,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAJ;UACD;QACF,CAND;MAOD,CAvBS;IADN,CADA;IA2BNK,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL,CA3BD;IA8BNW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,WAAf;IADN,CA9BA;IAiCNsG,KAAK,EAAE;MACLxH,OAAO,EAAE;IADJ;EAjCD;AAXmB,CAAnB,CAAV;AAkDAT,UAAU,CAAC,mBAAD,EAAsB;EAC9Bc,OAAO,EAAE,CAAC,YAAD,EAAe,QAAf,CADqB;EAE9BC,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,EAA6B,mBAA7B,CAFqB;EAG9Bb,MAAM,EAAE;IACNuH,UAAU,EAAE;MACVjF,QAAQ,EAAE,IADA;MAEVpC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,iBAAf,CAAX,CAFQ;IAFA,CADN;IAQNgG,UAAU,EAAE;MACVvH,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EACE,iBADF,EAEE,wBAFF,EAGE,0BAHF,CADF,CAFQ;IADA,CARN;IAoBN2F,MAAM,EAAE;MACNlH,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,eAAf;IADJ,CApBF;IAuBNuG,UAAU,EAAE;MAGV9H,QAAQ,EAAE,IAAAc,kBAAA,EAAY,MAAZ,EAAoB,QAApB,EAA8B,OAA9B,CAHA;MAIVsB,QAAQ,EAAE;IAJA;EAvBN;AAHsB,CAAtB,CAAV;AAmCAxC,UAAU,CAAC,wBAAD,EAA2B;EACnCc,OAAO,EAAE,CAAC,OAAD,CAD0B;EAEnCC,OAAO,EAAE,CAAC,iBAAD,CAF0B;EAGnCb,MAAM,EAAE;IACN4H,KAAK,EAAE;MACL1H,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL;EADD;AAH2B,CAA3B,CAAV;AAUA3B,UAAU,CAAC,0BAAD,EAA6B;EACrCc,OAAO,EAAE,CAAC,OAAD,CAD4B;EAErCC,OAAO,EAAE,CAAC,iBAAD,CAF4B;EAGrCb,MAAM,EAAE;IACN4H,KAAK,EAAE;MACL1H,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL;EADD;AAH6B,CAA7B,CAAV;AAUA3B,UAAU,CAAC,iBAAD,EAAoB;EAC5Bc,OAAO,EAAE,CAAC,OAAD,EAAU,UAAV,CADmB;EAE5BC,OAAO,EAAE,CAAC,iBAAD,CAFmB;EAG5Bb,MAAM,EAAE;IACN4H,KAAK,EAAE;MACL1H,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADL,CADD;IAINoD,QAAQ,EAAE;MACR3E,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,EAA6B,eAA7B;IADF,CAJJ;IAONuG,UAAU,EAAE;MAGV9H,QAAQ,EAAE,IAAAc,kBAAA,EAAY,MAAZ,EAAoB,QAApB,EAA8B,OAA9B,CAHA;MAIVsB,QAAQ,EAAE;IAJA;EAPN;AAHoB,CAApB,CAAV;AAmBAxC,UAAU,CAAC,cAAD,EAAiB;EACzBc,OAAO,EAAE,CAAC,MAAD,EAAS,UAAT,CADgB;EAEzBC,OAAO,EAAE,CAAC,YAAD,CAFgB;EAGzBb,MAAM,EAAE;IACN8E,IAAI,EAAE;MACJ5E,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAsB,qBAAA,EAAe,YAAf,CADQ,EAERM,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAAgCC,GAAhC,EAAqCC,GAArC,EAA0C;QACxC,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;QAEzC,IAAI4E,QAAJ;;QACA,QAAQjE,GAAG,CAACgD,IAAZ;UACE,KAAK,UAAL;YACEiB,QAAQ,GAAG,MAAX;YACA;;UACF,KAAK,KAAL;YACEA,QAAQ,GAAG,QAAX;YACA;;UACF,KAAK,QAAL;YACEA,QAAQ,GAAG,MAAX;YACA;QATJ;;QAWA,IAAI,CAAC,IAAA/D,WAAA,EAAG,YAAH,EAAiBJ,IAAI,CAACmE,QAAtB,EAAgC;UAAEjB,IAAI,EAAEiB;QAAR,CAAhC,CAAL,EAA0D;UACxD,MAAM,IAAIf,SAAJ,CAAc,2BAAd,CAAN;QACD;MACF,CAnBH,EAoBE;QAAEtC,cAAc,EAAE,CAAC,YAAD;MAAlB,CApBF,CAFQ;IADN,CADA;IA4BNqD,QAAQ,EAAE;MACRpF,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADF;EA5BJ;AAHiB,CAAjB,CAAV;;AAqCO,MAAMwG,2BAA2B,GAAG,OAAO;EAChDd,QAAQ,EAAE;IACRjH,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;IAERkC,QAAQ,EAAE;EAFF,CADsC;EAKhD4F,aAAa,EAAE;IACbhI,QAAQ,EAAE,IAAAc,kBAAA,EAAY,QAAZ,EAAsB,SAAtB,EAAiC,WAAjC,CADG;IAEbsB,QAAQ,EAAE;EAFG,CALiC;EAShD6F,MAAM,EAAE;IACN5H,OAAO,EAAE;EADH,CATwC;EAYhD6H,QAAQ,EAAE;IACR7H,OAAO,EAAE;EADD,CAZsC;EAehDqE,QAAQ,EAAE;IACRrE,OAAO,EAAE;EADD,CAfsC;EAkBhD+B,QAAQ,EAAE;IACRpC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;IAERkC,QAAQ,EAAE;EAFF,CAlBsC;EAsBhDlB,GAAG,EAAE;IACHlB,QAAQ,EAAE,IAAAC,YAAA,EACP,YAAY;MACX,MAAMoF,MAAM,GAAG,IAAA9D,qBAAA,EACb,YADa,EAEb,eAFa,EAGb,gBAHa,CAAf;MAKA,MAAMmD,QAAQ,GAAG,IAAAnD,qBAAA,EAAe,YAAf,CAAjB;MAEA,OAAO,UAAUN,IAAV,EAAqBC,GAArB,EAAkCC,GAAlC,EAA4C;QACjD,MAAMC,SAAS,GAAGH,IAAI,CAACyD,QAAL,GAAgBA,QAAhB,GAA2BW,MAA7C;QACAjE,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;MACD,CAHD;IAID,CAZD,EADQ,EAcR,IAAAI,qBAAA,EACE,YADF,EAEE,eAFF,EAGE,gBAHF,EAIE,eAJF,EAKE,YALF,CAdQ;EADP;AAtB2C,CAAP,CAApC;;;;AAgDA,MAAM4G,gCAAgC,GAAG,wBAC3C/E,cAAc,EAD6B,EAE3C2E,2BAA2B,EAFgB;EAG9C1E,MAAM,EAAE;IACNrD,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EACE,YADF,EAEE,SAFF,EAGE,aAHF,EAIE,qBAJF,CADF,CAFQ;EADJ,CAHsC;EAgB9CmE,IAAI,EAAE;IACJ1F,QAAQ,EAAE,IAAAc,kBAAA,EAAY,KAAZ,EAAmB,KAAnB,EAA0B,QAA1B,EAAoC,aAApC,CADN;IAEJT,OAAO,EAAE;EAFL,CAhBwC;EAoB9C+H,MAAM,EAAE;IACNpI,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,QAAhB,CADQ,EAER,IAAAY,kBAAA,EAAY,QAAZ,EAAsB,SAAtB,EAAiC,WAAjC,CAFQ,CADJ;IAKNsB,QAAQ,EAAE;EALJ,CApBsC;EA2B9C8B,UAAU,EAAE;IACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;IAKVa,QAAQ,EAAE;EALA;AA3BkC,EAAzC;;;AAoCPxC,UAAU,CAAC,aAAD,EAAgB;EACxBe,OAAO,EAAE,CAAC,UAAD,EAAa,UAAb,EAAyB,aAAzB,EAAwC,gBAAxC,EAA0D,QAA1D,CADe;EAExBc,OAAO,EAAE,CACP,MADO,EAEP,KAFO,EAGP,QAHO,EAIP,MAJO,EAKP,UALO,EAMP,QANO,EAOP,WAPO,EAQP,OARO,CAFe;EAYxBf,OAAO,EAAE,CACP,KADO,EAEP,QAFO,EAGP,MAHO,EAIP,YAJO,EAKP,YALO,EAMP,gBANO,CAZe;EAoBxBZ,MAAM,oBACDqI,gCAAgC,EAD/B,EAED3E,4BAA4B,EAF3B;IAGJtB,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IADN;EAHF;AApBkB,CAAhB,CAAV;AA6BA3B,UAAU,CAAC,eAAD,EAAkB;EAC1Bc,OAAO,EAAE,CACP,YADO,EAEP,gBAFO,EAGP,YAHO,CADiB;EAM1Be,OAAO,EAAE,CAAC,YAAD,CANiB;EAO1Bd,OAAO,EAAE,CAAC,SAAD,EAAY,aAAZ,EAA2B,MAA3B,CAPiB;EAQ1Bb,MAAM,oBACDkE,iBAAiB,EADhB;IAEJyB,UAAU,EAAE;MACVzF,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,aAAf,EAA8B,gBAA9B,CAAX,CAFQ;IADA;EAFR;AARoB,CAAlB,CAAV;AAmBA3B,UAAU,CAAC,eAAD,EAAkB;EAC1Bc,OAAO,EAAE,CAAC,UAAD,CADiB;EAE1BC,OAAO,EAAE,CAAC,WAAD,CAFiB;EAG1BoE,eAAe,EAAE,gBAHS;EAI1BjF,MAAM,EAAE;IACN8F,QAAQ,EAAE;MACR5F,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADF;EADJ;AAJkB,CAAlB,CAAV;AAWA3B,UAAU,CACR,OADQ,EAIJ;EACEe,OAAO,EAAE,CAAC,YAAD;AADX,CAJI,CAAV;AASAf,UAAU,CAAC,0BAAD,EAA6B;EACrCc,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,gBAAjB,CAD4B;EAErCe,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,CAF4B;EAGrCd,OAAO,EAAE,CAAC,YAAD,CAH4B;EAIrCb,MAAM,EAAE;IACNuI,GAAG,EAAE;MACHrI,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADP,CADC;IAIN+G,KAAK,EAAE;MACLtI,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,iBAAf;IADL,CAJD;IAONiB,cAAc,EAAE;MACdxC,QAAQ,EAAE,IAAAuB,qBAAA,EACR,4BADQ,EAER,8BAFQ,CADI;MAKda,QAAQ,EAAE;IALI;EAPV;AAJ6B,CAA7B,CAAV;AAqBAxC,UAAU,CAAC,iBAAD,EAAoB;EAC5B6B,OAAO,EAAE,CAAC,OAAD,EAAU,MAAV,CADmB;EAE5B3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAsI,kBAAA,EAAY;QACVC,GAAG,EAAE;UACHxI,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;QADP,CADK;QAIVuI,MAAM,EAAE;UACNzI,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB,CADJ;UAENkC,QAAQ,EAAE;QAFJ;MAJE,CAAZ,CADQ,EAUR,SAASsG,8BAAT,CAAwCzH,IAAxC,EAAiE;QAC/D,MAAMuH,GAAG,GAAGvH,IAAI,CAACe,KAAL,CAAWwG,GAAvB;QAEA,IAAIG,GAAJ;QAAA,IACEC,eADF;QAAA,IAEEC,kBAAkB,GAAG,KAFvB;;QAGA,IAAI;UACF,MAAMC,KAAK,GAAG,MAAM;YAClB,MAAM,IAAIjD,KAAJ,EAAN;UACD,CAFD;;UAGA,CAAC;YAAE8C,GAAF;YAAOC;UAAP,IAA2B,IAAAG,sCAAA,EAC1B,UAD0B,EAE1BP,GAF0B,EAG1B,CAH0B,EAI1B,CAJ0B,EAK1B,CAL0B,EAM1B;YACEQ,YAAY,GAAG;cACbH,kBAAkB,GAAG,IAArB;YACD,CAHH;;YAIEI,mBAAmB,EAAEH,KAJvB;YAKEI,qBAAqB,EAAEJ,KALzB;YAMEK,gCAAgC,EAAEL,KANpC;YAOEM,0BAA0B,EAAEN,KAP9B;YAQEO,YAAY,EAAEP,KARhB;YASEQ,gBAAgB,EAAER;UATpB,CAN0B,CAA5B;QAkBD,CAtBD,CAsBE,gBAAM;UAGND,kBAAkB,GAAG,IAArB;UACAD,eAAe,GAAG,IAAlB;QACD;;QACD,IAAI,CAACC,kBAAL,EAAyB,MAAM,IAAIhD,KAAJ,CAAU,aAAV,CAAN;QAEzB5E,IAAI,CAACe,KAAL,CAAWyG,MAAX,GAAoBG,eAAe,GAAG,IAAH,GAAUD,GAA7C;MACD,CA/CO;IADL,CADD;IAoDNY,IAAI,EAAE;MACJlJ,OAAO,EAAE;IADL;EApDA;AAFoB,CAApB,CAAV;AA4DAT,UAAU,CAAC,iBAAD,EAAoB;EAC5Bc,OAAO,EAAE,CAAC,QAAD,EAAW,aAAX,CADmB;EAE5BC,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,CAFmB;EAG5Bb,MAAM,EAAE;IACN0J,MAAM,EAAE;MACNxJ,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,iBAAf,CAAX,CAFQ;IADJ,CADF;IAON0E,WAAW,EAAE;MACXjG,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EACE,YADF,EAGE,QAHF,CADF,CAFQ,EASR,UAAUN,IAAV,EAAmCC,GAAnC,EAAwCC,GAAxC,EAA6C;QAC3C,IAAIF,IAAI,CAACuI,MAAL,CAAYxD,MAAZ,KAAuB7E,GAAG,CAAC6E,MAAJ,GAAa,CAAxC,EAA2C;UACzC,MAAM,IAAI3B,SAAJ,CACH,aACCpD,IAAI,CAACgC,IACN,gFACC9B,GAAG,CAAC6E,MAAJ,GAAa,CACd,mBAAkB/E,IAAI,CAACuI,MAAL,CAAYxD,MAAO,EALlC,CAAN;QAOD;MACF,CAnBO;IADC;EAPP;AAHoB,CAApB,CAAV;AAoCApG,UAAU,CAAC,iBAAD,EAAoB;EAC5B6B,OAAO,EAAE,CAAC,UAAD,EAAa,UAAb,CADmB;EAE5Bf,OAAO,EAAE,CAAC,UAAD,CAFmB;EAG5BC,OAAO,EAAE,CAAC,YAAD,EAAe,gBAAf,CAHmB;EAI5Bb,MAAM,EAAE;IACN2J,QAAQ,EAAE;MACRzJ,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,SAAhB,CADQ,EAER2B,MAAM,CAACC,MAAP,CACE,UAAUb,IAAV,EAAmCC,GAAnC,EAAwCC,GAAxC,EAA6C;QAC3C,IAAI,CAACb,OAAO,CAACC,GAAR,CAAYC,sBAAjB,EAAyC;;QAEzC,IAAIW,GAAG,IAAI,CAACF,IAAI,CAAC2E,QAAjB,EAA2B;UACzB,MAAM,IAAIvB,SAAJ,CACJ,6EADI,CAAN;QAGD;MACF,CATH,EAUE;QAAEpB,IAAI,EAAE;MAAR,CAVF,CAFQ,CADF;MAgBR5C,OAAO,EAAE;IAhBD,CADJ;IAmBNuF,QAAQ,EAAE;MACRxD,QAAQ,EAAE,IADF;MAERpC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IAFF;EAnBJ;AAJoB,CAApB,CAAV;AA+BA3B,UAAU,CAAC,iBAAD,EAAoB;EAC5B6B,OAAO,EAAE,CAAC,UAAD,CADmB;EAE5Bf,OAAO,EAAE,CAAC,UAAD,CAFmB;EAG5BC,OAAO,EAAE,CAAC,YAAD,EAAe,gBAAf,CAHmB;EAI5Bb,MAAM,EAAE;IACN8F,QAAQ,EAAE;MACR5F,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADF;EADJ;AAJoB,CAApB,CAAV;AAYA3B,UAAU,CAAC,QAAD,EAAW;EACnBe,OAAO,EAAE,CAAC,YAAD;AADU,CAAX,CAAV;AAKAf,UAAU,CAAC,eAAD,EAAkB;EAC1B6B,OAAO,EAAE,CAAC,OAAD,CADiB;EAE1B3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,QAAhB;IADL;EADD,CAFkB;EAO1BS,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,EAAqC,WAArC;AAPiB,CAAlB,CAAV;AAUAf,UAAU,CAAC,0BAAD,EAA6B;EACrCc,OAAO,EAAE,CAAC,UAAD,CAD4B;EAErCC,OAAO,EAAE,CAAC,iBAAD,CAF4B;EAGrCb,MAAM,EAAE;IACN6H,QAAQ,EAAE;MACR3H,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADF;EADJ;AAH6B,CAA7B,CAAV;AAUA3B,UAAU,CAAC,0BAAD,EAA6B;EACrC6B,OAAO,EAAE,CAAC,QAAD,EAAW,UAAX,EAAuB,UAAvB,EAAmC,UAAnC,CAD4B;EAErCf,OAAO,EAAE,CAAC,QAAD,EAAW,UAAX,CAF4B;EAGrCC,OAAO,EAAE,CAAC,YAAD,CAH4B;EAIrCb,MAAM,EAAE;IACNqF,MAAM,EAAE;MACNnF,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADJ,CADF;IAIN6D,QAAQ,EAAE;MACRpF,QAAQ,EAAG,YAAY;QACrB,MAAMqF,MAAM,GAAG,IAAA9D,qBAAA,EAAe,YAAf,CAAf;QACA,MAAMmD,QAAQ,GAAG,IAAAnD,qBAAA,EAAe,YAAf,CAAjB;QAEA,MAAMH,SAAoB,GAAGS,MAAM,CAACC,MAAP,CAC3B,UAAUb,IAAV,EAA4CC,GAA5C,EAAiDC,GAAjD,EAAsD;UACpD,MAAMC,SAAS,GAAGH,IAAI,CAACyD,QAAL,GAAgBA,QAAhB,GAA2BW,MAA7C;UACAjE,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAJ0B,EAM3B;UAAEY,cAAc,EAAE,CAAC,YAAD,EAAe,YAAf;QAAlB,CAN2B,CAA7B;QAQA,OAAOX,SAAP;MACD,CAbS;IADF,CAJJ;IAoBNsD,QAAQ,EAAE;MACRrE,OAAO,EAAE;IADD,CApBJ;IAuBN+B,QAAQ,EAAE;MACRpC,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACN,IAAAN,sBAAA,EAAgB,SAAhB,CADM,GAEN,IAAAD,YAAA,EAAM,IAAAC,sBAAA,EAAgB,SAAhB,CAAN,EAAkC,IAAAwJ,+BAAA,GAAlC;IAHI;EAvBJ;AAJ6B,CAA7B,CAAV;AAmCA9J,UAAU,CAAC,wBAAD,EAA2B;EACnCc,OAAO,EAAE,CAAC,QAAD,EAAW,WAAX,EAAwB,gBAAxB,EAA0C,eAA1C,CAD0B;EAEnCe,OAAO,EAAE,CAAC,QAAD,EAAW,WAAX,EAAwB,UAAxB,CAF0B;EAGnCd,OAAO,EAAE,CAAC,YAAD,CAH0B;EAInCb,MAAM,EAAE;IACNuC,MAAM,EAAE;MACNrC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADJ,CADF;IAINe,SAAS,EAAE;MACTtC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAoB,qBAAA,EACE,YADF,EAEE,eAFF,EAGE,mBAHF,EAIE,qBAJF,CADF,CAFQ;IADD,CAJL;IAiBNa,QAAQ,EAAE;MACRpC,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACN,IAAAN,sBAAA,EAAgB,SAAhB,CADM,GAEN,IAAAD,YAAA,EAAM,IAAAC,sBAAA,EAAgB,SAAhB,CAAN,EAAkC,IAAAwJ,+BAAA,GAAlC;IAHI,CAjBJ;IAsBNnH,aAAa,EAAE;MACbvC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,4BAAf,CADG;MAEba,QAAQ,EAAE;IAFG,CAtBT;IA0BNI,cAAc,EAAE;MACdxC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,8BAAf,CADI;MAEda,QAAQ,EAAE;IAFI;EA1BV;AAJ2B,CAA3B,CAAV;AAsCAxC,UAAU,CAAC,eAAD,EAAkB;EAC1Bc,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,gBAAjB,EAAmC,YAAnC,CADiB;EAE1Be,OAAO,EAAE,CACP,KADO,EAEP,OAFO,EAGP,gBAHO,EAIP,YAJO,EAKP,UALO,EAMP,QANO,CAFiB;EAU1Bd,OAAO,EAAE,CAAC,UAAD,CAViB;EAW1Bb,MAAM,oBACDiI,2BAA2B,EAD1B;IAEJ/F,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADL;MAELa,QAAQ,EAAE;IAFL,CAFH;IAMJwE,QAAQ,EAAE;MACR5G,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF,CANN;IAUJ6B,cAAc,EAAE;MACdjE,QAAQ,EAEJ,IAAAuB,qBAAA,EACE,gBADF,EAEE,kBAFF,EAIE,MAJF,CAHU;MASda,QAAQ,EAAE;IATI,CAVZ;IAqBJ8B,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CArBR;IA4BJuH,QAAQ,EAAE;MACR3J,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF,CA5BN;IAgCJuB,OAAO,EAAE;MACP3D,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADH;MAEPkC,QAAQ,EAAE;IAFH,CAhCL;IAoCJwH,QAAQ,EAAE;MACR5J,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,UAAf,CADF;MAERa,QAAQ,EAAE;IAFF;EApCN;AAXoB,CAAlB,CAAV;AAsDAxC,UAAU,CAAC,uBAAD,EAA0B;EAClCc,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,gBAAjB,EAAmC,YAAnC,CADyB;EAElCe,OAAO,EAAE,CACP,KADO,EAEP,OAFO,EAGP,gBAHO,EAIP,YAJO,EAKP,UALO,EAMP,QANO,CAFyB;EAUlCd,OAAO,EAAE,CAAC,UAAD,EAAa,UAAb,CAVyB;EAWlCb,MAAM,oBACDiI,2BAA2B,EAD1B;IAEJ7G,GAAG,EAAE;MACHlB,QAAQ,EAAE,IAAAC,YAAA,EACP,YAAY;QACX,MAAMoF,MAAM,GAAG,IAAA9D,qBAAA,EACb,YADa,EAEb,eAFa,EAGb,gBAHa,EAIb,eAJa,EAKb,aALa,CAAf;QAOA,MAAMmD,QAAQ,GAAG,IAAAnD,qBAAA,EAAe,YAAf,CAAjB;QAEA,OAAO,UAAUN,IAAV,EAAqBC,GAArB,EAAkCC,GAAlC,EAA4C;UACjD,MAAMC,SAAS,GAAGH,IAAI,CAACyD,QAAL,GAAgBA,QAAhB,GAA2BW,MAA7C;UACAjE,SAAS,CAACH,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAT;QACD,CAHD;MAID,CAdD,EADQ,EAgBR,IAAAI,qBAAA,EACE,YADF,EAEE,eAFF,EAGE,gBAHF,EAIE,eAJF,EAKE,YALF,EAME,aANF,CAhBQ;IADP,CAFD;IA6BJS,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADL;MAELa,QAAQ,EAAE;IAFL,CA7BH;IAiCJwE,QAAQ,EAAE;MACR5G,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF,CAjCN;IAqCJ6B,cAAc,EAAE;MACdjE,QAAQ,EAEJ,IAAAuB,qBAAA,EACE,gBADF,EAEE,kBAFF,EAIE,MAJF,CAHU;MASda,QAAQ,EAAE;IATI,CArCZ;IAgDJ8B,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CAhDR;IAuDJuH,QAAQ,EAAE;MACR3J,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF,CAvDN;IA2DJuB,OAAO,EAAE;MACP3D,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADH;MAEPkC,QAAQ,EAAE;IAFH,CA3DL;IA+DJwH,QAAQ,EAAE;MACR5J,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,UAAf,CADF;MAERa,QAAQ,EAAE;IAFF;EA/DN;AAX4B,CAA1B,CAAV;AAiFAxC,UAAU,CAAC,sBAAD,EAAyB;EACjCc,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,YAAjB,EAA+B,gBAA/B,CADwB;EAEjCe,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,YAAjB,EAA+B,QAA/B,CAFwB;EAGjCd,OAAO,EAAE,CAAC,UAAD,EAAa,SAAb,CAHwB;EAIjCb,MAAM,EAAE;IACNoB,GAAG,EAAE;MACHlB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,aAAf;IADP,CADC;IAINS,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf,CADL;MAELa,QAAQ,EAAE;IAFL,CAJD;IAQN6B,cAAc,EAAE;MACdjE,QAAQ,EAEJ,IAAAuB,qBAAA,EACE,gBADF,EAEE,kBAFF,EAIE,MAJF,CAHU;MASda,QAAQ,EAAE;IATI,CARV;IAmBN8B,UAAU,EAAE;MACVlE,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVa,QAAQ,EAAE;IALA,CAnBN;IA0BN6F,MAAM,EAAE;MACNjI,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADJ;MAENG,OAAO,EAAE;IAFH,CA1BF;IA8BNsJ,QAAQ,EAAE;MACR3J,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF,CA9BJ;IAkCNwE,QAAQ,EAAE;MACR5G,QAAQ,EAAE,IAAAE,sBAAA,EAAgB,SAAhB,CADF;MAERkC,QAAQ,EAAE;IAFF,CAlCJ;IAsCNwH,QAAQ,EAAE;MACR5J,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,UAAf,CADF;MAERa,QAAQ,EAAE;IAFF;EAtCJ;AAJyB,CAAzB,CAAV;AAiDAxC,UAAU,CAAC,oBAAD,EAAuB;EAC/B6B,OAAO,EAAE,CAAC,MAAD,EAAS,KAAT,EAAgB,QAAhB,EAA0B,MAA1B,EAAkC,QAAlC,CADsB;EAE/Bf,OAAO,EAAE,CACP,KADO,EAEP,QAFO,EAGP,MAHO,EAIP,YAJO,EAKP,YALO,EAMP,gBANO,CAFsB;EAU/BC,OAAO,EAAE,CACP,UADO,EAEP,UAFO,EAGP,aAHO,EAIP,gBAJO,EAKP,QALO,EAMP,SANO,CAVsB;EAkB/Bb,MAAM,oBACDqI,gCAAgC,EAD/B,EAED3E,4BAA4B,EAF3B;IAGJkC,IAAI,EAAE;MACJ1F,QAAQ,EAAE,IAAAc,kBAAA,EAAY,KAAZ,EAAmB,KAAnB,EAA0B,QAA1B,CADN;MAEJT,OAAO,EAAE;IAFL,CAHF;IAOJa,GAAG,EAAE;MACHlB,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,aAAf;IADP,CAPD;IAUJW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,gBAAf;IADN;EAVF;AAlByB,CAAvB,CAAV;AAkCA3B,UAAU,CAAC,aAAD,EAAgB;EACxBc,OAAO,EAAE,CAAC,IAAD,CADe;EAExBC,OAAO,EAAE,CAAC,SAAD,CAFe;EAGxBb,MAAM,EAAE;IACN8D,EAAE,EAAE;MACF5D,QAAQ,EAAE,IAAAuB,qBAAA,EAAe,YAAf;IADR;EADE;AAHgB,CAAhB,CAAV;AAUA3B,UAAU,CAAC,aAAD,EAAgB;EACxBc,OAAO,EAAE,CAAC,MAAD,CADe;EAExBZ,MAAM,EAAE;IACNoC,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAoB,qBAAA,EAAe,WAAf,CAAX,CAFQ;IADN;EADA,CAFgB;EAUxBZ,OAAO,EAAE,CAAC,UAAD,EAAa,aAAb,EAA4B,gBAA5B;AAVe,CAAhB,CAAV"}