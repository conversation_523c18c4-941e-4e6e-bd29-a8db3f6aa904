import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// 简化的用户服务类
export class UserService {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'default-secret';
  private static readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
  private static readonly BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');
  private static readonly SIGNATURE_SECRET = process.env.SIGNATURE_SECRET || 'license-signature-secret';

  /**
   * 哈希密码
   */
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, UserService.BCRYPT_ROUNDS);
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash);
  }

  /**
   * 生成JWT令牌
   */
  static generateToken(payload: object): string {
    return jwt.sign(payload, UserService.JWT_SECRET, { 
      expiresIn: UserService.JWT_EXPIRES_IN 
    } as jwt.SignOptions);
  }

  /**
   * 验证JWT令牌
   */
  static verifyToken(token: string): any {
    try {
      return jwt.verify(token, UserService.JWT_SECRET);
    } catch (error) {
      throw new Error('令牌无效');
    }
  }

  /**
   * 生成数字签名
   */
  static generateSignature(data: string): string {
    return crypto
      .createHmac('sha256', UserService.SIGNATURE_SECRET)
      .update(data)
      .digest('hex');
  }

  /**
   * 验证数字签名
   */
  static verifySignature(data: string, signature: string): boolean {
    const expectedSignature = UserService.generateSignature(data);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * 生成用户登录响应
   */
  static createLoginResponse(success: boolean, data?: any, message?: string) {
    return {
      success,
      ...(data && { token: data.token, user: data.user }),
      ...(message && { message }),
    };
  }
} 