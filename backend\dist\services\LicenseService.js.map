{"version": 3, "file": "LicenseService.js", "sourceRoot": "", "sources": ["../../src/services/LicenseService.ts"], "names": [], "mappings": ";;;AAAA,4CAA8C;AAE9C,MAAa,cAAc;IACzB;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,OAAO,oBAAW,CAAC,kBAAkB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,WAAgB;QACrC,cAAc;QACd,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,aAAa;QACb,IAAI,WAAW,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,OAAgB,EAAE,IAAU,EAAE,OAAgB;QAC5E,OAAO;YACL,OAAO;YACP,GAAG,CAAC,IAAI,IAAI;gBACV,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC;YACF,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,OAAgB,EAAE,OAAgB,EAAE,IAAU,EAAE,OAAgB;QAC9F,OAAO;YACL,OAAO;YACP,OAAO;YACP,GAAG,CAAC,IAAI,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1C,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,WAAgB,EAAE,SAAiB;QAC1D,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,OAAO,WAAW,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,WAAgB;QACxC,cAAc;QACd,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,aAAa;QACb,IAAI,WAAW,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,WAAgB;QACtC,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,WAAW,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YAC1E,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AApGD,wCAoGC"}