"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const UserService_1 = require("../services/UserService");
const DataService_1 = require("../services/DataService");
const router = (0, express_1.Router)();
// 仪表板统计数据
router.get('/stats', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const users = await DataService_1.DataService.getUsers();
        const software = await DataService_1.DataService.getSoftware();
        const licenses = await DataService_1.DataService.getLicenses();
        // 计算活跃许可证（未撤销且未过期）
        const activeLicenses = licenses.filter(l => !l.isRevoked &&
            (!l.expiresAt || new Date(l.expiresAt) > new Date()));
        // 获取最近的许可证
        const recentLicenses = licenses
            .slice(-5)
            .map(license => {
            const user = users.find(u => u.id === license.userId);
            const soft = software.find(s => s.id === license.softwareId);
            return {
                ...license,
                username: user ? user.username : '未知用户',
                softwareName: soft ? soft.name : '未知软件'
            };
        });
        return res.json({
            success: true,
            data: {
                totalUsers: users.length,
                totalSoftware: software.length,
                totalLicenses: licenses.length,
                activeLicenses: activeLicenses.length,
                recentLicenses: recentLicenses
            }
        });
    }
    catch (error) {
        console.error('[Admin] 获取统计数据失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取统计数据失败'
        });
    }
});
// 获取操作日志
router.get('/logs', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const limit = parseInt(req.query.limit) || 100;
        const logs = await DataService_1.DataService.getLogs(limit);
        return res.json({
            success: true,
            data: logs
        });
    }
    catch (error) {
        console.error('[Admin] 获取日志失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取日志失败'
        });
    }
});
// 用户管理
router.get('/users', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const users = await DataService_1.DataService.getUsers();
        const usersWithoutPasswords = users.map(user => ({
            id: user.id,
            username: user.username,
            email: user.email,
            roles: user.roles,
            createdAt: user.createdAt
        }));
        return res.json({
            success: true,
            data: usersWithoutPasswords
        });
    }
    catch (error) {
        console.error('[Admin] 获取用户列表失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取用户列表失败'
        });
    }
});
// 创建用户
router.post('/users', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const { username, email, password, roles } = req.body;
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名、邮箱和密码不能为空'
            });
        }
        // 检查用户名是否已存在
        const existingUser = await DataService_1.DataService.getUserByUsername(username);
        if (existingUser) {
            return res.status(409).json({
                success: false,
                message: '用户名已存在'
            });
        }
        const passwordHash = await UserService_1.UserService.hashPassword(password);
        const newUserId = await DataService_1.DataService.addUser({
            username,
            email,
            passwordHash,
            roles: roles || ['user'],
            createdAt: new Date().toISOString()
        });
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '创建用户',
            object: `用户: ${username}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '用户创建成功',
            data: { id: newUserId, username, email, roles }
        });
    }
    catch (error) {
        console.error('[Admin] 创建用户失败:', error);
        return res.status(500).json({
            success: false,
            message: '创建用户失败'
        });
    }
});
// 获取单个用户信息
router.get('/users/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const userId = parseInt(req.params.id);
        if (isNaN(userId)) {
            return res.status(400).json({
                success: false,
                message: '用户ID无效'
            });
        }
        const users = await DataService_1.DataService.getUsers();
        const user = users.find(u => u.id === userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        // 返回用户信息（不包含密码）
        const { passwordHash, ...userInfo } = user;
        return res.json({
            success: true,
            data: userInfo
        });
    }
    catch (error) {
        console.error('[Admin] 获取用户信息失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取用户信息失败'
        });
    }
});
// 更新用户
router.put('/users/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const userId = parseInt(req.params.id);
        const { username, email, password, roles } = req.body;
        const updates = {};
        if (username)
            updates.username = username;
        if (email)
            updates.email = email;
        if (password)
            updates.passwordHash = await UserService_1.UserService.hashPassword(password);
        if (roles)
            updates.roles = roles;
        const success = await DataService_1.DataService.updateUser(userId, updates);
        if (!success) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '更新用户',
            object: `用户ID: ${userId}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '用户更新成功'
        });
    }
    catch (error) {
        console.error('[Admin] 更新用户失败:', error);
        return res.status(500).json({
            success: false,
            message: '更新用户失败'
        });
    }
});
// 删除用户
router.delete('/users/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const userId = parseInt(req.params.id);
        // 不能删除自己
        if (userId === req.user.id) {
            return res.status(400).json({
                success: false,
                message: '不能删除自己的账户'
            });
        }
        const user = await DataService_1.DataService.getUserById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const success = await DataService_1.DataService.deleteUser(userId);
        if (!success) {
            return res.status(500).json({
                success: false,
                message: '删除用户失败'
            });
        }
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '删除用户',
            object: `用户: ${user.username}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '用户删除成功'
        });
    }
    catch (error) {
        console.error('[Admin] 删除用户失败:', error);
        return res.status(500).json({
            success: false,
            message: '删除用户失败'
        });
    }
});
// 软件管理
router.get('/software', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const software = await DataService_1.DataService.getSoftware();
        return res.json({
            success: true,
            data: software
        });
    }
    catch (error) {
        console.error('[Admin] 获取软件列表失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取软件列表失败'
        });
    }
});
// 创建软件
router.post('/software', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const { name, productKey, description, isActive, price, trialDays } = req.body;
        if (!name || !productKey) {
            return res.status(400).json({
                success: false,
                message: '软件名称和产品密钥不能为空'
            });
        }
        const newSoftwareId = await DataService_1.DataService.addSoftware({
            name,
            productKey,
            description: description || '',
            isActive: isActive !== undefined ? isActive : true,
            price: price || 0,
            trialDays: trialDays || 7,
            createdAt: new Date().toISOString()
        });
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '创建软件',
            object: `软件: ${name}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '软件创建成功',
            data: { id: newSoftwareId, name, productKey }
        });
    }
    catch (error) {
        console.error('[Admin] 创建软件失败:', error);
        return res.status(500).json({
            success: false,
            message: '创建软件失败'
        });
    }
});
// 获取单个软件信息
router.get('/software/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const softwareId = parseInt(req.params.id);
        if (isNaN(softwareId)) {
            return res.status(400).json({
                success: false,
                message: '软件ID无效'
            });
        }
        const software = await DataService_1.DataService.getSoftware();
        const targetSoftware = software.find(s => s.id === softwareId);
        if (!targetSoftware) {
            return res.status(404).json({
                success: false,
                message: '软件不存在'
            });
        }
        return res.json({
            success: true,
            data: targetSoftware
        });
    }
    catch (error) {
        console.error('[Admin] 获取软件信息失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取软件信息失败'
        });
    }
});
// 更新软件
router.put('/software/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const softwareId = parseInt(req.params.id);
        const { name, productKey, description, isActive, price, trialDays } = req.body;
        const updates = {};
        if (name)
            updates.name = name;
        if (productKey)
            updates.productKey = productKey;
        if (description !== undefined)
            updates.description = description;
        if (isActive !== undefined)
            updates.isActive = isActive;
        if (price !== undefined)
            updates.price = price;
        if (trialDays !== undefined)
            updates.trialDays = trialDays;
        const success = await DataService_1.DataService.updateSoftware(softwareId, updates);
        if (!success) {
            return res.status(404).json({
                success: false,
                message: '软件不存在'
            });
        }
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '更新软件',
            object: `软件ID: ${softwareId}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '软件更新成功'
        });
    }
    catch (error) {
        console.error('[Admin] 更新软件失败:', error);
        return res.status(500).json({
            success: false,
            message: '更新软件失败'
        });
    }
});
// 删除软件
router.delete('/software/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const softwareId = parseInt(req.params.id);
        const success = await DataService_1.DataService.deleteSoftware(softwareId);
        if (!success) {
            return res.status(404).json({
                success: false,
                message: '软件不存在'
            });
        }
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '删除软件',
            object: `软件ID: ${softwareId}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '软件删除成功'
        });
    }
    catch (error) {
        console.error('[Admin] 删除软件失败:', error);
        return res.status(500).json({
            success: false,
            message: '删除软件失败'
        });
    }
});
// 许可证管理
router.get('/licenses', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const licenses = await DataService_1.DataService.getLicenses();
        const users = await DataService_1.DataService.getUsers();
        const software = await DataService_1.DataService.getSoftware();
        const licensesWithDetails = licenses.map(license => {
            const user = users.find(u => u.id === license.userId);
            const soft = software.find(s => s.id === license.softwareId);
            return {
                ...license,
                username: user ? user.username : '未知用户',
                softwareName: soft ? soft.name : '未知软件'
            };
        });
        return res.json({
            success: true,
            data: licensesWithDetails
        });
    }
    catch (error) {
        console.error('[Admin] 获取许可证列表失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取许可证列表失败'
        });
    }
});
// 撤销许可证
router.put('/licenses/:id/revoke', auth_1.authenticateToken, async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user.roles.includes('admin')) {
            return res.status(403).json({
                success: false,
                message: '需要管理员权限'
            });
        }
        const licenseId = parseInt(req.params.id);
        const success = await DataService_1.DataService.updateLicense(licenseId, { isRevoked: true });
        if (!success) {
            return res.status(404).json({
                success: false,
                message: '许可证不存在'
            });
        }
        // 记录日志
        await DataService_1.DataService.createLog({
            userId: req.user.id,
            username: req.user.username,
            action: '撤销许可证',
            object: `许可证ID: ${licenseId}`,
            result: '成功'
        });
        return res.json({
            success: true,
            message: '许可证撤销成功'
        });
    }
    catch (error) {
        console.error('[Admin] 撤销许可证失败:', error);
        return res.status(500).json({
            success: false,
            message: '撤销许可证失败'
        });
    }
});
exports.default = router;
//# sourceMappingURL=admin.js.map