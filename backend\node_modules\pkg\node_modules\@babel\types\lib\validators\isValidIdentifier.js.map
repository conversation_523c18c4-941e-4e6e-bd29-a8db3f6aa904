{"version": 3, "names": ["isValidIdentifier", "name", "reserved", "isKeyword", "isStrictReservedWord", "isIdentifierName"], "sources": ["../../src/validators/isValidIdentifier.ts"], "sourcesContent": ["import {\n  isIdentifierName,\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\n/**\n * Check if the input `name` is a valid identifier name\n * and isn't a reserved word.\n */\nexport default function isValidIdentifier(\n  name: string,\n  reserved: boolean = true,\n): boolean {\n  if (typeof name !== \"string\") return false;\n\n  if (reserved) {\n    // \"await\" is invalid in module, valid in script; better be safe (see #4952)\n    if (isKeyword(name) || isStrictReservedWord(name, true)) {\n      return false;\n    }\n  }\n\n  return isIdentifierName(name);\n}\n"], "mappings": ";;;;;;;AAAA;;AAUe,SAASA,iBAAT,CACbC,IADa,EAEbC,QAAiB,GAAG,IAFP,EAGJ;EACT,IAAI,OAAOD,IAAP,KAAgB,QAApB,EAA8B,OAAO,KAAP;;EAE9B,IAAIC,QAAJ,EAAc;IAEZ,IAAI,IAAAC,oCAAA,EAAUF,IAAV,KAAmB,IAAAG,+CAAA,EAAqBH,IAArB,EAA2B,IAA3B,CAAvB,EAAyD;MACvD,OAAO,KAAP;IACD;EACF;;EAED,OAAO,IAAAI,2CAAA,EAAiBJ,IAAjB,CAAP;AACD"}