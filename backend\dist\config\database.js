"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = exports.sequelize = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const path_1 = __importDefault(require("path"));
const User_1 = require("../models/User");
const Software_1 = require("../models/Software");
const License_1 = require("../models/License");
const dbPath = process.env.DB_PATH || path_1.default.join(process.cwd(), 'database', 'license_management.db');
exports.sequelize = new sequelize_typescript_1.Sequelize({
    dialect: 'sqlite',
    storage: dbPath,
    models: [User_1.User, Software_1.Software, License_1.License],
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    define: {
        timestamps: true,
        underscored: false,
    },
});
const initializeDatabase = async () => {
    try {
        await exports.sequelize.authenticate();
        console.log('数据库连接成功');
        // 同步模型到数据库
        await exports.sequelize.sync({ alter: true });
        console.log('数据库模型同步完成');
    }
    catch (error) {
        console.error('数据库连接失败:', error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
//# sourceMappingURL=database.js.map