import { DatabaseService, User, Software, License } from './DatabaseService';

export { User, Software, License } from './DatabaseService';

export class DataService {
  private static db: DatabaseService;

  // 初始化数据服务
  public static async initialize(): Promise<void> {
    DataService.db = DatabaseService.getInstance();
    await DataService.db.initialize();
    
    // 检查是否需要插入默认数据
    await DataService.insertDefaultDataIfNeeded();
    console.log('[DataService] 数据服务初始化完成');
  }

  // 检查并插入默认数据
  private static async insertDefaultDataIfNeeded(): Promise<void> {
    const users = await DataService.db.getUsers();
    
    // 如果没有用户数据，插入默认数据
    if (users.length === 0) {
      console.log('[DataService] 插入默认数据...');
      
      // 默认用户
      const defaultUsers = [
        { 
          username: 'admin', 
          email: '<EMAIL>', 
          passwordHash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
          roles: ['admin'], 
          createdAt: '2024-01-01' 
        },
        { 
          username: 'user1', 
          email: '<EMAIL>', 
          passwordHash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
          roles: ['user'], 
          createdAt: '2024-01-02' 
        },
        { 
          username: 'manager1', 
          email: '<EMAIL>', 
          passwordHash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
          roles: ['manager'], 
          createdAt: '2024-01-03' 
        },
        { 
          username: 'zansimple', 
          email: '<EMAIL>', 
          passwordHash: '$2b$10$2CdCCfuHetn4UR/.KGOF0e8LYx5eklgjv4raMcYoBbG0mGE4..Wjq', // zan724513
          roles: ['user'], 
          createdAt: '2024-01-04' 
        }
      ];

      for (const user of defaultUsers) {
        await DataService.db.createUser(user);
      }

      // 默认软件
      const defaultSoftware = [
        { 
          name: '示例软件A', 
          productKey: 'ABCD-EFGH-IJKL-MNOP', 
          description: '这是一款功能强大的数据处理软件，支持多种文件格式转换和批量处理功能。', 
          isActive: true, 
          createdAt: '2024-01-01',
          price: 299.00,
          trialDays: 7
        },
        { 
          name: '示例软件B', 
          productKey: 'QRST-UVWX-YZ12-3456', 
          description: '专业的图像编辑工具，提供丰富的滤镜和特效，适合设计师和摄影师使用。', 
          isActive: true, 
          createdAt: '2024-01-02',
          price: 199.00,
          trialDays: 14
        },
        { 
          name: '示例软件C', 
          productKey: '7890-ABCD-EFGH-IJKL', 
          description: '轻量级的文本编辑器，支持语法高亮和多标签页，适合程序员和作家使用。', 
          isActive: false, 
          createdAt: '2024-01-03',
          price: 99.00,
          trialDays: 30
        }
      ];

      for (const software of defaultSoftware) {
        await DataService.db.createSoftware(software);
      }

      // 默认许可证
      const defaultLicenses = [
        { 
          licenseKey: 'LIC-12345678-ABCD-EFGH', 
          softwareId: 1, 
          userId: 2, 
          activationType: 'offline', 
          activatedAt: '2024-01-15T10:30:00Z', 
          expiresAt: '2024-02-15T10:30:00Z', 
          machineId: 'MACHINE-001',
          isRevoked: false,
          isTrial: false,
          createdAt: '2024-01-15T10:30:00Z'
        },
        { 
          licenseKey: 'LIC-87654321-WXYZ-1234', 
          softwareId: 2, 
          userId: 3, 
          activationType: 'offline', 
          activatedAt: '2024-01-16T14:20:00Z', 
          expiresAt: '2024-02-16T14:20:00Z', 
          machineId: 'MACHINE-002',
          isRevoked: false,
          isTrial: true,
          createdAt: '2024-01-16T14:20:00Z'
        },
        { 
          licenseKey: 'LIC-11223344-ABCD-EFGH', 
          softwareId: 1, 
          userId: 4, 
          activationType: 'offline', 
          activatedAt: '2024-01-17T10:00:00Z', 
          expiresAt: '2025-02-17T10:00:00Z', // 设置为2025年，确保未过期
          machineId: '',
          isRevoked: false,
          isTrial: true,
          createdAt: '2024-01-17T10:00:00Z'
        }
      ];

      for (const license of defaultLicenses) {
        await DataService.db.createLicense(license);
      }

      console.log('[DataService] 默认数据插入完成');
    }
  }

  // 用户数据操作
  public static async getUsers(): Promise<User[]> {
    return await DataService.db.getUsers();
  }

  public static async getUserById(id: number): Promise<User | null> {
    return await DataService.db.getUserById(id);
  }

  public static async getUserByUsername(username: string): Promise<User | null> {
    return await DataService.db.getUserByUsername(username);
  }

  public static async addUser(user: Omit<User, 'id'>): Promise<number> {
    return await DataService.db.createUser(user);
  }

  public static async updateUser(userId: number, updates: Partial<User>): Promise<boolean> {
    return await DataService.db.updateUser(userId, updates);
  }

  public static async deleteUser(userId: number): Promise<boolean> {
    return await DataService.db.deleteUser(userId);
  }

  // 软件数据操作
  public static async getSoftware(): Promise<Software[]> {
    return await DataService.db.getSoftware();
  }

  public static async addSoftware(software: Omit<Software, 'id'>): Promise<number> {
    return await DataService.db.createSoftware(software);
  }

  public static async updateSoftware(softwareId: number, updates: Partial<Software>): Promise<boolean> {
    return await DataService.db.updateSoftware(softwareId, updates);
  }

  public static async deleteSoftware(softwareId: number): Promise<boolean> {
    return await DataService.db.deleteSoftware(softwareId);
  }

  // 许可证数据操作
  public static async getLicenses(): Promise<License[]> {
    return await DataService.db.getLicenses();
  }

  public static async addLicense(license: Omit<License, 'id'>): Promise<number> {
    return await DataService.db.createLicense(license);
  }

  public static async updateLicense(licenseId: number, updates: Partial<License>): Promise<boolean> {
    return await DataService.db.updateLicense(licenseId, updates);
  }

  public static async deleteLicense(licenseId: number): Promise<boolean> {
    return await DataService.db.deleteLicense(licenseId);
  }

  // 获取下一个可用ID（SQLite自动处理，但保留兼容性）
  public static async getNextUserId(): Promise<number> {
    const users = await DataService.getUsers();
    return users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1;
  }

  public static async getNextSoftwareId(): Promise<number> {
    const software = await DataService.getSoftware();
    return software.length > 0 ? Math.max(...software.map(s => s.id)) + 1 : 1;
  }

  public static async getNextLicenseId(): Promise<number> {
    const licenses = await DataService.getLicenses();
    return licenses.length > 0 ? Math.max(...licenses.map(l => l.id)) + 1 : 1;
  }

  // 日志操作
  public static async createLog(log: { userId?: number; username: string; action: string; object?: string; result: string; details?: string }): Promise<number> {
    return await DataService.db.createLog(log);
  }

  public static async getLogs(limit: number = 100) {
    return await DataService.db.getLogs(limit);
  }

  // 关闭数据库连接
  public static async close(): Promise<void> {
    if (DataService.db) {
      await DataService.db.close();
    }
  }
} 