import { Sequelize } from 'sequelize-typescript';
import path from 'path';
import { User } from '../models/User';
import { Software } from '../models/Software';
import { License } from '../models/License';

const dbPath = process.env.DB_PATH || path.join(process.cwd(), 'database', 'license_management.db');

export const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: dbPath,
  models: [User, Software, License],
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  define: {
    timestamps: true,
    underscored: false,
  },
});

export const initializeDatabase = async (): Promise<void> => {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 同步模型到数据库
    await sequelize.sync({ alter: true });
    console.log('数据库模型同步完成');
  } catch (error) {
    console.error('数据库连接失败:', error);
    throw error;
  }
}; 