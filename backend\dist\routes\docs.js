"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
// API文档数据
const apiDocs = {
    title: '软件权限管理系统 API',
    version: '1.0.0',
    description: '提供用户认证和许可证管理功能的RESTful API',
    baseUrl: '/api',
    endpoints: [
        {
            group: '认证相关',
            apis: [
                {
                    method: 'POST',
                    path: '/auth/register',
                    description: '用户注册',
                    requestBody: {
                        username: 'string (3-50字符)',
                        email: 'string (有效邮箱)',
                        password: 'string (密码)',
                        roles: 'string[] (可选，默认为["user"])'
                    },
                    responses: {
                        201: '注册成功',
                        400: '请求参数错误'
                    }
                },
                {
                    method: 'POST',
                    path: '/auth/login',
                    description: '用户登录',
                    requestBody: {
                        username: 'string (用户名)',
                        password: 'string (密码)'
                    },
                    responses: {
                        200: '登录成功，返回JWT令牌',
                        401: '用户名或密码错误'
                    },
                    example: {
                        username: 'admin',
                        password: 'admin123'
                    }
                },
                {
                    method: 'GET',
                    path: '/auth/profile',
                    description: '获取当前用户信息',
                    headers: {
                        Authorization: 'Bearer {jwt-token}'
                    },
                    responses: {
                        200: '成功返回用户信息',
                        401: '未认证或令牌无效'
                    }
                },
                {
                    method: 'PUT',
                    path: '/auth/profile',
                    description: '更新用户信息',
                    headers: {
                        Authorization: 'Bearer {jwt-token}'
                    },
                    requestBody: {
                        username: 'string (可选)',
                        email: 'string (可选)',
                        password: 'string (可选)'
                    },
                    responses: {
                        200: '更新成功',
                        401: '未认证或令牌无效'
                    }
                }
            ]
        },
        {
            group: '许可证管理',
            apis: [
                {
                    method: 'POST',
                    path: '/licenses/activate',
                    description: '激活许可证',
                    requestBody: {
                        productKey: 'string (产品密钥)',
                        machineId: 'string (机器标识)',
                        activationType: 'string (online|offline)'
                    },
                    responses: {
                        200: '激活成功',
                        400: '激活失败，参数错误或产品密钥无效'
                    },
                    example: {
                        productKey: 'ABCD-EFGH-IJKL-MNOP',
                        machineId: 'unique-machine-id',
                        activationType: 'online'
                    }
                },
                {
                    method: 'POST',
                    path: '/licenses/validate/{licenseKey}',
                    description: '验证许可证',
                    parameters: {
                        licenseKey: 'string (许可证密钥)'
                    },
                    requestBody: {
                        machineId: 'string (可选，机器标识)'
                    },
                    responses: {
                        200: '验证结果',
                        400: '请求参数错误'
                    }
                },
                {
                    method: 'GET',
                    path: '/licenses/{licenseKey}',
                    description: '获取许可证信息',
                    parameters: {
                        licenseKey: 'string (许可证密钥)'
                    },
                    headers: {
                        Authorization: 'Bearer {jwt-token}'
                    },
                    responses: {
                        200: '成功返回许可证信息',
                        401: '未认证',
                        404: '许可证不存在'
                    }
                },
                {
                    method: 'DELETE',
                    path: '/licenses/{licenseKey}/revoke',
                    description: '撤销许可证',
                    parameters: {
                        licenseKey: 'string (许可证密钥)'
                    },
                    headers: {
                        Authorization: 'Bearer {jwt-token}'
                    },
                    responses: {
                        200: '撤销成功',
                        401: '未认证或权限不足',
                        404: '许可证不存在'
                    }
                }
            ]
        }
    ],
    systemEndpoints: [
        {
            method: 'GET',
            path: '/health',
            description: '健康检查',
            responses: {
                200: '服务正常运行'
            }
        },
        {
            method: 'GET',
            path: '/',
            description: '根路径，返回API基本信息'
        },
        {
            method: 'GET',
            path: '/api/docs',
            description: 'API文档（JSON格式）'
        }
    ]
};
// 获取API文档
router.get('/', (_req, res) => {
    res.json(apiDocs);
});
exports.default = router;
//# sourceMappingURL=docs.js.map