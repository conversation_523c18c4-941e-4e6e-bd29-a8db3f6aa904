import { Request, Response } from 'express';
export declare class LicenseController {
    /**
     * 激活许可证
     */
    static activate(req: Request, res: Response): Promise<Response>;
    /**
     * 验证许可证
     */
    static validate(req: Request, res: Response): Promise<Response>;
    /**
     * 获取许可证信息
     */
    static getLicense(req: Request, res: Response): Promise<Response>;
    /**
     * 撤销许可证
     */
    static revoke(req: Request, res: Response): Promise<Response>;
}
//# sourceMappingURL=LicenseController.d.ts.map