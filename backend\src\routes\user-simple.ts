import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { UserService } from '../services/UserService';
import { DataService } from '../services/DataService';

const router = Router();

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }
    
    console.log(`[用户登录] 尝试登录用户: ${username}`);
    
    // 查找用户
    const user = await DataService.getUserByUsername(username);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 验证密码
    const isValidPassword = await UserService.verifyPassword(password, user.passwordHash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 生成JWT令牌
    const tokenPayload = {
      id: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles
    };
    
    const token = UserService.generateToken(tokenPayload);
    
    console.log(`[用户登录] 用户 ${username} 登录成功`);
    
    return res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          roles: user.roles
        }
      }
    });
  } catch (error) {
    console.error('[用户登录] 登录失败:', error);
    return res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱和密码为必填项'
      });
    }
    
    // 验证密码长度
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: '密码长度至少为6位'
      });
    }
    
    // 检查用户名是否已存在
    const existingUser = await DataService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }
    
    // 哈希密码
    const passwordHash = await UserService.hashPassword(password);
    
    const newUserId = await DataService.addUser({
      username,
      email,
      passwordHash,
      roles: ['user'],
      createdAt: new Date().toISOString()
    });
    
    console.log(`[用户注册] 用户: ${username}, ID: ${newUserId}`);
    
    return res.status(201).json({
      success: true,
      message: '用户注册成功',
      data: {
        id: newUserId,
        username,
        email,
        roles: ['user']
      }
    });
  } catch (error) {
    console.error('[用户注册] 注册失败:', error);
    return res.status(500).json({
      success: false,
      message: '注册失败，请稍后重试'
    });
  }
});

// 获取软件列表
router.get('/software', authenticateToken, async (_req, res) => {
  try {
    const software = await DataService.getSoftware();
    const activeSoftware = software.filter(s => s.isActive);
    
    const softwareWithLicenseInfo = activeSoftware.map(software => ({
      id: software.id,
      name: software.name,
      description: software.description,
      price: software.price,
      trialDays: software.trialDays
    }));
    
    return res.json({
      success: true,
      data: softwareWithLicenseInfo
    });
  } catch (error) {
    console.error('[获取软件列表] 失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取软件列表失败'
    });
  }
});

// 获取用户许可证
router.get('/licenses', authenticateToken, async (req, res) => {
  try {
    const licenses = await DataService.getLicenses();
    const software = await DataService.getSoftware();
    
    const userLicenses = licenses
      .filter(l => l.userId === req.user.id)
      .map(license => {
        const softwareItem = software.find(s => s.id === license.softwareId);
        return {
          ...license,
          softwareName: softwareItem ? softwareItem.name : '未知软件'
        };
      });
    
    return res.json({
      success: true,
      data: userLicenses
    });
  } catch (error) {
    console.error('[获取用户许可证] 失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取许可证失败'
    });
  }
});

// 申请试用
router.post('/trial', authenticateToken, async (req, res) => {
  try {
    const { softwareId } = req.body;
    const userId = req.user.id;
    
    if (!softwareId) {
      return res.status(400).json({
        success: false,
        message: '软件ID为必填项'
      });
    }
    
    const software = await DataService.getSoftware();
    const licenses = await DataService.getLicenses();
    
    // 检查软件是否存在
    const targetSoftware = software.find(s => s.id === parseInt(softwareId) && s.isActive);
    if (!targetSoftware) {
      return res.status(404).json({
        success: false,
        message: '软件不存在或已禁用'
      });
    }
    
    // 检查用户是否已有该软件的有效许可证
    const existingLicense = licenses.find(l => 
      l.userId === userId && 
      l.softwareId === parseInt(softwareId) && 
      !l.isRevoked &&
      (!l.expiresAt || new Date(l.expiresAt) > new Date())
    );
    
    if (existingLicense) {
      return res.status(400).json({
        success: false,
        message: '您已拥有该软件的有效许可证'
      });
    }
    
    // 生成试用许可证
    const trialExpiresAt = new Date();
    trialExpiresAt.setDate(trialExpiresAt.getDate() + targetSoftware.trialDays);
    
    const licenseKey = `TRIAL-${Date.now()}-${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
    
    const newLicense = {
      licenseKey,
      softwareId: parseInt(softwareId),
      userId,
      activationType: 'offline',
      activatedAt: new Date().toISOString(),
      expiresAt: trialExpiresAt.toISOString(),
      machineId: '',
      isRevoked: false,
      isTrial: true,
      createdAt: new Date().toISOString()
    };
    
    // 保存到持久化存储
    await DataService.addLicense(newLicense);
    
    console.log(`[试用申请] 用户: ${req.user.username}, 软件: ${targetSoftware.name}, 许可证: ${licenseKey}`);
    
    return res.json({
      success: true,
      message: `试用许可证生成成功，有效期${targetSoftware.trialDays}天`,
      data: {
        licenseKey: newLicense.licenseKey,
        expiresAt: newLicense.expiresAt,
        trialDays: targetSoftware.trialDays
      }
    });
  } catch (error) {
    console.error('[试用申请] 失败:', error);
    return res.status(500).json({
      success: false,
      message: '申请试用失败'
    });
  }
});

// 获取激活文件
router.get('/activation-file/:licenseKey/:machineId', authenticateToken, async (req, res) => {
  try {
    const { licenseKey, machineId: targetMachineId } = req.params;
    
    console.log(`[激活文件下载] 用户: ${req.user.username}, 许可证: ${licenseKey}, 机器ID: ${targetMachineId}`);
    
    // 查找许可证
    const licenses = await DataService.getLicenses();
    const license = licenses.find(l => l.licenseKey === licenseKey && l.userId === req.user.id);
    
    if (!license) {
      return res.status(404).json({
        success: false,
        message: '未找到许可证'
      });
    }
    
    // 检查许可证状态
    if (license.isRevoked) {
      return res.status(403).json({
        success: false,
        message: '许可证已被撤销'
      });
    }
    
    // 检查是否过期
    if (license.expiresAt && new Date(license.expiresAt) < new Date()) {
      return res.status(403).json({
        success: false,
        message: '许可证已过期'
      });
    }
    
    // 获取软件信息
    const software = await DataService.getSoftware();
    const softwareItem = software.find(s => s.id === license.softwareId);
    
    if (!softwareItem) {
      return res.status(404).json({
        success: false,
        message: '软件不存在'
      });
    }
    
    // 获取用户信息
    const users = await DataService.getUsers();
    const licensedUser = users.find(u => u.id === license.userId);
    
    // 创建激活数据
    const activationData = {
      licenseKey: license.licenseKey,
      productKey: softwareItem.productKey,
      machineId: targetMachineId,
      softwareName: softwareItem.name,
      licensedUser: licensedUser ? licensedUser.username : '未知用户',
      activationType: license.activationType,
      issuedAt: license.createdAt,
      expiresAt: license.expiresAt,
      isTrial: license.isTrial
    };
    
    // 生成数字签名
    const signatureData = JSON.stringify(activationData);
    const signature = UserService.generateSignature(signatureData);
    
    const result = {
      ...activationData,
      signature
    };
    
    // 设置下载头
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="activation_${licenseKey}_${targetMachineId}.json"`);
    
    console.log(`[激活文件] 生成成功，用户: ${req.user.username}, 许可证: ${licenseKey}`);
    
    return res.json(result);
  } catch (error) {
    console.error('[激活文件下载] 失败:', error);
    return res.status(500).json({
      success: false,
      message: '生成激活文件失败'
    });
  }
});

// 在线激活
router.post('/activate', authenticateToken, async (req, res) => {
  try {
    const { productKey, machineId } = req.body;
    const userId = req.user.id;
    
    console.log(`[在线激活] 用户: ${req.user.username}, 产品密钥: ${productKey}, 机器ID: ${machineId}`);
    
    if (!productKey || !machineId) {
      return res.status(400).json({
        success: false,
        message: '产品密钥和机器ID不能为空'
      });
    }
    
    // 查找软件
    const software = await DataService.getSoftware();
    const targetSoftware = software.find(s => s.productKey === productKey && s.isActive);
    
    if (!targetSoftware) {
      return res.status(404).json({
        success: false,
        message: '产品密钥无效或软件已禁用'
      });
    }
    
    // 查找用户的许可证
    const licenses = await DataService.getLicenses();
    const userLicenses = licenses.filter(l => 
      l.userId === userId && 
      l.softwareId === targetSoftware.id && 
      !l.isRevoked
    );
    
    if (userLicenses.length === 0) {
      return res.status(403).json({
        success: false,
        message: '您没有该软件的有效许可证，请先申请试用或购买许可证'
      });
    }
    
    // 找到最佳许可证（优先级：未过期的正式版 > 未过期的试用版 > 已过期的）
    const now = new Date();
    const sortedLicenses = userLicenses.sort((a, b) => {
      const aExpired = a.expiresAt && new Date(a.expiresAt) < now;
      const bExpired = b.expiresAt && new Date(b.expiresAt) < now;
      
      // 未过期的排在前面
      if (aExpired !== bExpired) {
        return aExpired ? 1 : -1;
      }
      
      // 都未过期时，正式版排在试用版前面
      if (!aExpired && !bExpired) {
        if (a.isTrial !== b.isTrial) {
          return a.isTrial ? 1 : -1;
        }
      }
      
      // 按过期时间排序（晚过期的排在前面）
      if (a.expiresAt && b.expiresAt) {
        return new Date(b.expiresAt).getTime() - new Date(a.expiresAt).getTime();
      }
      
      return 0;
    });
    
    const userLicense = sortedLicenses[0];
    
    console.log(`[在线激活] 找到${userLicenses.length}个许可证，选择最佳: ${userLicense.licenseKey}`);
    console.log(`[在线激活] 许可证详情: 过期时间=${userLicense.expiresAt}, 是否试用=${userLicense.isTrial}`);
    
    // 检查最佳许可证是否过期
    if (userLicense.expiresAt && new Date(userLicense.expiresAt) < now) {
      return res.status(403).json({
        success: false,
        message: `许可证已过期 (过期时间: ${new Date(userLicense.expiresAt).toLocaleString()})`
      });
    }
    
    // 检查是否已在其他机器激活（如果有机器绑定限制）
    if (userLicense.machineId && userLicense.machineId !== machineId) {
      return res.status(403).json({
        success: false,
        message: `许可证已绑定到其他设备 (${userLicense.machineId})`
      });
    }
    
    // 更新许可证的机器绑定和激活时间
    await DataService.updateLicense(userLicense.id, {
      machineId: machineId,
      activatedAt: new Date().toISOString()
    });
    
    // 记录激活日志
    await DataService.createLog({
      userId: req.user.id,
      username: req.user.username,
      action: '在线激活',
      object: `软件: ${targetSoftware.name}, 机器: ${machineId}`,
      result: '成功'
    });
    
    console.log(`[在线激活] 成功: 用户: ${req.user.username}, 软件: ${targetSoftware.name}, 许可证: ${userLicense.licenseKey}`);
    
    return res.json({
      success: true,
      message: '激活成功',
      data: {
        licenseKey: userLicense.licenseKey,
        softwareName: targetSoftware.name,
        activationType: 'online',
        expiresAt: userLicense.expiresAt,
        isTrial: userLicense.isTrial,
        machineId: machineId
      }
    });
  } catch (error) {
    console.error('[在线激活] 失败:', error);
    return res.status(500).json({
      success: false,
      message: '激活失败，请稍后重试'
    });
  }
});

export default router; 