{"name": "has", "description": "Object.prototype.hasOwnProperty.call shortcut", "version": "1.0.4", "homepage": "https://github.com/tarruda/has", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "repository": {"type": "git", "url": "git://github.com/tarruda/has.git"}, "bugs": {"url": "https://github.com/tarruda/has/issues"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/tarruda/has/blob/master/LICENSE-MIT"}], "main": "./src", "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "eslint": "^4.19.1", "tape": "^4.9.0"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "tape test"}}