import { Request, Response } from 'express';
export declare class UserController {
    /**
     * 用户注册
     */
    static register(req: Request, res: Response): Promise<Response>;
    /**
     * 用户登录
     */
    static login(req: Request, res: Response): Promise<Response>;
    /**
     * 获取当前用户信息
     */
    static getProfile(req: Request, res: Response): Promise<Response>;
    /**
     * 更新用户信息
     */
    static updateProfile(req: Request, res: Response): Promise<Response>;
}
//# sourceMappingURL=UserController.d.ts.map