{"version": 3, "names": ["STATEMENT_OR_BLOCK_KEYS", "FLATTENABLE_KEYS", "FOR_INIT_KEYS", "COMMENT_KEYS", "LOGICAL_OPERATORS", "UPDATE_OPERATORS", "BOOLEAN_NUMBER_BINARY_OPERATORS", "EQUALITY_BINARY_OPERATORS", "COMPARISON_BINARY_OPERATORS", "BOOLEAN_BINARY_OPERATORS", "NUMBER_BINARY_OPERATORS", "BINARY_OPERATORS", "ASSIGNMENT_OPERATORS", "map", "op", "BOOLEAN_UNARY_OPERATORS", "NUMBER_UNARY_OPERATORS", "STRING_UNARY_OPERATORS", "UNARY_OPERATORS", "INHERIT_KEYS", "optional", "force", "BLOCK_SCOPED_SYMBOL", "Symbol", "for", "NOT_LOCAL_BINDING"], "sources": ["../../src/constants/index.ts"], "sourcesContent": ["export const STATEMENT_OR_BLOCK_KEYS = [\"consequent\", \"body\", \"alternate\"];\nexport const FLATTENABLE_KEYS = [\"body\", \"expressions\"];\nexport const FOR_INIT_KEYS = [\"left\", \"init\"];\nexport const COMMENT_KEYS = [\n  \"leadingComments\",\n  \"trailingComments\",\n  \"innerComments\",\n] as const;\n\nexport const LOGICAL_OPERATORS = [\"||\", \"&&\", \"??\"];\nexport const UPDATE_OPERATORS = [\"++\", \"--\"];\n\nexport const BOOLEAN_NUMBER_BINARY_OPERATORS = [\">\", \"<\", \">=\", \"<=\"];\nexport const EQUALITY_BINARY_OPERATORS = [\"==\", \"===\", \"!=\", \"!==\"];\nexport const COMPARISON_BINARY_OPERATORS = [\n  ...EQUALITY_BINARY_OPERATORS,\n  \"in\",\n  \"instanceof\",\n];\nexport const BOOLEAN_BINARY_OPERATORS = [\n  ...COMPARISON_BINARY_OPERATORS,\n  ...BOOLEAN_NUMBER_BINARY_OPERATORS,\n];\nexport const NUMBER_BINARY_OPERATORS = [\n  \"-\",\n  \"/\",\n  \"%\",\n  \"*\",\n  \"**\",\n  \"&\",\n  \"|\",\n  \">>\",\n  \">>>\",\n  \"<<\",\n  \"^\",\n];\nexport const BINARY_OPERATORS = [\n  \"+\",\n  ...NUMBER_BINARY_OPERATORS,\n  ...BOOLEAN_BINARY_OPERATORS,\n  \"|>\",\n];\n\nexport const ASSIGNMENT_OPERATORS = [\n  \"=\",\n  \"+=\",\n  ...NUMBER_BINARY_OPERATORS.map(op => op + \"=\"),\n  ...LOGICAL_OPERATORS.map(op => op + \"=\"),\n];\n\nexport const BOOLEAN_UNARY_OPERATORS = [\"delete\", \"!\"];\nexport const NUMBER_UNARY_OPERATORS = [\"+\", \"-\", \"~\"];\nexport const STRING_UNARY_OPERATORS = [\"typeof\"];\nexport const UNARY_OPERATORS = [\n  \"void\",\n  \"throw\",\n  ...BOOLEAN_UNARY_OPERATORS,\n  ...NUMBER_UNARY_OPERATORS,\n  ...STRING_UNARY_OPERATORS,\n];\n\nexport const INHERIT_KEYS = {\n  optional: [\"typeAnnotation\", \"typeParameters\", \"returnType\"],\n  force: [\"start\", \"loc\", \"end\"],\n} as const;\n\nexport const BLOCK_SCOPED_SYMBOL = Symbol.for(\"var used to be block scoped\");\nexport const NOT_LOCAL_BINDING = Symbol.for(\n  \"should not be considered a local binding\",\n);\n"], "mappings": ";;;;;;AAAO,MAAMA,uBAAuB,GAAG,CAAC,YAAD,EAAe,MAAf,EAAuB,WAAvB,CAAhC;;AACA,MAAMC,gBAAgB,GAAG,CAAC,MAAD,EAAS,aAAT,CAAzB;;AACA,MAAMC,aAAa,GAAG,CAAC,MAAD,EAAS,MAAT,CAAtB;;AACA,MAAMC,YAAY,GAAG,CAC1B,iBAD0B,EAE1B,kBAF0B,EAG1B,eAH0B,CAArB;;AAMA,MAAMC,iBAAiB,GAAG,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,CAA1B;;AACA,MAAMC,gBAAgB,GAAG,CAAC,IAAD,EAAO,IAAP,CAAzB;;AAEA,MAAMC,+BAA+B,GAAG,CAAC,GAAD,EAAM,GAAN,EAAW,IAAX,EAAiB,IAAjB,CAAxC;;AACA,MAAMC,yBAAyB,GAAG,CAAC,IAAD,EAAO,KAAP,EAAc,IAAd,EAAoB,KAApB,CAAlC;;AACA,MAAMC,2BAA2B,GAAG,CACzC,GAAGD,yBADsC,EAEzC,IAFyC,EAGzC,YAHyC,CAApC;;AAKA,MAAME,wBAAwB,GAAG,CACtC,GAAGD,2BADmC,EAEtC,GAAGF,+BAFmC,CAAjC;;AAIA,MAAMI,uBAAuB,GAAG,CACrC,GADqC,EAErC,GAFqC,EAGrC,GAHqC,EAIrC,GAJqC,EAKrC,IALqC,EAMrC,GANqC,EAOrC,GAPqC,EAQrC,IARqC,EASrC,KATqC,EAUrC,IAVqC,EAWrC,GAXqC,CAAhC;;AAaA,MAAMC,gBAAgB,GAAG,CAC9B,GAD8B,EAE9B,GAAGD,uBAF2B,EAG9B,GAAGD,wBAH2B,EAI9B,IAJ8B,CAAzB;;AAOA,MAAMG,oBAAoB,GAAG,CAClC,GADkC,EAElC,IAFkC,EAGlC,GAAGF,uBAAuB,CAACG,GAAxB,CAA4BC,EAAE,IAAIA,EAAE,GAAG,GAAvC,CAH+B,EAIlC,GAAGV,iBAAiB,CAACS,GAAlB,CAAsBC,EAAE,IAAIA,EAAE,GAAG,GAAjC,CAJ+B,CAA7B;;AAOA,MAAMC,uBAAuB,GAAG,CAAC,QAAD,EAAW,GAAX,CAAhC;;AACA,MAAMC,sBAAsB,GAAG,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAA/B;;AACA,MAAMC,sBAAsB,GAAG,CAAC,QAAD,CAA/B;;AACA,MAAMC,eAAe,GAAG,CAC7B,MAD6B,EAE7B,OAF6B,EAG7B,GAAGH,uBAH0B,EAI7B,GAAGC,sBAJ0B,EAK7B,GAAGC,sBAL0B,CAAxB;;AAQA,MAAME,YAAY,GAAG;EAC1BC,QAAQ,EAAE,CAAC,gBAAD,EAAmB,gBAAnB,EAAqC,YAArC,CADgB;EAE1BC,KAAK,EAAE,CAAC,OAAD,EAAU,KAAV,EAAiB,KAAjB;AAFmB,CAArB;;AAKA,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,GAAP,CAAW,6BAAX,CAA5B;;AACA,MAAMC,iBAAiB,GAAGF,MAAM,CAACC,GAAP,CAC/B,0CAD+B,CAA1B"}