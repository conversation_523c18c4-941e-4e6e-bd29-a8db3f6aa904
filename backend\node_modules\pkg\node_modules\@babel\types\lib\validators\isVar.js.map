{"version": 3, "names": ["isVar", "node", "isVariableDeclaration", "kind", "BLOCK_SCOPED_SYMBOL"], "sources": ["../../src/validators/isVar.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated\";\nimport { BLOCK_SCOPED_SYMBOL } from \"../constants\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is a variable declaration.\n */\nexport default function isVar(node: t.Node): boolean {\n  return (\n    isVariableDeclaration(node, { kind: \"var\" }) &&\n    !(\n      // @ts-expect-error document private properties\n      node[BLOCK_SCOPED_SYMBOL]\n    )\n  );\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAMe,SAASA,KAAT,CAAeC,IAAf,EAAsC;EACnD,OACE,IAAAC,gCAAA,EAAsBD,IAAtB,EAA4B;IAAEE,IAAI,EAAE;EAAR,CAA5B,KACA,CAEEF,IAAI,CAACG,8BAAD,CAJR;AAOD"}