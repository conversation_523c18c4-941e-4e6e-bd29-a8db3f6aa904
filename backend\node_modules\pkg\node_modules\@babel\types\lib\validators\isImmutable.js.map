{"version": 3, "names": ["isImmutable", "node", "isType", "type", "isIdentifier", "name"], "sources": ["../../src/validators/isImmutable.ts"], "sourcesContent": ["import isType from \"./isType\";\nimport { isIdentifier } from \"./generated\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is definitely immutable.\n */\nexport default function isImmutable(node: t.Node): boolean {\n  if (isType(node.type, \"Immutable\")) return true;\n\n  if (isIdentifier(node)) {\n    if (node.name === \"undefined\") {\n      // immutable!\n      return true;\n    } else {\n      // no idea...\n      return false;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAMe,SAASA,WAAT,CAAqBC,IAArB,EAA4C;EACzD,IAAI,IAAAC,eAAA,EAAOD,IAAI,CAACE,IAAZ,EAAkB,WAAlB,CAAJ,EAAoC,OAAO,IAAP;;EAEpC,IAAI,IAAAC,uBAAA,EAAaH,IAAb,CAAJ,EAAwB;IACtB,IAAIA,IAAI,CAACI,IAAL,KAAc,WAAlB,EAA+B;MAE7B,OAAO,IAAP;IACD,CAHD,MAGO;MAEL,OAAO,KAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD"}