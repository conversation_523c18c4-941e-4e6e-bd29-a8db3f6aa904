"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseController = void 0;
const LicenseService_1 = require("../services/LicenseService");
class LicenseController {
    /**
     * 激活许可证
     */
    static async activate(req, res) {
        try {
            const { productKey, machineId, activationType } = req.body;
            // 验证必填字段
            if (!productKey || !machineId || !activationType) {
                return res.status(400).json({
                    success: false,
                    message: '产品密钥、机器ID和激活类型为必填项'
                });
            }
            // 验证激活类型
            if (!['online', 'offline'].includes(activationType)) {
                return res.status(400).json({
                    success: false,
                    message: '无效的激活类型'
                });
            }
            // 模拟查找软件和许可证数据
            // 在实际应用中，这里会查询数据库
            const mockSoftware = {
                id: 1,
                name: '示例软件',
                productKey: 'ABCD-EFGH-IJKL-MNOP',
                isActive: true
            };
            // 验证产品密钥
            if (productKey !== mockSoftware.productKey) {
                return res.status(400).json(LicenseService_1.LicenseService.createActivationResponse(false, null, '无效的产品密钥'));
            }
            // 检查软件是否激活
            if (!mockSoftware.isActive) {
                return res.status(400).json(LicenseService_1.LicenseService.createActivationResponse(false, null, '软件已被禁用'));
            }
            // 生成新的许可证密钥
            const licenseKey = LicenseService_1.LicenseService.generateLicenseKey();
            const expiresAt = new Date();
            expiresAt.setFullYear(expiresAt.getFullYear() + 1); // 1年有效期
            // 模拟创建许可证记录
            const licenseData = {
                licenseKey,
                activationType,
                activatedAt: new Date(),
                expiresAt,
                machineId,
                isRevoked: false,
                softwareId: mockSoftware.id
            };
            // 检查是否可以激活
            if (!LicenseService_1.LicenseService.canActivateLicense(licenseData)) {
                return res.status(400).json(LicenseService_1.LicenseService.createActivationResponse(false, null, '许可证无法激活'));
            }
            const response = LicenseService_1.LicenseService.createActivationResponse(true, {
                licenseKey,
                activationType,
                expiresAt
            }, '许可证激活成功');
            return res.json(response);
        }
        catch (error) {
            return res.status(500).json(LicenseService_1.LicenseService.createActivationResponse(false, null, error.message || '激活失败'));
        }
    }
    /**
     * 验证许可证
     */
    static async validate(req, res) {
        try {
            const { licenseKey } = req.params;
            const { machineId } = req.body;
            if (!licenseKey) {
                return res.status(400).json(LicenseService_1.LicenseService.createValidationResponse(false, false, null, '许可证密钥为必填项'));
            }
            // 模拟查找许可证数据
            const mockLicense = {
                id: 1,
                licenseKey: licenseKey,
                activationType: 'online',
                activatedAt: new Date('2024-01-01'),
                expiresAt: new Date('2025-12-31'),
                machineId: machineId || 'default-machine-id',
                isRevoked: false,
                softwareId: 1
            };
            // 验证许可证是否存在
            if (mockLicense.licenseKey !== licenseKey) {
                return res.status(404).json(LicenseService_1.LicenseService.createValidationResponse(false, false, null, '许可证不存在'));
            }
            // 验证机器ID
            if (machineId && !LicenseService_1.LicenseService.validateMachineId(mockLicense, machineId)) {
                return res.status(400).json(LicenseService_1.LicenseService.createValidationResponse(false, false, null, '机器ID不匹配'));
            }
            // 验证许可证是否有效
            const isValid = LicenseService_1.LicenseService.validateLicense(mockLicense);
            if (!isValid) {
                const status = LicenseService_1.LicenseService.getLicenseStatus(mockLicense);
                let message = '许可证无效';
                switch (status) {
                    case 'expired':
                        message = '许可证已过期';
                        break;
                    case 'revoked':
                        message = '许可证已被撤销';
                        break;
                }
                return res.json(LicenseService_1.LicenseService.createValidationResponse(true, false, null, message));
            }
            const response = LicenseService_1.LicenseService.createValidationResponse(true, true, {
                expiresAt: mockLicense.expiresAt
            }, '许可证验证成功');
            return res.json(response);
        }
        catch (error) {
            return res.status(500).json(LicenseService_1.LicenseService.createValidationResponse(false, false, null, error.message || '验证失败'));
        }
    }
    /**
     * 获取许可证信息
     */
    static async getLicense(req, res) {
        try {
            const { licenseKey } = req.params;
            if (!licenseKey) {
                return res.status(400).json({
                    success: false,
                    message: '许可证密钥为必填项'
                });
            }
            // 模拟许可证数据
            const mockLicense = {
                id: 1,
                licenseKey,
                activationType: 'online',
                activatedAt: new Date('2024-01-01'),
                expiresAt: new Date('2025-12-31'),
                machineId: 'default-machine-id',
                isRevoked: false,
                status: 'active',
                software: {
                    name: '示例软件',
                    productKey: 'ABCD-EFGH-IJKL-MNOP'
                }
            };
            return res.json({
                success: true,
                license: mockLicense
            });
        }
        catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || '获取许可证信息失败'
            });
        }
    }
    /**
     * 撤销许可证
     */
    static async revoke(req, res) {
        try {
            const { licenseKey } = req.params;
            if (!licenseKey) {
                return res.status(400).json({
                    success: false,
                    message: '许可证密钥为必填项'
                });
            }
            // 这里应该更新数据库中的许可证状态
            return res.json({
                success: true,
                message: '许可证已撤销'
            });
        }
        catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || '撤销许可证失败'
            });
        }
    }
}
exports.LicenseController = LicenseController;
//# sourceMappingURL=LicenseController.js.map