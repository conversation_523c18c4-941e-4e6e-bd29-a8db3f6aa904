import { User, Software, License } from './DatabaseService';
export { User, Software, License } from './DatabaseService';
export declare class DataService {
    private static db;
    static initialize(): Promise<void>;
    private static insertDefaultDataIfNeeded;
    static getUsers(): Promise<User[]>;
    static getUserById(id: number): Promise<User | null>;
    static getUserByUsername(username: string): Promise<User | null>;
    static addUser(user: Omit<User, 'id'>): Promise<number>;
    static updateUser(userId: number, updates: Partial<User>): Promise<boolean>;
    static deleteUser(userId: number): Promise<boolean>;
    static getSoftware(): Promise<Software[]>;
    static addSoftware(software: Omit<Software, 'id'>): Promise<number>;
    static updateSoftware(softwareId: number, updates: Partial<Software>): Promise<boolean>;
    static deleteSoftware(softwareId: number): Promise<boolean>;
    static getLicenses(): Promise<License[]>;
    static addLicense(license: Omit<License, 'id'>): Promise<number>;
    static updateLicense(licenseId: number, updates: Partial<License>): Promise<boolean>;
    static deleteLicense(licenseId: number): Promise<boolean>;
    static getNextUserId(): Promise<number>;
    static getNextSoftwareId(): Promise<number>;
    static getNextLicenseId(): Promise<number>;
    static createLog(log: {
        userId?: number;
        username: string;
        action: string;
        object?: string;
        result: string;
        details?: string;
    }): Promise<number>;
    static getLogs(limit?: number): Promise<import("./DatabaseService").Log[]>;
    static close(): Promise<void>;
}
//# sourceMappingURL=DataService.d.ts.map