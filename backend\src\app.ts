import express from 'express';
import cors from 'cors';
import path from 'path';
import { DataService } from './services/DataService';

// 导入路由
import authRoutes from './routes/auth';
import adminRoutes from './routes/admin';
import userRoutes from './routes/user-simple';

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 提供静态文件
app.use(express.static(path.join(__dirname, '../public')));

// 应用路由
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/user', userRoutes);

// 默认路由
app.get('/', (_req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 启动服务器
async function startServer() {
  try {
    // 初始化数据服务
    await DataService.initialize();
    
    // 启动服务器
    app.listen(PORT, () => {
      console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
      console.log(`📊 管理后台: http://localhost:${PORT}/admin.html`);
      console.log(`👤 用户界面: http://localhost:${PORT}/user.html`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  await DataService.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  await DataService.close();
  process.exit(0);
});

// 启动服务器
startServer(); 