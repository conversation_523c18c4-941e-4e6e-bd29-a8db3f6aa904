import { Model, Table, Column, DataType, BelongsTo, ForeignKey } from 'sequelize-typescript';
import { User } from './User';
import { Software } from './Software';

@Table({
  tableName: 'licenses',
  timestamps: true,
})
export class License extends Model<License> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    validate: {
      len: [10, 100],
    },
  })
  licenseKey!: string;

  @Column({
    type: DataType.ENUM('online', 'offline'),
    allowNull: false,
  })
  activationType!: 'online' | 'offline';

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  activatedAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expiresAt?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    validate: {
      len: [1, 200],
    },
  })
  machineId?: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isRevoked!: boolean;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId!: number;

  @ForeignKey(() => Software)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  softwareId!: number;

  @BelongsTo(() => User)
  user!: User;

  @BelongsTo(() => Software)
  software!: Software;
} 