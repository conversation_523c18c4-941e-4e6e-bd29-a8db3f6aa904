{"version": 3, "names": ["isSpecifierDefault", "specifier", "isImportDefaultSpecifier", "isIdentifier", "imported", "exported", "name"], "sources": ["../../src/validators/isSpecifierDefault.ts"], "sourcesContent": ["import { isIdentifier, isImportDefaultSpecifier } from \"./generated\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `specifier` is a `default` import or export.\n */\nexport default function isSpecifierDefault(\n  specifier: t.ModuleSpecifier,\n): boolean {\n  return (\n    isImportDefaultSpecifier(specifier) ||\n    // @ts-expect-error todo(flow->ts): stricter type for specifier\n    isIdentifier(specifier.imported || specifier.exported, {\n      name: \"default\",\n    })\n  );\n}\n"], "mappings": ";;;;;;;AAAA;;AAMe,SAASA,kBAAT,CACbC,SADa,EAEJ;EACT,OACE,IAAAC,mCAAA,EAAyBD,SAAzB,KAEA,IAAAE,uBAAA,EAAaF,SAAS,CAACG,QAAV,IAAsBH,SAAS,CAACI,QAA7C,EAAuD;IACrDC,IAAI,EAAE;EAD+C,CAAvD,CAHF;AAOD"}