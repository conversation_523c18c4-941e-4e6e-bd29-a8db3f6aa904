{"version": 3, "names": ["defineType", "defineAliasedType", "bool", "assertValueType", "tSFunctionTypeAnnotationCommon", "returnType", "validate", "assertNodeType", "optional", "typeParameters", "aliases", "visitor", "fields", "accessibility", "assertOneOf", "readonly", "parameter", "override", "decorators", "chain", "assertEach", "functionDeclaration<PERSON>ommon", "classMethodOrDeclareMethodCommon", "left", "validateType", "right", "signatureDeclarationCommon", "validateOptionalType", "validateArrayOfType", "callConstructSignatureDeclaration", "namedTypeElementCommon", "key", "computed", "default", "validateOptional", "typeAnnotation", "initializer", "kind", "static", "parameters", "tsKeywordTypes", "type", "fnOrCtrBase", "abstract", "typeName", "builder", "parameterName", "asserts", "exprName", "members", "elementType", "elementTypes", "label", "unionOrIntersection", "types", "checkType", "extendsType", "trueType", "falseType", "typeParameter", "operator", "objectType", "indexType", "nameType", "literal", "unaryExpression", "unaryOperator", "validator", "parent", "node", "is", "argument", "oneOfNodeTypes", "expression", "declare", "id", "extends", "arrayOfType", "body", "const", "global", "qualifier", "isExport", "moduleReference", "importKind", "params", "name", "in", "out", "constraint"], "sources": ["../../src/definitions/typescript.ts"], "sourcesContent": ["import {\n  defineAliasedType,\n  arrayOfType,\n  assertEach,\n  assertNodeType,\n  assertOneOf,\n  assertValueType,\n  chain,\n  validate,\n  validateArrayOfType,\n  validateOptional,\n  validateOptionalType,\n  validateType,\n} from \"./utils\";\nimport {\n  functionDeclarationCommon,\n  classMethodOrDeclareMethodCommon,\n} from \"./core\";\nimport is from \"../validators/is\";\n\nconst defineType = defineAliasedType(\"TypeScript\");\n\nconst bool = assertValueType(\"boolean\");\n\nconst tSFunctionTypeAnnotationCommon = () => ({\n  returnType: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TSTypeAnnotation\")\n      : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        assertNodeType(\"TSTypeAnnotation\", \"Noop\"),\n    optional: true,\n  },\n  typeParameters: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TSTypeParameterDeclaration\")\n      : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        assertNodeType(\"TSTypeParameterDeclaration\", \"Noop\"),\n    optional: true,\n  },\n});\n\ndefineType(\"TSParameterProperty\", {\n  aliases: [\"LVal\"], // TODO: This isn't usable in general as an LVal. Should have a \"Parameter\" alias.\n  visitor: [\"parameter\"],\n  fields: {\n    accessibility: {\n      validate: assertOneOf(\"public\", \"private\", \"protected\"),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    parameter: {\n      validate: assertNodeType(\"Identifier\", \"AssignmentPattern\"),\n    },\n    override: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TSDeclareFunction\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"typeParameters\", \"params\", \"returnType\"],\n  fields: {\n    ...functionDeclarationCommon(),\n    ...tSFunctionTypeAnnotationCommon(),\n  },\n});\n\ndefineType(\"TSDeclareMethod\", {\n  visitor: [\"decorators\", \"key\", \"typeParameters\", \"params\", \"returnType\"],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...tSFunctionTypeAnnotationCommon(),\n  },\n});\n\ndefineType(\"TSQualifiedName\", {\n  aliases: [\"TSEntityName\"],\n  visitor: [\"left\", \"right\"],\n  fields: {\n    left: validateType(\"TSEntityName\"),\n    right: validateType(\"Identifier\"),\n  },\n});\n\nconst signatureDeclarationCommon = () => ({\n  typeParameters: validateOptionalType(\"TSTypeParameterDeclaration\"),\n  [process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\"]: validateArrayOfType(\n    [\"Identifier\", \"RestElement\"],\n  ),\n  [process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\"]:\n    validateOptionalType(\"TSTypeAnnotation\"),\n});\n\nconst callConstructSignatureDeclaration = {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\n    \"typeParameters\",\n    process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\",\n    process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\",\n  ],\n  fields: signatureDeclarationCommon(),\n};\n\ndefineType(\"TSCallSignatureDeclaration\", callConstructSignatureDeclaration);\ndefineType(\n  \"TSConstructSignatureDeclaration\",\n  callConstructSignatureDeclaration,\n);\n\nconst namedTypeElementCommon = () => ({\n  key: validateType(\"Expression\"),\n  computed: { default: false },\n  optional: validateOptional(bool),\n});\n\ndefineType(\"TSPropertySignature\", {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\"key\", \"typeAnnotation\", \"initializer\"],\n  fields: {\n    ...namedTypeElementCommon(),\n    readonly: validateOptional(bool),\n    typeAnnotation: validateOptionalType(\"TSTypeAnnotation\"),\n    initializer: validateOptionalType(\"Expression\"),\n    kind: {\n      validate: assertOneOf(\"get\", \"set\"),\n    },\n  },\n});\n\ndefineType(\"TSMethodSignature\", {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\n    \"key\",\n    \"typeParameters\",\n    process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\",\n    process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\",\n  ],\n  fields: {\n    ...signatureDeclarationCommon(),\n    ...namedTypeElementCommon(),\n    kind: {\n      validate: assertOneOf(\"method\", \"get\", \"set\"),\n    },\n  },\n});\n\ndefineType(\"TSIndexSignature\", {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\"parameters\", \"typeAnnotation\"],\n  fields: {\n    readonly: validateOptional(bool),\n    static: validateOptional(bool),\n    parameters: validateArrayOfType(\"Identifier\"), // Length must be 1\n    typeAnnotation: validateOptionalType(\"TSTypeAnnotation\"),\n  },\n});\n\nconst tsKeywordTypes = [\n  \"TSAnyKeyword\",\n  \"TSBooleanKeyword\",\n  \"TSBigIntKeyword\",\n  \"TSIntrinsicKeyword\",\n  \"TSNeverKeyword\",\n  \"TSNullKeyword\",\n  \"TSNumberKeyword\",\n  \"TSObjectKeyword\",\n  \"TSStringKeyword\",\n  \"TSSymbolKeyword\",\n  \"TSUndefinedKeyword\",\n  \"TSUnknownKeyword\",\n  \"TSVoidKeyword\",\n] as const;\n\nfor (const type of tsKeywordTypes) {\n  defineType(type, {\n    aliases: [\"TSType\", \"TSBaseType\"],\n    visitor: [],\n    fields: {},\n  });\n}\n\ndefineType(\"TSThisType\", {\n  aliases: [\"TSType\", \"TSBaseType\"],\n  visitor: [],\n  fields: {},\n});\n\nconst fnOrCtrBase = {\n  aliases: [\"TSType\"],\n  visitor: [\n    \"typeParameters\",\n    process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\",\n    process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\",\n  ],\n};\n\ndefineType(\"TSFunctionType\", {\n  ...fnOrCtrBase,\n  fields: signatureDeclarationCommon(),\n});\ndefineType(\"TSConstructorType\", {\n  ...fnOrCtrBase,\n  fields: {\n    ...signatureDeclarationCommon(),\n    abstract: validateOptional(bool),\n  },\n});\n\ndefineType(\"TSTypeReference\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeName\", \"typeParameters\"],\n  fields: {\n    typeName: validateType(\"TSEntityName\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSTypePredicate\", {\n  aliases: [\"TSType\"],\n  visitor: [\"parameterName\", \"typeAnnotation\"],\n  builder: [\"parameterName\", \"typeAnnotation\", \"asserts\"],\n  fields: {\n    parameterName: validateType([\"Identifier\", \"TSThisType\"]),\n    typeAnnotation: validateOptionalType(\"TSTypeAnnotation\"),\n    asserts: validateOptional(bool),\n  },\n});\n\ndefineType(\"TSTypeQuery\", {\n  aliases: [\"TSType\"],\n  visitor: [\"exprName\", \"typeParameters\"],\n  fields: {\n    exprName: validateType([\"TSEntityName\", \"TSImportType\"]),\n    typeParameters: validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSTypeLiteral\", {\n  aliases: [\"TSType\"],\n  visitor: [\"members\"],\n  fields: {\n    members: validateArrayOfType(\"TSTypeElement\"),\n  },\n});\n\ndefineType(\"TSArrayType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"elementType\"],\n  fields: {\n    elementType: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTupleType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"elementTypes\"],\n  fields: {\n    elementTypes: validateArrayOfType([\"TSType\", \"TSNamedTupleMember\"]),\n  },\n});\n\ndefineType(\"TSOptionalType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSRestType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSNamedTupleMember\", {\n  visitor: [\"label\", \"elementType\"],\n  builder: [\"label\", \"elementType\", \"optional\"],\n  fields: {\n    label: validateType(\"Identifier\"),\n    optional: {\n      validate: bool,\n      default: false,\n    },\n    elementType: validateType(\"TSType\"),\n  },\n});\n\nconst unionOrIntersection = {\n  aliases: [\"TSType\"],\n  visitor: [\"types\"],\n  fields: {\n    types: validateArrayOfType(\"TSType\"),\n  },\n};\n\ndefineType(\"TSUnionType\", unionOrIntersection);\ndefineType(\"TSIntersectionType\", unionOrIntersection);\n\ndefineType(\"TSConditionalType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"checkType\", \"extendsType\", \"trueType\", \"falseType\"],\n  fields: {\n    checkType: validateType(\"TSType\"),\n    extendsType: validateType(\"TSType\"),\n    trueType: validateType(\"TSType\"),\n    falseType: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSInferType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeParameter\"],\n  fields: {\n    typeParameter: validateType(\"TSTypeParameter\"),\n  },\n});\n\ndefineType(\"TSParenthesizedType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTypeOperator\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    operator: validate(assertValueType(\"string\")),\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSIndexedAccessType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"objectType\", \"indexType\"],\n  fields: {\n    objectType: validateType(\"TSType\"),\n    indexType: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSMappedType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeParameter\", \"typeAnnotation\", \"nameType\"],\n  fields: {\n    readonly: validateOptional(assertOneOf(true, false, \"+\", \"-\")),\n    typeParameter: validateType(\"TSTypeParameter\"),\n    optional: validateOptional(assertOneOf(true, false, \"+\", \"-\")),\n    typeAnnotation: validateOptionalType(\"TSType\"),\n    nameType: validateOptionalType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSLiteralType\", {\n  aliases: [\"TSType\", \"TSBaseType\"],\n  visitor: [\"literal\"],\n  fields: {\n    literal: {\n      validate: (function () {\n        const unaryExpression = assertNodeType(\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        );\n        const unaryOperator = assertOneOf(\"-\");\n\n        const literal = assertNodeType(\n          \"NumericLiteral\",\n          \"StringLiteral\",\n          \"BooleanLiteral\",\n          \"BigIntLiteral\",\n          \"TemplateLiteral\",\n        );\n        function validator(parent: any, key: string, node: any) {\n          // type A = -1 | 1;\n          if (is(\"UnaryExpression\", node)) {\n            // check operator first\n            unaryOperator(node, \"operator\", node.operator);\n            unaryExpression(node, \"argument\", node.argument);\n          } else {\n            // type A = 'foo' | 'bar' | false | 1;\n            literal(parent, key, node);\n          }\n        }\n\n        validator.oneOfNodeTypes = [\n          \"NumericLiteral\",\n          \"StringLiteral\",\n          \"BooleanLiteral\",\n          \"BigIntLiteral\",\n          \"TemplateLiteral\",\n          \"UnaryExpression\",\n        ];\n\n        return validator;\n      })(),\n    },\n  },\n});\n\ndefineType(\"TSExpressionWithTypeArguments\", {\n  aliases: [\"TSType\"],\n  visitor: [\"expression\", \"typeParameters\"],\n  fields: {\n    expression: validateType(\"TSEntityName\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSInterfaceDeclaration\", {\n  // \"Statement\" alias prevents a semicolon from appearing after it in an export declaration.\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"typeParameters\", \"extends\", \"body\"],\n  fields: {\n    declare: validateOptional(bool),\n    id: validateType(\"Identifier\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterDeclaration\"),\n    extends: validateOptional(arrayOfType(\"TSExpressionWithTypeArguments\")),\n    body: validateType(\"TSInterfaceBody\"),\n  },\n});\n\ndefineType(\"TSInterfaceBody\", {\n  visitor: [\"body\"],\n  fields: {\n    body: validateArrayOfType(\"TSTypeElement\"),\n  },\n});\n\ndefineType(\"TSTypeAliasDeclaration\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"typeParameters\", \"typeAnnotation\"],\n  fields: {\n    declare: validateOptional(bool),\n    id: validateType(\"Identifier\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterDeclaration\"),\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSInstantiationExpression\", {\n  aliases: [\"Expression\"],\n  visitor: [\"expression\", \"typeParameters\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSAsExpression\", {\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  visitor: [\"expression\", \"typeAnnotation\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTypeAssertion\", {\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  visitor: [\"typeAnnotation\", \"expression\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n    expression: validateType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSEnumDeclaration\", {\n  // \"Statement\" alias prevents a semicolon from appearing after it in an export declaration.\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"members\"],\n  fields: {\n    declare: validateOptional(bool),\n    const: validateOptional(bool),\n    id: validateType(\"Identifier\"),\n    members: validateArrayOfType(\"TSEnumMember\"),\n    initializer: validateOptionalType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSEnumMember\", {\n  visitor: [\"id\", \"initializer\"],\n  fields: {\n    id: validateType([\"Identifier\", \"StringLiteral\"]),\n    initializer: validateOptionalType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSModuleDeclaration\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"body\"],\n  fields: {\n    declare: validateOptional(bool),\n    global: validateOptional(bool),\n    id: validateType([\"Identifier\", \"StringLiteral\"]),\n    body: validateType([\"TSModuleBlock\", \"TSModuleDeclaration\"]),\n  },\n});\n\ndefineType(\"TSModuleBlock\", {\n  aliases: [\"Scopable\", \"Block\", \"BlockParent\"],\n  visitor: [\"body\"],\n  fields: {\n    body: validateArrayOfType(\"Statement\"),\n  },\n});\n\ndefineType(\"TSImportType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"argument\", \"qualifier\", \"typeParameters\"],\n  fields: {\n    argument: validateType(\"StringLiteral\"),\n    qualifier: validateOptionalType(\"TSEntityName\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSImportEqualsDeclaration\", {\n  aliases: [\"Statement\"],\n  visitor: [\"id\", \"moduleReference\"],\n  fields: {\n    isExport: validate(bool),\n    id: validateType(\"Identifier\"),\n    moduleReference: validateType([\n      \"TSEntityName\",\n      \"TSExternalModuleReference\",\n    ]),\n    importKind: {\n      validate: assertOneOf(\"type\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TSExternalModuleReference\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: validateType(\"StringLiteral\"),\n  },\n});\n\ndefineType(\"TSNonNullExpression\", {\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSExportAssignment\", {\n  aliases: [\"Statement\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSNamespaceExportDeclaration\", {\n  aliases: [\"Statement\"],\n  visitor: [\"id\"],\n  fields: {\n    id: validateType(\"Identifier\"),\n  },\n});\n\ndefineType(\"TSTypeAnnotation\", {\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: {\n      validate: assertNodeType(\"TSType\"),\n    },\n  },\n});\n\ndefineType(\"TSTypeParameterInstantiation\", {\n  visitor: [\"params\"],\n  fields: {\n    params: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"TSType\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"TSTypeParameterDeclaration\", {\n  visitor: [\"params\"],\n  fields: {\n    params: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"TSTypeParameter\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"TSTypeParameter\", {\n  builder: [\"constraint\", \"default\", \"name\"],\n  visitor: [\"constraint\", \"default\"],\n  fields: {\n    name: {\n      validate: !process.env.BABEL_8_BREAKING\n        ? assertValueType(\"string\")\n        : assertNodeType(\"Identifier\"),\n    },\n    in: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    out: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    constraint: {\n      validate: assertNodeType(\"TSType\"),\n      optional: true,\n    },\n    default: {\n      validate: assertNodeType(\"TSType\"),\n      optional: true,\n    },\n  },\n});\n"], "mappings": ";;AAAA;;AAcA;;AAIA;;AAEA,MAAMA,UAAU,GAAG,IAAAC,wBAAA,EAAkB,YAAlB,CAAnB;AAEA,MAAMC,IAAI,GAAG,IAAAC,sBAAA,EAAgB,SAAhB,CAAb;;AAEA,MAAMC,8BAA8B,GAAG,OAAO;EAC5CC,UAAU,EAAE;IACVC,QAAQ,EAGJ,IAAAC,qBAAA,EAAe,kBAAf,EAAmC,MAAnC,CAJM;IAKVC,QAAQ,EAAE;EALA,CADgC;EAQ5CC,cAAc,EAAE;IACdH,QAAQ,EAGJ,IAAAC,qBAAA,EAAe,4BAAf,EAA6C,MAA7C,CAJU;IAKdC,QAAQ,EAAE;EALI;AAR4B,CAAP,CAAvC;;AAiBAR,UAAU,CAAC,qBAAD,EAAwB;EAChCU,OAAO,EAAE,CAAC,MAAD,CADuB;EAEhCC,OAAO,EAAE,CAAC,WAAD,CAFuB;EAGhCC,MAAM,EAAE;IACNC,aAAa,EAAE;MACbP,QAAQ,EAAE,IAAAQ,kBAAA,EAAY,QAAZ,EAAsB,SAAtB,EAAiC,WAAjC,CADG;MAEbN,QAAQ,EAAE;IAFG,CADT;IAKNO,QAAQ,EAAE;MACRT,QAAQ,EAAE,IAAAH,sBAAA,EAAgB,SAAhB,CADF;MAERK,QAAQ,EAAE;IAFF,CALJ;IASNQ,SAAS,EAAE;MACTV,QAAQ,EAAE,IAAAC,qBAAA,EAAe,YAAf,EAA6B,mBAA7B;IADD,CATL;IAYNU,QAAQ,EAAE;MACRX,QAAQ,EAAE,IAAAH,sBAAA,EAAgB,SAAhB,CADF;MAERK,QAAQ,EAAE;IAFF,CAZJ;IAgBNU,UAAU,EAAE;MACVZ,QAAQ,EAAE,IAAAa,YAAA,EACR,IAAAhB,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAiB,iBAAA,EAAW,IAAAb,qBAAA,EAAe,WAAf,CAAX,CAFQ,CADA;MAKVC,QAAQ,EAAE;IALA;EAhBN;AAHwB,CAAxB,CAAV;AA6BAR,UAAU,CAAC,mBAAD,EAAsB;EAC9BU,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CADqB;EAE9BC,OAAO,EAAE,CAAC,IAAD,EAAO,gBAAP,EAAyB,QAAzB,EAAmC,YAAnC,CAFqB;EAG9BC,MAAM,oBACD,IAAAS,+BAAA,GADC,EAEDjB,8BAA8B,EAF7B;AAHwB,CAAtB,CAAV;AASAJ,UAAU,CAAC,iBAAD,EAAoB;EAC5BW,OAAO,EAAE,CAAC,YAAD,EAAe,KAAf,EAAsB,gBAAtB,EAAwC,QAAxC,EAAkD,YAAlD,CADmB;EAE5BC,MAAM,oBACD,IAAAU,sCAAA,GADC,EAEDlB,8BAA8B,EAF7B;AAFsB,CAApB,CAAV;AAQAJ,UAAU,CAAC,iBAAD,EAAoB;EAC5BU,OAAO,EAAE,CAAC,cAAD,CADmB;EAE5BC,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CAFmB;EAG5BC,MAAM,EAAE;IACNW,IAAI,EAAE,IAAAC,mBAAA,EAAa,cAAb,CADA;IAENC,KAAK,EAAE,IAAAD,mBAAA,EAAa,YAAb;EAFD;AAHoB,CAApB,CAAV;;AASA,MAAME,0BAA0B,GAAG,OAAO;EACxCjB,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,4BAArB,CADwB;EAExC,CAA2C,YAA3C,GAA0D,IAAAC,0BAAA,EACxD,CAAC,YAAD,EAAe,aAAf,CADwD,CAFlB;EAKxC,CAA+C,gBAA/C,GACE,IAAAD,2BAAA,EAAqB,kBAArB;AANsC,CAAP,CAAnC;;AASA,MAAME,iCAAiC,GAAG;EACxCnB,OAAO,EAAE,CAAC,eAAD,CAD+B;EAExCC,OAAO,EAAE,CACP,gBADO,EAEmC,YAFnC,EAGuC,gBAHvC,CAF+B;EAOxCC,MAAM,EAAEc,0BAA0B;AAPM,CAA1C;AAUA1B,UAAU,CAAC,4BAAD,EAA+B6B,iCAA/B,CAAV;AACA7B,UAAU,CACR,iCADQ,EAER6B,iCAFQ,CAAV;;AAKA,MAAMC,sBAAsB,GAAG,OAAO;EACpCC,GAAG,EAAE,IAAAP,mBAAA,EAAa,YAAb,CAD+B;EAEpCQ,QAAQ,EAAE;IAAEC,OAAO,EAAE;EAAX,CAF0B;EAGpCzB,QAAQ,EAAE,IAAA0B,uBAAA,EAAiBhC,IAAjB;AAH0B,CAAP,CAA/B;;AAMAF,UAAU,CAAC,qBAAD,EAAwB;EAChCU,OAAO,EAAE,CAAC,eAAD,CADuB;EAEhCC,OAAO,EAAE,CAAC,KAAD,EAAQ,gBAAR,EAA0B,aAA1B,CAFuB;EAGhCC,MAAM,oBACDkB,sBAAsB,EADrB;IAEJf,QAAQ,EAAE,IAAAmB,uBAAA,EAAiBhC,IAAjB,CAFN;IAGJiC,cAAc,EAAE,IAAAR,2BAAA,EAAqB,kBAArB,CAHZ;IAIJS,WAAW,EAAE,IAAAT,2BAAA,EAAqB,YAArB,CAJT;IAKJU,IAAI,EAAE;MACJ/B,QAAQ,EAAE,IAAAQ,kBAAA,EAAY,KAAZ,EAAmB,KAAnB;IADN;EALF;AAH0B,CAAxB,CAAV;AAcAd,UAAU,CAAC,mBAAD,EAAsB;EAC9BU,OAAO,EAAE,CAAC,eAAD,CADqB;EAE9BC,OAAO,EAAE,CACP,KADO,EAEP,gBAFO,EAGmC,YAHnC,EAIuC,gBAJvC,CAFqB;EAQ9BC,MAAM,oBACDc,0BAA0B,EADzB,EAEDI,sBAAsB,EAFrB;IAGJO,IAAI,EAAE;MACJ/B,QAAQ,EAAE,IAAAQ,kBAAA,EAAY,QAAZ,EAAsB,KAAtB,EAA6B,KAA7B;IADN;EAHF;AARwB,CAAtB,CAAV;AAiBAd,UAAU,CAAC,kBAAD,EAAqB;EAC7BU,OAAO,EAAE,CAAC,eAAD,CADoB;EAE7BC,OAAO,EAAE,CAAC,YAAD,EAAe,gBAAf,CAFoB;EAG7BC,MAAM,EAAE;IACNG,QAAQ,EAAE,IAAAmB,uBAAA,EAAiBhC,IAAjB,CADJ;IAENoC,MAAM,EAAE,IAAAJ,uBAAA,EAAiBhC,IAAjB,CAFF;IAGNqC,UAAU,EAAE,IAAAX,0BAAA,EAAoB,YAApB,CAHN;IAINO,cAAc,EAAE,IAAAR,2BAAA,EAAqB,kBAArB;EAJV;AAHqB,CAArB,CAAV;AAWA,MAAMa,cAAc,GAAG,CACrB,cADqB,EAErB,kBAFqB,EAGrB,iBAHqB,EAIrB,oBAJqB,EAKrB,gBALqB,EAMrB,eANqB,EAOrB,iBAPqB,EAQrB,iBARqB,EASrB,iBATqB,EAUrB,iBAVqB,EAWrB,oBAXqB,EAYrB,kBAZqB,EAarB,eAbqB,CAAvB;;AAgBA,KAAK,MAAMC,IAAX,IAAmBD,cAAnB,EAAmC;EACjCxC,UAAU,CAACyC,IAAD,EAAO;IACf/B,OAAO,EAAE,CAAC,QAAD,EAAW,YAAX,CADM;IAEfC,OAAO,EAAE,EAFM;IAGfC,MAAM,EAAE;EAHO,CAAP,CAAV;AAKD;;AAEDZ,UAAU,CAAC,YAAD,EAAe;EACvBU,OAAO,EAAE,CAAC,QAAD,EAAW,YAAX,CADc;EAEvBC,OAAO,EAAE,EAFc;EAGvBC,MAAM,EAAE;AAHe,CAAf,CAAV;AAMA,MAAM8B,WAAW,GAAG;EAClBhC,OAAO,EAAE,CAAC,QAAD,CADS;EAElBC,OAAO,EAAE,CACP,gBADO,EAEmC,YAFnC,EAGuC,gBAHvC;AAFS,CAApB;AASAX,UAAU,CAAC,gBAAD,oBACL0C,WADK;EAER9B,MAAM,EAAEc,0BAA0B;AAF1B,GAAV;AAIA1B,UAAU,CAAC,mBAAD,oBACL0C,WADK;EAER9B,MAAM,oBACDc,0BAA0B,EADzB;IAEJiB,QAAQ,EAAE,IAAAT,uBAAA,EAAiBhC,IAAjB;EAFN;AAFE,GAAV;AAQAF,UAAU,CAAC,iBAAD,EAAoB;EAC5BU,OAAO,EAAE,CAAC,QAAD,CADmB;EAE5BC,OAAO,EAAE,CAAC,UAAD,EAAa,gBAAb,CAFmB;EAG5BC,MAAM,EAAE;IACNgC,QAAQ,EAAE,IAAApB,mBAAA,EAAa,cAAb,CADJ;IAENf,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,8BAArB;EAFV;AAHoB,CAApB,CAAV;AASA3B,UAAU,CAAC,iBAAD,EAAoB;EAC5BU,OAAO,EAAE,CAAC,QAAD,CADmB;EAE5BC,OAAO,EAAE,CAAC,eAAD,EAAkB,gBAAlB,CAFmB;EAG5BkC,OAAO,EAAE,CAAC,eAAD,EAAkB,gBAAlB,EAAoC,SAApC,CAHmB;EAI5BjC,MAAM,EAAE;IACNkC,aAAa,EAAE,IAAAtB,mBAAA,EAAa,CAAC,YAAD,EAAe,YAAf,CAAb,CADT;IAENW,cAAc,EAAE,IAAAR,2BAAA,EAAqB,kBAArB,CAFV;IAGNoB,OAAO,EAAE,IAAAb,uBAAA,EAAiBhC,IAAjB;EAHH;AAJoB,CAApB,CAAV;AAWAF,UAAU,CAAC,aAAD,EAAgB;EACxBU,OAAO,EAAE,CAAC,QAAD,CADe;EAExBC,OAAO,EAAE,CAAC,UAAD,EAAa,gBAAb,CAFe;EAGxBC,MAAM,EAAE;IACNoC,QAAQ,EAAE,IAAAxB,mBAAA,EAAa,CAAC,cAAD,EAAiB,cAAjB,CAAb,CADJ;IAENf,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,8BAArB;EAFV;AAHgB,CAAhB,CAAV;AASA3B,UAAU,CAAC,eAAD,EAAkB;EAC1BU,OAAO,EAAE,CAAC,QAAD,CADiB;EAE1BC,OAAO,EAAE,CAAC,SAAD,CAFiB;EAG1BC,MAAM,EAAE;IACNqC,OAAO,EAAE,IAAArB,0BAAA,EAAoB,eAApB;EADH;AAHkB,CAAlB,CAAV;AAQA5B,UAAU,CAAC,aAAD,EAAgB;EACxBU,OAAO,EAAE,CAAC,QAAD,CADe;EAExBC,OAAO,EAAE,CAAC,aAAD,CAFe;EAGxBC,MAAM,EAAE;IACNsC,WAAW,EAAE,IAAA1B,mBAAA,EAAa,QAAb;EADP;AAHgB,CAAhB,CAAV;AAQAxB,UAAU,CAAC,aAAD,EAAgB;EACxBU,OAAO,EAAE,CAAC,QAAD,CADe;EAExBC,OAAO,EAAE,CAAC,cAAD,CAFe;EAGxBC,MAAM,EAAE;IACNuC,YAAY,EAAE,IAAAvB,0BAAA,EAAoB,CAAC,QAAD,EAAW,oBAAX,CAApB;EADR;AAHgB,CAAhB,CAAV;AAQA5B,UAAU,CAAC,gBAAD,EAAmB;EAC3BU,OAAO,EAAE,CAAC,QAAD,CADkB;EAE3BC,OAAO,EAAE,CAAC,gBAAD,CAFkB;EAG3BC,MAAM,EAAE;IACNuB,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb;EADV;AAHmB,CAAnB,CAAV;AAQAxB,UAAU,CAAC,YAAD,EAAe;EACvBU,OAAO,EAAE,CAAC,QAAD,CADc;EAEvBC,OAAO,EAAE,CAAC,gBAAD,CAFc;EAGvBC,MAAM,EAAE;IACNuB,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb;EADV;AAHe,CAAf,CAAV;AAQAxB,UAAU,CAAC,oBAAD,EAAuB;EAC/BW,OAAO,EAAE,CAAC,OAAD,EAAU,aAAV,CADsB;EAE/BkC,OAAO,EAAE,CAAC,OAAD,EAAU,aAAV,EAAyB,UAAzB,CAFsB;EAG/BjC,MAAM,EAAE;IACNwC,KAAK,EAAE,IAAA5B,mBAAA,EAAa,YAAb,CADD;IAENhB,QAAQ,EAAE;MACRF,QAAQ,EAAEJ,IADF;MAER+B,OAAO,EAAE;IAFD,CAFJ;IAMNiB,WAAW,EAAE,IAAA1B,mBAAA,EAAa,QAAb;EANP;AAHuB,CAAvB,CAAV;AAaA,MAAM6B,mBAAmB,GAAG;EAC1B3C,OAAO,EAAE,CAAC,QAAD,CADiB;EAE1BC,OAAO,EAAE,CAAC,OAAD,CAFiB;EAG1BC,MAAM,EAAE;IACN0C,KAAK,EAAE,IAAA1B,0BAAA,EAAoB,QAApB;EADD;AAHkB,CAA5B;AAQA5B,UAAU,CAAC,aAAD,EAAgBqD,mBAAhB,CAAV;AACArD,UAAU,CAAC,oBAAD,EAAuBqD,mBAAvB,CAAV;AAEArD,UAAU,CAAC,mBAAD,EAAsB;EAC9BU,OAAO,EAAE,CAAC,QAAD,CADqB;EAE9BC,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,EAA6B,UAA7B,EAAyC,WAAzC,CAFqB;EAG9BC,MAAM,EAAE;IACN2C,SAAS,EAAE,IAAA/B,mBAAA,EAAa,QAAb,CADL;IAENgC,WAAW,EAAE,IAAAhC,mBAAA,EAAa,QAAb,CAFP;IAGNiC,QAAQ,EAAE,IAAAjC,mBAAA,EAAa,QAAb,CAHJ;IAINkC,SAAS,EAAE,IAAAlC,mBAAA,EAAa,QAAb;EAJL;AAHsB,CAAtB,CAAV;AAWAxB,UAAU,CAAC,aAAD,EAAgB;EACxBU,OAAO,EAAE,CAAC,QAAD,CADe;EAExBC,OAAO,EAAE,CAAC,eAAD,CAFe;EAGxBC,MAAM,EAAE;IACN+C,aAAa,EAAE,IAAAnC,mBAAA,EAAa,iBAAb;EADT;AAHgB,CAAhB,CAAV;AAQAxB,UAAU,CAAC,qBAAD,EAAwB;EAChCU,OAAO,EAAE,CAAC,QAAD,CADuB;EAEhCC,OAAO,EAAE,CAAC,gBAAD,CAFuB;EAGhCC,MAAM,EAAE;IACNuB,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb;EADV;AAHwB,CAAxB,CAAV;AAQAxB,UAAU,CAAC,gBAAD,EAAmB;EAC3BU,OAAO,EAAE,CAAC,QAAD,CADkB;EAE3BC,OAAO,EAAE,CAAC,gBAAD,CAFkB;EAG3BC,MAAM,EAAE;IACNgD,QAAQ,EAAE,IAAAtD,eAAA,EAAS,IAAAH,sBAAA,EAAgB,QAAhB,CAAT,CADJ;IAENgC,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb;EAFV;AAHmB,CAAnB,CAAV;AASAxB,UAAU,CAAC,qBAAD,EAAwB;EAChCU,OAAO,EAAE,CAAC,QAAD,CADuB;EAEhCC,OAAO,EAAE,CAAC,YAAD,EAAe,WAAf,CAFuB;EAGhCC,MAAM,EAAE;IACNiD,UAAU,EAAE,IAAArC,mBAAA,EAAa,QAAb,CADN;IAENsC,SAAS,EAAE,IAAAtC,mBAAA,EAAa,QAAb;EAFL;AAHwB,CAAxB,CAAV;AASAxB,UAAU,CAAC,cAAD,EAAiB;EACzBU,OAAO,EAAE,CAAC,QAAD,CADgB;EAEzBC,OAAO,EAAE,CAAC,eAAD,EAAkB,gBAAlB,EAAoC,UAApC,CAFgB;EAGzBC,MAAM,EAAE;IACNG,QAAQ,EAAE,IAAAmB,uBAAA,EAAiB,IAAApB,kBAAA,EAAY,IAAZ,EAAkB,KAAlB,EAAyB,GAAzB,EAA8B,GAA9B,CAAjB,CADJ;IAEN6C,aAAa,EAAE,IAAAnC,mBAAA,EAAa,iBAAb,CAFT;IAGNhB,QAAQ,EAAE,IAAA0B,uBAAA,EAAiB,IAAApB,kBAAA,EAAY,IAAZ,EAAkB,KAAlB,EAAyB,GAAzB,EAA8B,GAA9B,CAAjB,CAHJ;IAINqB,cAAc,EAAE,IAAAR,2BAAA,EAAqB,QAArB,CAJV;IAKNoC,QAAQ,EAAE,IAAApC,2BAAA,EAAqB,QAArB;EALJ;AAHiB,CAAjB,CAAV;AAYA3B,UAAU,CAAC,eAAD,EAAkB;EAC1BU,OAAO,EAAE,CAAC,QAAD,EAAW,YAAX,CADiB;EAE1BC,OAAO,EAAE,CAAC,SAAD,CAFiB;EAG1BC,MAAM,EAAE;IACNoD,OAAO,EAAE;MACP1D,QAAQ,EAAG,YAAY;QACrB,MAAM2D,eAAe,GAAG,IAAA1D,qBAAA,EACtB,gBADsB,EAEtB,eAFsB,CAAxB;QAIA,MAAM2D,aAAa,GAAG,IAAApD,kBAAA,EAAY,GAAZ,CAAtB;QAEA,MAAMkD,OAAO,GAAG,IAAAzD,qBAAA,EACd,gBADc,EAEd,eAFc,EAGd,gBAHc,EAId,eAJc,EAKd,iBALc,CAAhB;;QAOA,SAAS4D,SAAT,CAAmBC,MAAnB,EAAgCrC,GAAhC,EAA6CsC,IAA7C,EAAwD;UAEtD,IAAI,IAAAC,WAAA,EAAG,iBAAH,EAAsBD,IAAtB,CAAJ,EAAiC;YAE/BH,aAAa,CAACG,IAAD,EAAO,UAAP,EAAmBA,IAAI,CAACT,QAAxB,CAAb;YACAK,eAAe,CAACI,IAAD,EAAO,UAAP,EAAmBA,IAAI,CAACE,QAAxB,CAAf;UACD,CAJD,MAIO;YAELP,OAAO,CAACI,MAAD,EAASrC,GAAT,EAAcsC,IAAd,CAAP;UACD;QACF;;QAEDF,SAAS,CAACK,cAAV,GAA2B,CACzB,gBADyB,EAEzB,eAFyB,EAGzB,gBAHyB,EAIzB,eAJyB,EAKzB,iBALyB,EAMzB,iBANyB,CAA3B;QASA,OAAOL,SAAP;MACD,CApCS;IADH;EADH;AAHkB,CAAlB,CAAV;AA8CAnE,UAAU,CAAC,+BAAD,EAAkC;EAC1CU,OAAO,EAAE,CAAC,QAAD,CADiC;EAE1CC,OAAO,EAAE,CAAC,YAAD,EAAe,gBAAf,CAFiC;EAG1CC,MAAM,EAAE;IACN6D,UAAU,EAAE,IAAAjD,mBAAA,EAAa,cAAb,CADN;IAENf,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,8BAArB;EAFV;AAHkC,CAAlC,CAAV;AASA3B,UAAU,CAAC,wBAAD,EAA2B;EAEnCU,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CAF0B;EAGnCC,OAAO,EAAE,CAAC,IAAD,EAAO,gBAAP,EAAyB,SAAzB,EAAoC,MAApC,CAH0B;EAInCC,MAAM,EAAE;IACN8D,OAAO,EAAE,IAAAxC,uBAAA,EAAiBhC,IAAjB,CADH;IAENyE,EAAE,EAAE,IAAAnD,mBAAA,EAAa,YAAb,CAFE;IAGNf,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,4BAArB,CAHV;IAINiD,OAAO,EAAE,IAAA1C,uBAAA,EAAiB,IAAA2C,kBAAA,EAAY,+BAAZ,CAAjB,CAJH;IAKNC,IAAI,EAAE,IAAAtD,mBAAA,EAAa,iBAAb;EALA;AAJ2B,CAA3B,CAAV;AAaAxB,UAAU,CAAC,iBAAD,EAAoB;EAC5BW,OAAO,EAAE,CAAC,MAAD,CADmB;EAE5BC,MAAM,EAAE;IACNkE,IAAI,EAAE,IAAAlD,0BAAA,EAAoB,eAApB;EADA;AAFoB,CAApB,CAAV;AAOA5B,UAAU,CAAC,wBAAD,EAA2B;EACnCU,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CAD0B;EAEnCC,OAAO,EAAE,CAAC,IAAD,EAAO,gBAAP,EAAyB,gBAAzB,CAF0B;EAGnCC,MAAM,EAAE;IACN8D,OAAO,EAAE,IAAAxC,uBAAA,EAAiBhC,IAAjB,CADH;IAENyE,EAAE,EAAE,IAAAnD,mBAAA,EAAa,YAAb,CAFE;IAGNf,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,4BAArB,CAHV;IAINQ,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb;EAJV;AAH2B,CAA3B,CAAV;AAWAxB,UAAU,CAAC,2BAAD,EAA8B;EACtCU,OAAO,EAAE,CAAC,YAAD,CAD6B;EAEtCC,OAAO,EAAE,CAAC,YAAD,EAAe,gBAAf,CAF6B;EAGtCC,MAAM,EAAE;IACN6D,UAAU,EAAE,IAAAjD,mBAAA,EAAa,YAAb,CADN;IAENf,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,8BAArB;EAFV;AAH8B,CAA9B,CAAV;AASA3B,UAAU,CAAC,gBAAD,EAAmB;EAC3BU,OAAO,EAAE,CAAC,YAAD,EAAe,MAAf,EAAuB,aAAvB,CADkB;EAE3BC,OAAO,EAAE,CAAC,YAAD,EAAe,gBAAf,CAFkB;EAG3BC,MAAM,EAAE;IACN6D,UAAU,EAAE,IAAAjD,mBAAA,EAAa,YAAb,CADN;IAENW,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb;EAFV;AAHmB,CAAnB,CAAV;AASAxB,UAAU,CAAC,iBAAD,EAAoB;EAC5BU,OAAO,EAAE,CAAC,YAAD,EAAe,MAAf,EAAuB,aAAvB,CADmB;EAE5BC,OAAO,EAAE,CAAC,gBAAD,EAAmB,YAAnB,CAFmB;EAG5BC,MAAM,EAAE;IACNuB,cAAc,EAAE,IAAAX,mBAAA,EAAa,QAAb,CADV;IAENiD,UAAU,EAAE,IAAAjD,mBAAA,EAAa,YAAb;EAFN;AAHoB,CAApB,CAAV;AASAxB,UAAU,CAAC,mBAAD,EAAsB;EAE9BU,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CAFqB;EAG9BC,OAAO,EAAE,CAAC,IAAD,EAAO,SAAP,CAHqB;EAI9BC,MAAM,EAAE;IACN8D,OAAO,EAAE,IAAAxC,uBAAA,EAAiBhC,IAAjB,CADH;IAEN6E,KAAK,EAAE,IAAA7C,uBAAA,EAAiBhC,IAAjB,CAFD;IAGNyE,EAAE,EAAE,IAAAnD,mBAAA,EAAa,YAAb,CAHE;IAINyB,OAAO,EAAE,IAAArB,0BAAA,EAAoB,cAApB,CAJH;IAKNQ,WAAW,EAAE,IAAAT,2BAAA,EAAqB,YAArB;EALP;AAJsB,CAAtB,CAAV;AAaA3B,UAAU,CAAC,cAAD,EAAiB;EACzBW,OAAO,EAAE,CAAC,IAAD,EAAO,aAAP,CADgB;EAEzBC,MAAM,EAAE;IACN+D,EAAE,EAAE,IAAAnD,mBAAA,EAAa,CAAC,YAAD,EAAe,eAAf,CAAb,CADE;IAENY,WAAW,EAAE,IAAAT,2BAAA,EAAqB,YAArB;EAFP;AAFiB,CAAjB,CAAV;AAQA3B,UAAU,CAAC,qBAAD,EAAwB;EAChCU,OAAO,EAAE,CAAC,WAAD,EAAc,aAAd,CADuB;EAEhCC,OAAO,EAAE,CAAC,IAAD,EAAO,MAAP,CAFuB;EAGhCC,MAAM,EAAE;IACN8D,OAAO,EAAE,IAAAxC,uBAAA,EAAiBhC,IAAjB,CADH;IAEN8E,MAAM,EAAE,IAAA9C,uBAAA,EAAiBhC,IAAjB,CAFF;IAGNyE,EAAE,EAAE,IAAAnD,mBAAA,EAAa,CAAC,YAAD,EAAe,eAAf,CAAb,CAHE;IAINsD,IAAI,EAAE,IAAAtD,mBAAA,EAAa,CAAC,eAAD,EAAkB,qBAAlB,CAAb;EAJA;AAHwB,CAAxB,CAAV;AAWAxB,UAAU,CAAC,eAAD,EAAkB;EAC1BU,OAAO,EAAE,CAAC,UAAD,EAAa,OAAb,EAAsB,aAAtB,CADiB;EAE1BC,OAAO,EAAE,CAAC,MAAD,CAFiB;EAG1BC,MAAM,EAAE;IACNkE,IAAI,EAAE,IAAAlD,0BAAA,EAAoB,WAApB;EADA;AAHkB,CAAlB,CAAV;AAQA5B,UAAU,CAAC,cAAD,EAAiB;EACzBU,OAAO,EAAE,CAAC,QAAD,CADgB;EAEzBC,OAAO,EAAE,CAAC,UAAD,EAAa,WAAb,EAA0B,gBAA1B,CAFgB;EAGzBC,MAAM,EAAE;IACN2D,QAAQ,EAAE,IAAA/C,mBAAA,EAAa,eAAb,CADJ;IAENyD,SAAS,EAAE,IAAAtD,2BAAA,EAAqB,cAArB,CAFL;IAGNlB,cAAc,EAAE,IAAAkB,2BAAA,EAAqB,8BAArB;EAHV;AAHiB,CAAjB,CAAV;AAUA3B,UAAU,CAAC,2BAAD,EAA8B;EACtCU,OAAO,EAAE,CAAC,WAAD,CAD6B;EAEtCC,OAAO,EAAE,CAAC,IAAD,EAAO,iBAAP,CAF6B;EAGtCC,MAAM,EAAE;IACNsE,QAAQ,EAAE,IAAA5E,eAAA,EAASJ,IAAT,CADJ;IAENyE,EAAE,EAAE,IAAAnD,mBAAA,EAAa,YAAb,CAFE;IAGN2D,eAAe,EAAE,IAAA3D,mBAAA,EAAa,CAC5B,cAD4B,EAE5B,2BAF4B,CAAb,CAHX;IAON4D,UAAU,EAAE;MACV9E,QAAQ,EAAE,IAAAQ,kBAAA,EAAY,MAAZ,EAAoB,OAApB,CADA;MAEVN,QAAQ,EAAE;IAFA;EAPN;AAH8B,CAA9B,CAAV;AAiBAR,UAAU,CAAC,2BAAD,EAA8B;EACtCW,OAAO,EAAE,CAAC,YAAD,CAD6B;EAEtCC,MAAM,EAAE;IACN6D,UAAU,EAAE,IAAAjD,mBAAA,EAAa,eAAb;EADN;AAF8B,CAA9B,CAAV;AAOAxB,UAAU,CAAC,qBAAD,EAAwB;EAChCU,OAAO,EAAE,CAAC,YAAD,EAAe,MAAf,EAAuB,aAAvB,CADuB;EAEhCC,OAAO,EAAE,CAAC,YAAD,CAFuB;EAGhCC,MAAM,EAAE;IACN6D,UAAU,EAAE,IAAAjD,mBAAA,EAAa,YAAb;EADN;AAHwB,CAAxB,CAAV;AAQAxB,UAAU,CAAC,oBAAD,EAAuB;EAC/BU,OAAO,EAAE,CAAC,WAAD,CADsB;EAE/BC,OAAO,EAAE,CAAC,YAAD,CAFsB;EAG/BC,MAAM,EAAE;IACN6D,UAAU,EAAE,IAAAjD,mBAAA,EAAa,YAAb;EADN;AAHuB,CAAvB,CAAV;AAQAxB,UAAU,CAAC,8BAAD,EAAiC;EACzCU,OAAO,EAAE,CAAC,WAAD,CADgC;EAEzCC,OAAO,EAAE,CAAC,IAAD,CAFgC;EAGzCC,MAAM,EAAE;IACN+D,EAAE,EAAE,IAAAnD,mBAAA,EAAa,YAAb;EADE;AAHiC,CAAjC,CAAV;AAQAxB,UAAU,CAAC,kBAAD,EAAqB;EAC7BW,OAAO,EAAE,CAAC,gBAAD,CADoB;EAE7BC,MAAM,EAAE;IACNuB,cAAc,EAAE;MACd7B,QAAQ,EAAE,IAAAC,qBAAA,EAAe,QAAf;IADI;EADV;AAFqB,CAArB,CAAV;AASAP,UAAU,CAAC,8BAAD,EAAiC;EACzCW,OAAO,EAAE,CAAC,QAAD,CADgC;EAEzCC,MAAM,EAAE;IACNyE,MAAM,EAAE;MACN/E,QAAQ,EAAE,IAAAa,YAAA,EACR,IAAAhB,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAiB,iBAAA,EAAW,IAAAb,qBAAA,EAAe,QAAf,CAAX,CAFQ;IADJ;EADF;AAFiC,CAAjC,CAAV;AAYAP,UAAU,CAAC,4BAAD,EAA+B;EACvCW,OAAO,EAAE,CAAC,QAAD,CAD8B;EAEvCC,MAAM,EAAE;IACNyE,MAAM,EAAE;MACN/E,QAAQ,EAAE,IAAAa,YAAA,EACR,IAAAhB,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAiB,iBAAA,EAAW,IAAAb,qBAAA,EAAe,iBAAf,CAAX,CAFQ;IADJ;EADF;AAF+B,CAA/B,CAAV;AAYAP,UAAU,CAAC,iBAAD,EAAoB;EAC5B6C,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,MAA1B,CADmB;EAE5BlC,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,CAFmB;EAG5BC,MAAM,EAAE;IACN0E,IAAI,EAAE;MACJhF,QAAQ,EACJ,IAAAH,sBAAA,EAAgB,QAAhB;IAFA,CADA;IAMNoF,EAAE,EAAE;MACFjF,QAAQ,EAAE,IAAAH,sBAAA,EAAgB,SAAhB,CADR;MAEFK,QAAQ,EAAE;IAFR,CANE;IAUNgF,GAAG,EAAE;MACHlF,QAAQ,EAAE,IAAAH,sBAAA,EAAgB,SAAhB,CADP;MAEHK,QAAQ,EAAE;IAFP,CAVC;IAcNiF,UAAU,EAAE;MACVnF,QAAQ,EAAE,IAAAC,qBAAA,EAAe,QAAf,CADA;MAEVC,QAAQ,EAAE;IAFA,CAdN;IAkBNyB,OAAO,EAAE;MACP3B,QAAQ,EAAE,IAAAC,qBAAA,EAAe,QAAf,CADH;MAEPC,QAAQ,EAAE;IAFH;EAlBH;AAHoB,CAApB,CAAV"}