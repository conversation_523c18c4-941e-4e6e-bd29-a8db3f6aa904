import crypto from 'crypto';

export class CryptoUtils {
  private static readonly ALGORITHM = 'aes-256-cbc';
  private static readonly IV_LENGTH = 16;

  /**
   * 生成随机许可证密钥
   */
  static generateLicenseKey(): string {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(16).toString('hex');
    return `${timestamp}-${random}`.toUpperCase();
  }

  /**
   * 生成产品密钥
   */
  static generateProductKey(): string {
    const segments: string[] = [];
    for (let i = 0; i < 4; i++) {
      segments.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return segments.join('-');
  }

  /**
   * 加密数据
   */
  static encrypt(text: string, key: string): string {
    const keyBuffer = crypto.scryptSync(key, 'salt', 32);
    const iv = crypto.randomBytes(CryptoUtils.IV_LENGTH);
    const cipher = crypto.createCipheriv(CryptoUtils.ALGORITHM, keyBuffer, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * 解密数据
   */
  static decrypt(encryptedData: string, key: string): string {
    const parts = encryptedData.split(':');
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    const keyBuffer = crypto.scryptSync(key, 'salt', 32);

    const decipher = crypto.createDecipheriv(CryptoUtils.ALGORITHM, keyBuffer, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  /**
   * 生成哈希值
   */
  static hash(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * 验证哈希值
   */
  static verifyHash(data: string, hash: string): boolean {
    return CryptoUtils.hash(data) === hash;
  }

  /**
   * 生成JWT密钥
   */
  static generateJWTSecret(): string {
    return crypto.randomBytes(64).toString('hex');
  }
} 