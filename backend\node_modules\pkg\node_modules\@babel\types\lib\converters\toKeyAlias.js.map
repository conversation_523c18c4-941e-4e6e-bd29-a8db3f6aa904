{"version": 3, "names": ["to<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "key", "alias", "kind", "increment", "isIdentifier", "name", "isStringLiteral", "JSON", "stringify", "value", "removePropertiesDeep", "cloneNode", "computed", "static", "uid", "Number", "MAX_SAFE_INTEGER"], "sources": ["../../src/converters/toKeyAlias.ts"], "sourcesContent": ["import { isIdentifier, isStringLiteral } from \"../validators/generated\";\nimport cloneNode from \"../clone/cloneNode\";\nimport removePropertiesDeep from \"../modifications/removePropertiesDeep\";\nimport type * as t from \"..\";\n\nexport default function toKeyAlias(\n  node: t.Method | t.Property,\n  key: t.Node = node.key,\n): string {\n  let alias;\n\n  // @ts-expect-error todo(flow->ts): maybe add node type check before checking `.kind`\n  if (node.kind === \"method\") {\n    return toKeyAlias.increment() + \"\";\n  } else if (isIdentifier(key)) {\n    alias = key.name;\n  } else if (isStringLiteral(key)) {\n    alias = JSON.stringify(key.value);\n  } else {\n    alias = JSON.stringify(removePropertiesDeep(cloneNode(key)));\n  }\n\n  // @ts-expect-error todo(flow->ts): maybe add node type check before checking `.computed`\n  if (node.computed) {\n    alias = `[${alias}]`;\n  }\n\n  // @ts-expect-error todo(flow->ts): maybe add node type check before checking `.static`\n  if (node.static) {\n    alias = `static:${alias}`;\n  }\n\n  return alias;\n}\n\ntoKeyAlias.uid = 0;\n\ntoKeyAlias.increment = function () {\n  if (toKeyAlias.uid >= Number.MAX_SAFE_INTEGER) {\n    return (toKeyAlias.uid = 0);\n  } else {\n    return toKeyAlias.uid++;\n  }\n};\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAGe,SAASA,UAAT,CACbC,IADa,EAEbC,GAAW,GAAGD,IAAI,CAACC,GAFN,EAGL;EACR,IAAIC,KAAJ;;EAGA,IAAIF,IAAI,CAACG,IAAL,KAAc,QAAlB,EAA4B;IAC1B,OAAOJ,UAAU,CAACK,SAAX,KAAyB,EAAhC;EACD,CAFD,MAEO,IAAI,IAAAC,uBAAA,EAAaJ,GAAb,CAAJ,EAAuB;IAC5BC,KAAK,GAAGD,GAAG,CAACK,IAAZ;EACD,CAFM,MAEA,IAAI,IAAAC,0BAAA,EAAgBN,GAAhB,CAAJ,EAA0B;IAC/BC,KAAK,GAAGM,IAAI,CAACC,SAAL,CAAeR,GAAG,CAACS,KAAnB,CAAR;EACD,CAFM,MAEA;IACLR,KAAK,GAAGM,IAAI,CAACC,SAAL,CAAe,IAAAE,6BAAA,EAAqB,IAAAC,kBAAA,EAAUX,GAAV,CAArB,CAAf,CAAR;EACD;;EAGD,IAAID,IAAI,CAACa,QAAT,EAAmB;IACjBX,KAAK,GAAI,IAAGA,KAAM,GAAlB;EACD;;EAGD,IAAIF,IAAI,CAACc,MAAT,EAAiB;IACfZ,KAAK,GAAI,UAASA,KAAM,EAAxB;EACD;;EAED,OAAOA,KAAP;AACD;;AAEDH,UAAU,CAACgB,GAAX,GAAiB,CAAjB;;AAEAhB,UAAU,CAACK,SAAX,GAAuB,YAAY;EACjC,IAAIL,UAAU,CAACgB,GAAX,IAAkBC,MAAM,CAACC,gBAA7B,EAA+C;IAC7C,OAAQlB,UAAU,CAACgB,GAAX,GAAiB,CAAzB;EACD,CAFD,MAEO;IACL,OAAOhB,UAAU,CAACgB,GAAX,EAAP;EACD;AACF,CAND"}