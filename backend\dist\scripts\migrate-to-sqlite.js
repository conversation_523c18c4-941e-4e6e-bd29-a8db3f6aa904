"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateData = migrateData;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const DatabaseService_1 = require("../services/DatabaseService");
async function migrateData() {
    console.log('开始数据迁移...');
    const db = DatabaseService_1.DatabaseService.getInstance();
    await db.initialize();
    const dataDir = path.join(process.cwd(), 'data');
    try {
        // 迁移用户数据
        const usersPath = path.join(dataDir, 'users.json');
        if (fs.existsSync(usersPath)) {
            console.log('迁移用户数据...');
            const usersData = JSON.parse(fs.readFileSync(usersPath, 'utf8'));
            for (const user of usersData) {
                try {
                    await db.createUser({
                        username: user.username,
                        email: user.email,
                        passwordHash: user.passwordHash,
                        roles: user.roles,
                        createdAt: user.createdAt
                    });
                    console.log(`✓ 用户 ${user.username} 迁移成功`);
                }
                catch (error) {
                    if (error.message.includes('UNIQUE constraint failed')) {
                        console.log(`- 用户 ${user.username} 已存在，跳过`);
                    }
                    else {
                        console.error(`✗ 用户 ${user.username} 迁移失败:`, error.message);
                    }
                }
            }
        }
        // 迁移软件数据
        const softwarePath = path.join(dataDir, 'software.json');
        if (fs.existsSync(softwarePath)) {
            console.log('迁移软件数据...');
            const softwareData = JSON.parse(fs.readFileSync(softwarePath, 'utf8'));
            for (const software of softwareData) {
                try {
                    await db.createSoftware({
                        name: software.name,
                        productKey: software.productKey,
                        description: software.description,
                        isActive: software.isActive,
                        price: software.price,
                        trialDays: software.trialDays,
                        createdAt: software.createdAt
                    });
                    console.log(`✓ 软件 ${software.name} 迁移成功`);
                }
                catch (error) {
                    if (error.message.includes('UNIQUE constraint failed')) {
                        console.log(`- 软件 ${software.name} 已存在，跳过`);
                    }
                    else {
                        console.error(`✗ 软件 ${software.name} 迁移失败:`, error.message);
                    }
                }
            }
        }
        // 迁移许可证数据
        const licensesPath = path.join(dataDir, 'licenses.json');
        if (fs.existsSync(licensesPath)) {
            console.log('迁移许可证数据...');
            const licensesData = JSON.parse(fs.readFileSync(licensesPath, 'utf8'));
            for (const license of licensesData) {
                try {
                    await db.createLicense({
                        licenseKey: license.licenseKey,
                        softwareId: license.softwareId,
                        userId: license.userId,
                        activationType: license.activationType,
                        activatedAt: license.activatedAt,
                        expiresAt: license.expiresAt,
                        machineId: license.machineId,
                        isRevoked: license.isRevoked,
                        isTrial: license.isTrial,
                        createdAt: license.createdAt
                    });
                    console.log(`✓ 许可证 ${license.licenseKey} 迁移成功`);
                }
                catch (error) {
                    if (error.message.includes('UNIQUE constraint failed')) {
                        console.log(`- 许可证 ${license.licenseKey} 已存在，跳过`);
                    }
                    else {
                        console.error(`✗ 许可证 ${license.licenseKey} 迁移失败:`, error.message);
                    }
                }
            }
        }
        console.log('✅ 数据迁移完成！');
        // 备份原有JSON文件
        const backupDir = path.join(dataDir, 'json_backup');
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir);
        }
        const jsonFiles = ['users.json', 'software.json', 'licenses.json'];
        for (const file of jsonFiles) {
            const sourcePath = path.join(dataDir, file);
            const backupPath = path.join(backupDir, file);
            if (fs.existsSync(sourcePath)) {
                fs.copyFileSync(sourcePath, backupPath);
                console.log(`📦 ${file} 已备份到 json_backup/`);
            }
        }
    }
    catch (error) {
        console.error('❌ 数据迁移失败:', error);
    }
    finally {
        await db.close();
    }
}
// 如果直接运行此脚本
if (require.main === module) {
    migrateData().catch(console.error);
}
//# sourceMappingURL=migrate-to-sqlite.js.map