{"version": 3, "names": ["traverseFast", "node", "enter", "opts", "keys", "VISITOR_KEYS", "type", "key", "subNode", "Array", "isArray"], "sources": ["../../src/traverse/traverseFast.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\n/**\n * A prefix AST traversal implementation meant for simple searching\n * and processing.\n */\nexport default function traverseFast<Options = {}>(\n  node: t.Node | null | undefined,\n  enter: (node: t.Node, opts?: Options) => void,\n  opts?: Options,\n): void {\n  if (!node) return;\n\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return;\n\n  opts = opts || ({} as Options);\n  enter(node, opts);\n\n  for (const key of keys) {\n    const subNode: t.Node | undefined | null =\n      // @ts-expect-error key must present in node\n      node[key];\n\n    if (Array.isArray(subNode)) {\n      for (const node of subNode) {\n        traverseFast(node, enter, opts);\n      }\n    } else {\n      traverseFast(subNode, enter, opts);\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AAOe,SAASA,YAAT,CACbC,IADa,EAEbC,KAFa,EAGbC,IAHa,EAIP;EACN,IAAI,CAACF,IAAL,EAAW;EAEX,MAAMG,IAAI,GAAGC,yBAAA,CAAaJ,IAAI,CAACK,IAAlB,CAAb;EACA,IAAI,CAACF,IAAL,EAAW;EAEXD,IAAI,GAAGA,IAAI,IAAK,EAAhB;EACAD,KAAK,CAACD,IAAD,EAAOE,IAAP,CAAL;;EAEA,KAAK,MAAMI,GAAX,IAAkBH,IAAlB,EAAwB;IACtB,MAAMI,OAAkC,GAEtCP,IAAI,CAACM,GAAD,CAFN;;IAIA,IAAIE,KAAK,CAACC,OAAN,CAAcF,OAAd,CAAJ,EAA4B;MAC1B,KAAK,MAAMP,IAAX,IAAmBO,OAAnB,EAA4B;QAC1BR,YAAY,CAACC,IAAD,EAAOC,KAAP,EAAcC,IAAd,CAAZ;MACD;IACF,CAJD,MAIO;MACLH,YAAY,CAACQ,OAAD,EAAUN,KAAV,EAAiBC,IAAjB,CAAZ;IACD;EACF;AACF"}