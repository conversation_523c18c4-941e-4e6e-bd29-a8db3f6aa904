"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const UserService_1 = require("../services/UserService");
class UserController {
    /**
     * 用户注册
     */
    static async register(req, res) {
        try {
            const { username, email, password, roles } = req.body;
            // 验证必填字段
            if (!username || !email || !password) {
                return res.status(400).json({
                    success: false,
                    message: '用户名、邮箱和密码为必填项'
                });
            }
            // 哈希密码（为将来的数据库操作准备）
            await UserService_1.UserService.hashPassword(password);
            // 这里需要调用数据库创建用户的逻辑
            // 由于Sequelize类型问题，先返回一个模拟响应
            const userResponse = {
                id: Date.now(),
                username,
                email,
                roles: roles || ['user'],
                createdAt: new Date(),
                updatedAt: new Date()
            };
            return res.status(201).json({
                success: true,
                message: '用户创建成功',
                user: userResponse
            });
        }
        catch (error) {
            return res.status(400).json({
                success: false,
                message: error.message || '用户创建失败'
            });
        }
    }
    /**
     * 用户登录
     */
    static async login(req, res) {
        try {
            const { username, password } = req.body;
            if (!username || !password) {
                return res.status(400).json({
                    success: false,
                    message: '用户名和密码为必填项'
                });
            }
            // 这里需要查询数据库验证用户
            // 由于Sequelize类型问题，先使用模拟数据
            const mockUser = {
                id: 1,
                username: 'admin',
                email: '<EMAIL>',
                passwordHash: await UserService_1.UserService.hashPassword('admin123'),
                roles: ['admin']
            };
            // 验证密码
            const isPasswordValid = await UserService_1.UserService.verifyPassword(password, mockUser.passwordHash);
            if (!isPasswordValid || username !== mockUser.username) {
                return res.status(401).json({
                    success: false,
                    message: '用户名或密码错误'
                });
            }
            // 生成JWT令牌
            const token = UserService_1.UserService.generateToken({
                id: mockUser.id,
                username: mockUser.username,
                roles: mockUser.roles
            });
            const response = UserService_1.UserService.createLoginResponse(true, {
                token,
                user: {
                    id: mockUser.id,
                    username: mockUser.username,
                    email: mockUser.email,
                    roles: mockUser.roles
                }
            });
            return res.json(response);
        }
        catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || '登录失败'
            });
        }
    }
    /**
     * 获取当前用户信息
     */
    static async getProfile(req, res) {
        try {
            const user = req.user;
            // 这里应该从数据库获取完整的用户信息
            return res.json({
                success: true,
                user: {
                    id: user.id,
                    username: user.username,
                    roles: user.roles
                }
            });
        }
        catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || '获取用户信息失败'
            });
        }
    }
    /**
     * 更新用户信息
     */
    static async updateProfile(req, res) {
        try {
            // 获取用户ID (为了避免未使用变量警告，我们将其注释掉但保留逻辑)
            // const userId = req.user.id;
            const updateData = req.body;
            // 如果要更新密码，先哈希
            if (updateData.password) {
                updateData.passwordHash = await UserService_1.UserService.hashPassword(updateData.password);
                delete updateData.password;
            }
            // 这里应该更新数据库中的用户信息
            return res.json({
                success: true,
                message: '用户信息更新成功'
            });
        }
        catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || '更新用户信息失败'
            });
        }
    }
}
exports.UserController = UserController;
//# sourceMappingURL=UserController.js.map