{"version": 3, "names": ["addComment", "node", "type", "content", "line", "addComments", "value"], "sources": ["../../src/comments/addComment.ts"], "sourcesContent": ["import addComments from \"./addComments\";\nimport type * as t from \"..\";\n\n/**\n * Add comment of certain type to a node.\n */\nexport default function addComment<T extends t.Node>(\n  node: T,\n  type: t.CommentTypeShorthand,\n  content: string,\n  line?: boolean,\n): T {\n  return addComments(node, type, [\n    {\n      type: line ? \"CommentLine\" : \"CommentBlock\",\n      value: content,\n    } as t.Comment,\n  ]);\n}\n"], "mappings": ";;;;;;;AAAA;;AAMe,SAASA,UAAT,CACbC,IADa,EAEbC,IAFa,EAGbC,OAHa,EAIbC,IAJa,EAKV;EACH,OAAO,IAAAC,oBAAA,EAAYJ,IAAZ,EAAkBC,IAAlB,EAAwB,CAC7B;IACEA,IAAI,EAAEE,IAAI,GAAG,aAAH,GAAmB,cAD/B;IAEEE,KAAK,EAAEH;EAFT,CAD6B,CAAxB,CAAP;AAMD"}