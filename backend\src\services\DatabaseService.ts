import sqlite3, { Database } from 'sqlite3';
import * as fs from 'fs';
import * as path from 'path';

export interface User {
  id: number;
  username: string;
  email: string;
  passwordHash: string;
  roles: string[];
  createdAt: string;
}

export interface Software {
  id: number;
  name: string;
  productKey: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  price: number;
  trialDays: number;
}

export interface License {
  id: number;
  licenseKey: string;
  softwareId: number;
  userId: number;
  activationType: string;
  activatedAt: string;
  expiresAt: string;
  machineId: string;
  isRevoked: boolean;
  isTrial: boolean;
  createdAt: string;
}

export interface Log {
  id: number;
  userId?: number;
  username: string;
  action: string;
  object?: string;
  result: string;
  details?: string;
  timestamp: string;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private db!: Database; // 使用断言操作符，因为在initialize方法中会初始化
  private readonly dbPath: string;
  private readonly schemaPath: string;

  private constructor() {
    this.dbPath = path.join(process.cwd(), 'data', 'license_management.db');
    this.schemaPath = path.join(__dirname, '../../src/database/schema.sql');
    
    // 确保data目录存在
    const dataDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('[DatabaseService] 数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('[DatabaseService] 数据库连接成功:', this.dbPath);
          this.initializeSchema()
            .then(() => resolve())
            .catch(reject);
        }
      });
    });
  }

  private async initializeSchema(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!fs.existsSync(this.schemaPath)) {
        reject(new Error(`Schema file not found: ${this.schemaPath}`));
        return;
      }

      const schema = fs.readFileSync(this.schemaPath, 'utf8');
      this.db.exec(schema, (err) => {
        if (err) {
          console.error('[DatabaseService] 数据库初始化失败:', err.message);
          reject(err);
        } else {
          console.log('[DatabaseService] 数据库表结构初始化成功');
          resolve();
        }
      });
    });
  }

  public async close(): Promise<void> {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('[DatabaseService] 关闭数据库失败:', err.message);
          } else {
            console.log('[DatabaseService] 数据库连接已关闭');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // 用户相关操作
  public async getUsers(): Promise<User[]> {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM users ORDER BY id', (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const users = rows.map(row => ({
            id: row.id,
            username: row.username,
            email: row.email,
            passwordHash: row.password_hash,
            roles: JSON.parse(row.roles),
            createdAt: row.created_at
          }));
          resolve(users);
        }
      });
    });
  }

  public async getUserById(id: number): Promise<User | null> {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT * FROM users WHERE id = ?', [id], (err, row: any) => {
        if (err) {
          reject(err);
        } else if (row) {
          resolve({
            id: row.id,
            username: row.username,
            email: row.email,
            passwordHash: row.password_hash,
            roles: JSON.parse(row.roles),
            createdAt: row.created_at
          });
        } else {
          resolve(null);
        }
      });
    });
  }

  public async getUserByUsername(username: string): Promise<User | null> {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT * FROM users WHERE username = ?', [username], (err, row: any) => {
        if (err) {
          reject(err);
        } else if (row) {
          resolve({
            id: row.id,
            username: row.username,
            email: row.email,
            passwordHash: row.password_hash,
            roles: JSON.parse(row.roles),
            createdAt: row.created_at
          });
        } else {
          resolve(null);
        }
      });
    });
  }

  public async createUser(user: Omit<User, 'id'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO users (username, email, password_hash, roles, created_at) 
        VALUES (?, ?, ?, ?, ?)
      `);
      
      stmt.run([
        user.username,
        user.email,
        user.passwordHash,
        JSON.stringify(user.roles),
        user.createdAt
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
      
      stmt.finalize();
    });
  }

  public async updateUser(id: number, updates: Partial<User>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const fields: string[] = [];
      const values: any[] = [];

      if (updates.username !== undefined) {
        fields.push('username = ?');
        values.push(updates.username);
      }
      if (updates.email !== undefined) {
        fields.push('email = ?');
        values.push(updates.email);
      }
      if (updates.passwordHash !== undefined) {
        fields.push('password_hash = ?');
        values.push(updates.passwordHash);
      }
      if (updates.roles !== undefined) {
        fields.push('roles = ?');
        values.push(JSON.stringify(updates.roles));
      }

      if (fields.length === 0) {
        resolve(false);
        return;
      }

      values.push(id);
      const sql = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;

      this.db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  public async deleteUser(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM users WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // 软件相关操作
  public async getSoftware(): Promise<Software[]> {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM software ORDER BY id', (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const software = rows.map(row => ({
            id: row.id,
            name: row.name,
            productKey: row.product_key,
            description: row.description,
            isActive: !!row.is_active,
            price: row.price,
            trialDays: row.trial_days,
            createdAt: row.created_at
          }));
          resolve(software);
        }
      });
    });
  }

  public async createSoftware(software: Omit<Software, 'id'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO software (name, product_key, description, is_active, price, trial_days, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run([
        software.name,
        software.productKey,
        software.description,
        software.isActive ? 1 : 0,
        software.price,
        software.trialDays,
        software.createdAt
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
      
      stmt.finalize();
    });
  }

  public async updateSoftware(id: number, updates: Partial<Software>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const fields: string[] = [];
      const values: any[] = [];

      if (updates.name !== undefined) {
        fields.push('name = ?');
        values.push(updates.name);
      }
      if (updates.productKey !== undefined) {
        fields.push('product_key = ?');
        values.push(updates.productKey);
      }
      if (updates.description !== undefined) {
        fields.push('description = ?');
        values.push(updates.description);
      }
      if (updates.isActive !== undefined) {
        fields.push('is_active = ?');
        values.push(updates.isActive ? 1 : 0);
      }
      if (updates.price !== undefined) {
        fields.push('price = ?');
        values.push(updates.price);
      }
      if (updates.trialDays !== undefined) {
        fields.push('trial_days = ?');
        values.push(updates.trialDays);
      }

      if (fields.length === 0) {
        resolve(false);
        return;
      }

      values.push(id);
      const sql = `UPDATE software SET ${fields.join(', ')} WHERE id = ?`;

      this.db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  public async deleteSoftware(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM software WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // 许可证相关操作
  public async getLicenses(): Promise<License[]> {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM licenses ORDER BY id', (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const licenses = rows.map(row => ({
            id: row.id,
            licenseKey: row.license_key,
            softwareId: row.software_id,
            userId: row.user_id,
            activationType: row.activation_type,
            activatedAt: row.activated_at,
            expiresAt: row.expires_at,
            machineId: row.machine_id,
            isRevoked: !!row.is_revoked,
            isTrial: !!row.is_trial,
            createdAt: row.created_at
          }));
          resolve(licenses);
        }
      });
    });
  }

  public async createLicense(license: Omit<License, 'id'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO licenses (license_key, software_id, user_id, activation_type, activated_at, expires_at, machine_id, is_revoked, is_trial, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run([
        license.licenseKey,
        license.softwareId,
        license.userId,
        license.activationType,
        license.activatedAt,
        license.expiresAt,
        license.machineId,
        license.isRevoked ? 1 : 0,
        license.isTrial ? 1 : 0,
        license.createdAt
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
      
      stmt.finalize();
    });
  }

  public async updateLicense(id: number, updates: Partial<License>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const fields: string[] = [];
      const values: any[] = [];

      if (updates.activatedAt !== undefined) {
        fields.push('activated_at = ?');
        values.push(updates.activatedAt);
      }
      if (updates.expiresAt !== undefined) {
        fields.push('expires_at = ?');
        values.push(updates.expiresAt);
      }
      if (updates.machineId !== undefined) {
        fields.push('machine_id = ?');
        values.push(updates.machineId);
      }
      if (updates.isRevoked !== undefined) {
        fields.push('is_revoked = ?');
        values.push(updates.isRevoked ? 1 : 0);
      }

      if (fields.length === 0) {
        resolve(false);
        return;
      }

      values.push(id);
      const sql = `UPDATE licenses SET ${fields.join(', ')} WHERE id = ?`;

      this.db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  public async deleteLicense(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM licenses WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // 日志相关操作
  public async createLog(log: Omit<Log, 'id' | 'timestamp'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO logs (user_id, username, action, object, result, details, timestamp) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      
      // 使用ISO 8601格式的时间戳，确保前端能正确解析
      const timestamp = new Date().toISOString();
      
      stmt.run([
        log.userId || null,
        log.username,
        log.action,
        log.object || null,
        log.result,
        log.details || null,
        timestamp
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
      
      stmt.finalize();
    });
  }

  public async getLogs(limit: number = 100): Promise<Log[]> {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM logs ORDER BY timestamp DESC LIMIT ?', [limit], (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const logs = rows.map(row => ({
            id: row.id,
            userId: row.user_id,
            username: row.username,
            action: row.action,
            object: row.object,
            result: row.result,
            details: row.details,
            timestamp: row.timestamp
          }));
          resolve(logs);
        }
      });
    });
  }
} 