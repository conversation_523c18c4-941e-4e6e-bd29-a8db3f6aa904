{"version": 3, "names": ["isReferenced", "node", "parent", "grandparent", "type", "property", "computed", "object", "init", "body", "key", "superClass", "right", "source", "local", "id"], "sources": ["../../src/validators/isReferenced.ts"], "sourcesContent": ["import type * as t from \"..\";\n\n/**\n * Check if the input `node` is a reference to a bound variable.\n */\nexport default function isReferenced(\n  node: t.Node,\n  parent: t.Node,\n  grandparent?: t.Node,\n): boolean {\n  switch (parent.type) {\n    // yes: PARENT[NODE]\n    // yes: NODE.child\n    // no: parent.NODE\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n      if (parent.property === node) {\n        return !!parent.computed;\n      }\n      return parent.object === node;\n\n    case \"JSXMemberExpression\":\n      return parent.object === node;\n    // no: let NODE = init;\n    // yes: let id = NODE;\n    case \"VariableDeclarator\":\n      return parent.init === node;\n\n    // yes: () => NODE\n    // no: (NODE) => {}\n    case \"ArrowFunctionExpression\":\n      return parent.body === node;\n\n    // no: class { #NODE; }\n    // no: class { get #NODE() {} }\n    // no: class { #NODE() {} }\n    // no: class { fn() { return this.#NODE; } }\n    case \"PrivateName\":\n      return false;\n\n    // no: class { NODE() {} }\n    // yes: class { [NODE]() {} }\n    // no: class { foo(NODE) {} }\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n    case \"ObjectMethod\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n      return false;\n\n    // yes: { [NODE]: \"\" }\n    // no: { NODE: \"\" }\n    // depends: { NODE }\n    // depends: { key: NODE }\n    case \"ObjectProperty\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n      // parent.value === node\n      return !grandparent || grandparent.type !== \"ObjectPattern\";\n    // no: class { NODE = value; }\n    // yes: class { [NODE] = value; }\n    // yes: class { key = NODE; }\n    case \"ClassProperty\":\n    case \"ClassAccessorProperty\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n      return true;\n    case \"ClassPrivateProperty\":\n      return parent.key !== node;\n\n    // no: class NODE {}\n    // yes: class Foo extends NODE {}\n    case \"ClassDeclaration\":\n    case \"ClassExpression\":\n      return parent.superClass === node;\n\n    // yes: left = NODE;\n    // no: NODE = right;\n    case \"AssignmentExpression\":\n      return parent.right === node;\n\n    // no: [NODE = foo] = [];\n    // yes: [foo = NODE] = [];\n    case \"AssignmentPattern\":\n      return parent.right === node;\n\n    // no: NODE: for (;;) {}\n    case \"LabeledStatement\":\n      return false;\n\n    // no: try {} catch (NODE) {}\n    case \"CatchClause\":\n      return false;\n\n    // no: function foo(...NODE) {}\n    case \"RestElement\":\n      return false;\n\n    case \"BreakStatement\":\n    case \"ContinueStatement\":\n      return false;\n\n    // no: function NODE() {}\n    // no: function foo(NODE) {}\n    case \"FunctionDeclaration\":\n    case \"FunctionExpression\":\n      return false;\n\n    // no: export NODE from \"foo\";\n    // no: export * as NODE from \"foo\";\n    case \"ExportNamespaceSpecifier\":\n    case \"ExportDefaultSpecifier\":\n      return false;\n\n    // no: export { foo as NODE };\n    // yes: export { NODE as foo };\n    // no: export { NODE as foo } from \"foo\";\n    case \"ExportSpecifier\":\n      // @ts-expect-error todo(flow->ts): Property 'source' does not exist on type 'AnyTypeAnnotation'.\n      if (grandparent?.source) {\n        return false;\n      }\n      return parent.local === node;\n\n    // no: import NODE from \"foo\";\n    // no: import * as NODE from \"foo\";\n    // no: import { NODE as foo } from \"foo\";\n    // no: import { foo as NODE } from \"foo\";\n    // no: import NODE from \"bar\";\n    case \"ImportDefaultSpecifier\":\n    case \"ImportNamespaceSpecifier\":\n    case \"ImportSpecifier\":\n      return false;\n\n    // no: import \"foo\" assert { NODE: \"json\" }\n    case \"ImportAttribute\":\n      return false;\n\n    // no: <div NODE=\"foo\" />\n    case \"JSXAttribute\":\n      return false;\n\n    // no: [NODE] = [];\n    // no: ({ NODE }) = [];\n    case \"ObjectPattern\":\n    case \"ArrayPattern\":\n      return false;\n\n    // no: new.NODE\n    // no: NODE.target\n    case \"MetaProperty\":\n      return false;\n\n    // yes: type X = { somePropert: NODE }\n    // no: type X = { NODE: OtherType }\n    case \"ObjectTypeProperty\":\n      return parent.key !== node;\n\n    // yes: enum X { Foo = NODE }\n    // no: enum X { NODE }\n    case \"TSEnumMember\":\n      return parent.id !== node;\n\n    // yes: { [NODE]: value }\n    // no: { NODE: value }\n    case \"TSPropertySignature\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n\n      return true;\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;;AAKe,SAASA,YAAT,CACbC,IADa,EAEbC,MAFa,EAGbC,WAHa,EAIJ;EACT,QAAQD,MAAM,CAACE,IAAf;IAIE,KAAK,kBAAL;IACA,KAAK,0BAAL;MACE,IAAIF,MAAM,CAACG,QAAP,KAAoBJ,IAAxB,EAA8B;QAC5B,OAAO,CAAC,CAACC,MAAM,CAACI,QAAhB;MACD;;MACD,OAAOJ,MAAM,CAACK,MAAP,KAAkBN,IAAzB;;IAEF,KAAK,qBAAL;MACE,OAAOC,MAAM,CAACK,MAAP,KAAkBN,IAAzB;;IAGF,KAAK,oBAAL;MACE,OAAOC,MAAM,CAACM,IAAP,KAAgBP,IAAvB;;IAIF,KAAK,yBAAL;MACE,OAAOC,MAAM,CAACO,IAAP,KAAgBR,IAAvB;;IAMF,KAAK,aAAL;MACE,OAAO,KAAP;;IAKF,KAAK,aAAL;IACA,KAAK,oBAAL;IACA,KAAK,cAAL;MACE,IAAIC,MAAM,CAACQ,GAAP,KAAeT,IAAnB,EAAyB;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAhB;MACD;;MACD,OAAO,KAAP;;IAMF,KAAK,gBAAL;MACE,IAAIJ,MAAM,CAACQ,GAAP,KAAeT,IAAnB,EAAyB;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAhB;MACD;;MAED,OAAO,CAACH,WAAD,IAAgBA,WAAW,CAACC,IAAZ,KAAqB,eAA5C;;IAIF,KAAK,eAAL;IACA,KAAK,uBAAL;MACE,IAAIF,MAAM,CAACQ,GAAP,KAAeT,IAAnB,EAAyB;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAhB;MACD;;MACD,OAAO,IAAP;;IACF,KAAK,sBAAL;MACE,OAAOJ,MAAM,CAACQ,GAAP,KAAeT,IAAtB;;IAIF,KAAK,kBAAL;IACA,KAAK,iBAAL;MACE,OAAOC,MAAM,CAACS,UAAP,KAAsBV,IAA7B;;IAIF,KAAK,sBAAL;MACE,OAAOC,MAAM,CAACU,KAAP,KAAiBX,IAAxB;;IAIF,KAAK,mBAAL;MACE,OAAOC,MAAM,CAACU,KAAP,KAAiBX,IAAxB;;IAGF,KAAK,kBAAL;MACE,OAAO,KAAP;;IAGF,KAAK,aAAL;MACE,OAAO,KAAP;;IAGF,KAAK,aAAL;MACE,OAAO,KAAP;;IAEF,KAAK,gBAAL;IACA,KAAK,mBAAL;MACE,OAAO,KAAP;;IAIF,KAAK,qBAAL;IACA,KAAK,oBAAL;MACE,OAAO,KAAP;;IAIF,KAAK,0BAAL;IACA,KAAK,wBAAL;MACE,OAAO,KAAP;;IAKF,KAAK,iBAAL;MAEE,IAAIE,WAAJ,YAAIA,WAAW,CAAEU,MAAjB,EAAyB;QACvB,OAAO,KAAP;MACD;;MACD,OAAOX,MAAM,CAACY,KAAP,KAAiBb,IAAxB;;IAOF,KAAK,wBAAL;IACA,KAAK,0BAAL;IACA,KAAK,iBAAL;MACE,OAAO,KAAP;;IAGF,KAAK,iBAAL;MACE,OAAO,KAAP;;IAGF,KAAK,cAAL;MACE,OAAO,KAAP;;IAIF,KAAK,eAAL;IACA,KAAK,cAAL;MACE,OAAO,KAAP;;IAIF,KAAK,cAAL;MACE,OAAO,KAAP;;IAIF,KAAK,oBAAL;MACE,OAAOC,MAAM,CAACQ,GAAP,KAAeT,IAAtB;;IAIF,KAAK,cAAL;MACE,OAAOC,MAAM,CAACa,EAAP,KAAcd,IAArB;;IAIF,KAAK,qBAAL;MACE,IAAIC,MAAM,CAACQ,GAAP,KAAeT,IAAnB,EAAyB;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAhB;MACD;;MAED,OAAO,IAAP;EAnKJ;;EAsKA,OAAO,IAAP;AACD"}