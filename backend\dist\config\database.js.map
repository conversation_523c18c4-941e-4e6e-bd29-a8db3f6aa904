{"version": 3, "file": "database.js", "sourceRoot": "", "sources": ["../../src/config/database.ts"], "names": [], "mappings": ";;;;;;AAAA,+DAAiD;AACjD,gDAAwB;AACxB,yCAAsC;AACtC,iDAA8C;AAC9C,+CAA4C;AAE5C,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,uBAAuB,CAAC,CAAC;AAEvF,QAAA,SAAS,GAAG,IAAI,gCAAS,CAAC;IACrC,OAAO,EAAE,QAAQ;IACjB,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,CAAC,WAAI,EAAE,mBAAQ,EAAE,iBAAO,CAAC;IACjC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;IACrE,MAAM,EAAE;QACN,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,KAAK;KACnB;CACF,CAAC,CAAC;AAEI,MAAM,kBAAkB,GAAG,KAAK,IAAmB,EAAE;IAC1D,IAAI,CAAC;QACH,MAAM,iBAAS,CAAC,YAAY,EAAE,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEvB,WAAW;QACX,MAAM,iBAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,kBAAkB,sBAY7B"}