export interface User {
    id: number;
    username: string;
    email: string;
    passwordHash: string;
    roles: string[];
    createdAt: string;
}
export interface Software {
    id: number;
    name: string;
    productKey: string;
    description: string;
    isActive: boolean;
    createdAt: string;
    price: number;
    trialDays: number;
}
export interface License {
    id: number;
    licenseKey: string;
    softwareId: number;
    userId: number;
    activationType: string;
    activatedAt: string;
    expiresAt: string;
    machineId: string;
    isRevoked: boolean;
    isTrial: boolean;
    createdAt: string;
}
export interface Log {
    id: number;
    userId?: number;
    username: string;
    action: string;
    object?: string;
    result: string;
    details?: string;
    timestamp: string;
}
export declare class DatabaseService {
    private static instance;
    private db;
    private readonly dbPath;
    private readonly schemaPath;
    private constructor();
    static getInstance(): DatabaseService;
    initialize(): Promise<void>;
    private initializeSchema;
    close(): Promise<void>;
    getUsers(): Promise<User[]>;
    getUserById(id: number): Promise<User | null>;
    getUserByUsername(username: string): Promise<User | null>;
    createUser(user: Omit<User, 'id'>): Promise<number>;
    updateUser(id: number, updates: Partial<User>): Promise<boolean>;
    deleteUser(id: number): Promise<boolean>;
    getSoftware(): Promise<Software[]>;
    createSoftware(software: Omit<Software, 'id'>): Promise<number>;
    updateSoftware(id: number, updates: Partial<Software>): Promise<boolean>;
    deleteSoftware(id: number): Promise<boolean>;
    getLicenses(): Promise<License[]>;
    createLicense(license: Omit<License, 'id'>): Promise<number>;
    updateLicense(id: number, updates: Partial<License>): Promise<boolean>;
    deleteLicense(id: number): Promise<boolean>;
    createLog(log: Omit<Log, 'id' | 'timestamp'>): Promise<number>;
    getLogs(limit?: number): Promise<Log[]>;
}
//# sourceMappingURL=DatabaseService.d.ts.map