import { Router } from 'express';
import { LicenseController } from '../controllers/LicenseController';
import { authenticateToken, requireManagerOrAdmin } from '../middleware/auth';

const router = Router();

// 公开路由 - 激活许可证（不需要认证）
router.post('/activate', LicenseController.activate);

// 公开路由 - 验证许可证（不需要认证）
router.post('/validate/:licenseKey', LicenseController.validate);

// 需要认证的路由
// 获取许可证信息
router.get('/:licenseKey', authenticateToken, LicenseController.getLicense);

// 管理员路由 - 撤销许可证（需要管理员或经理权限）
router.delete('/:licenseKey/revoke', authenticateToken, requireManagerOrAdmin, LicenseController.revoke);

export default router; 