{"version": 3, "file": "LicenseController.js", "sourceRoot": "", "sources": ["../../src/controllers/LicenseController.ts"], "names": [], "mappings": ";;;AACA,+DAA4D;AAE5D,MAAa,iBAAiB;IAC5B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAY,EAAE,GAAa;QAC/C,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAE3D,SAAS;YACT,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,EAAE,CAAC;gBACjD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,oBAAoB;iBAC9B,CAAC,CAAC;YACL,CAAC;YAED,SAAS;YACT,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,SAAS;iBACnB,CAAC,CAAC;YACL,CAAC;YAED,eAAe;YACf,kBAAkB;YAClB,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,qBAAqB;gBACjC,QAAQ,EAAE,IAAI;aACf,CAAC;YAEF,SAAS;YACT,IAAI,UAAU,KAAK,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CAChE,CAAC;YACJ,CAAC;YAED,WAAW;YACX,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC3B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAC/D,CAAC;YACJ,CAAC;YAED,YAAY;YACZ,MAAM,UAAU,GAAG,+BAAc,CAAC,kBAAkB,EAAE,CAAC;YACvD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ;YAE5D,YAAY;YACZ,MAAM,WAAW,GAAG;gBAClB,UAAU;gBACV,cAAc;gBACd,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,SAAS;gBACT,SAAS;gBACT,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,YAAY,CAAC,EAAE;aAC5B,CAAC;YAEF,WAAW;YACX,IAAI,CAAC,+BAAc,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;gBACpD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CAChE,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,+BAAc,CAAC,wBAAwB,CAAC,IAAI,EAAE;gBAC7D,UAAU;gBACV,cAAc;gBACd,SAAS;aACV,EAAE,SAAS,CAAC,CAAC;YAEd,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,CAC9E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAY,EAAE,GAAa;QAC/C,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAClC,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAE/B,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CACzE,CAAC;YACJ,CAAC;YAED,YAAY;YACZ,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,CAAC;gBACL,UAAU,EAAE,UAAU;gBACtB,cAAc,EAAE,QAAQ;gBACxB,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACnC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,SAAS,IAAI,oBAAoB;gBAC5C,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,CAAC;aACd,CAAC;YAEF,YAAY;YACZ,IAAI,WAAW,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CACtE,CAAC;YACJ,CAAC;YAED,SAAS;YACT,IAAI,SAAS,IAAI,CAAC,+BAAc,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC3E,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CACvE,CAAC;YACJ,CAAC;YAED,YAAY;YACZ,MAAM,OAAO,GAAG,+BAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE5D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,MAAM,GAAG,+BAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAC5D,IAAI,OAAO,GAAG,OAAO,CAAC;gBAEtB,QAAQ,MAAM,EAAE,CAAC;oBACf,KAAK,SAAS;wBACZ,OAAO,GAAG,QAAQ,CAAC;wBACnB,MAAM;oBACR,KAAK,SAAS;wBACZ,OAAO,GAAG,SAAS,CAAC;wBACpB,MAAM;gBACV,CAAC;gBAED,OAAO,GAAG,CAAC,IAAI,CACb,+BAAc,CAAC,wBAAwB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CACpE,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,+BAAc,CAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,EAAE;gBACnE,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,EAAE,SAAS,CAAC,CAAC;YAEd,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACzB,+BAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,CACrF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,GAAY,EAAE,GAAa;QACjD,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAElC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,WAAW;iBACrB,CAAC,CAAC;YACL,CAAC;YAED,UAAU;YACV,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,CAAC;gBACL,UAAU;gBACV,cAAc,EAAE,QAAQ;gBACxB,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACnC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,oBAAoB;gBAC/B,SAAS,EAAE,KAAK;gBAChB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,qBAAqB;iBAClC;aACF,CAAC;YAEF,OAAO,GAAG,CAAC,IAAI,CAAC;gBACd,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,WAAW;aACrB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,WAAW;aACtC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAY,EAAE,GAAa;QAC7C,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAElC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,WAAW;iBACrB,CAAC,CAAC;YACL,CAAC;YAED,mBAAmB;YACnB,OAAO,GAAG,CAAC,IAAI,CAAC;gBACd,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,QAAQ;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,SAAS;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AAjOD,8CAiOC"}