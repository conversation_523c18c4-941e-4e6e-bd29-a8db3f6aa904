# License Management System - 可执行文件版本

## 文件说明

- `license-management.exe` - 主程序可执行文件
- `start-server.bat` - 启动服务器的批处理文件
- `public/` - 静态文件目录（HTML页面等）
- `data/` - 数据库文件目录

## 使用方法

### 方法1：直接运行
双击 `license-management.exe` 文件启动服务器

### 方法2：使用批处理文件
双击 `start-server.bat` 文件启动服务器（推荐）

### 方法3：命令行运行
```bash
.\license-management.exe
```

## 访问应用

服务器启动后，在浏览器中访问：
- 主页：http://localhost:3000
- 管理员页面：http://localhost:3000/admin.html
- 用户页面：http://localhost:3000/user.html

## 注意事项

1. **端口占用**：默认使用3000端口，如果端口被占用，请先关闭占用该端口的程序
2. **防火墙**：首次运行时Windows可能会询问防火墙权限，请允许访问
3. **数据库**：程序会自动创建SQLite数据库文件在 `data/` 目录下
4. **静态文件**：确保 `public/` 目录与exe文件在同一目录下

## 部署到其他机器

将以下文件和目录复制到目标机器：
- `license-management.exe`
- `start-server.bat`
- `public/` 目录
- `data/` 目录（如果需要保留数据）

## 停止服务器

在命令行窗口中按 `Ctrl+C` 停止服务器

## 故障排除

1. **无法启动**：检查是否有其他程序占用3000端口
2. **页面无法访问**：确认服务器已正常启动，检查防火墙设置
3. **数据库错误**：确保 `data/` 目录存在且有写入权限

## 技术信息

- 基于 Node.js 18
- 使用 pkg 工具打包
- 包含所有依赖项，无需安装 Node.js
- 支持 Windows x64 系统
