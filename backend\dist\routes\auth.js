"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const UserController_1 = require("../controllers/UserController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// 用户注册
router.post('/register', UserController_1.UserController.register);
// 用户登录
router.post('/login', UserController_1.UserController.login);
// 获取当前用户信息（需要认证）
router.get('/profile', auth_1.authenticateToken, UserController_1.UserController.getProfile);
// 更新用户信息（需要认证）
router.put('/profile', auth_1.authenticateToken, UserController_1.UserController.updateProfile);
exports.default = router;
//# sourceMappingURL=auth.js.map