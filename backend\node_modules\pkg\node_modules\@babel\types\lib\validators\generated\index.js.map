{"version": 3, "names": ["isArrayExpression", "node", "opts", "nodeType", "type", "shallowEqual", "isAssignmentExpression", "isBinaryExpression", "isInterpreterDirective", "isDirective", "isDirectiveLiteral", "isBlockStatement", "isBreakStatement", "isCallExpression", "isCatchClause", "isConditionalExpression", "isContinueStatement", "isDebuggerStatement", "isDoWhileStatement", "isEmptyStatement", "isExpressionStatement", "isFile", "isForInStatement", "isForStatement", "isFunctionDeclaration", "isFunctionExpression", "isIdentifier", "isIfStatement", "isLabeledStatement", "isStringLiteral", "isNumericLiteral", "is<PERSON>ull<PERSON>iteral", "isBooleanLiteral", "isRegExpLiteral", "isLogicalExpression", "isMemberExpression", "isNewExpression", "isProgram", "isObjectExpression", "isObjectMethod", "isObjectProperty", "isRestElement", "isReturnStatement", "isSequenceExpression", "isParenthesizedExpression", "isSwitchCase", "isSwitchStatement", "isThisExpression", "isThrowStatement", "isTryStatement", "isUnaryExpression", "isUpdateExpression", "isVariableDeclaration", "isVariableDeclarator", "isWhileStatement", "isWithStatement", "isAssignmentPattern", "isArrayPattern", "isArrowFunctionExpression", "isClassBody", "isClassExpression", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isExportSpecifier", "isForOfStatement", "isImportDeclaration", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isImportSpecifier", "isMetaProperty", "isClassMethod", "isObjectPattern", "isSpreadElement", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateElement", "isTemplateLiteral", "isYieldExpression", "isAwaitExpression", "isImport", "isBigIntLiteral", "isExportNamespaceSpecifier", "isOptionalMemberExpression", "isOptionalCallExpression", "isClassProperty", "isClassAccessorProperty", "isClassPrivateProperty", "isClassPrivateMethod", "isPrivateName", "isStaticBlock", "isAnyTypeAnnotation", "isArrayTypeAnnotation", "isBooleanTypeAnnotation", "isBooleanLiteralTypeAnnotation", "isNullLiteralTypeAnnotation", "isClassImplements", "isDeclareClass", "isDeclareFunction", "isDeclareInterface", "isDeclareModule", "isDeclareModuleExports", "isDeclareTypeAlias", "isDeclareOpaqueType", "isDeclareVariable", "isDeclareExportDeclaration", "isDeclareExportAllDeclaration", "isDeclaredPredicate", "isExistsTypeAnnotation", "isFunctionTypeAnnotation", "isFunctionTypeParam", "isGenericTypeAnnotation", "isInferredPredicate", "isInterfaceExtends", "isInterfaceDeclaration", "isInterfaceTypeAnnotation", "isIntersectionTypeAnnotation", "isMixedTypeAnnotation", "isEmptyTypeAnnotation", "isNullableTypeAnnotation", "isNumberLiteralTypeAnnotation", "isNumberTypeAnnotation", "isObjectTypeAnnotation", "isObjectTypeInternalSlot", "isObjectTypeCallProperty", "isObjectTypeIndexer", "isObjectTypeProperty", "isObjectTypeSpreadProperty", "isOpaqueType", "isQualifiedTypeIdentifier", "isStringLiteralTypeAnnotation", "isStringTypeAnnotation", "isSymbolTypeAnnotation", "isThisTypeAnnotation", "isTupleTypeAnnotation", "isTypeofTypeAnnotation", "isTypeAlias", "isTypeAnnotation", "isTypeCastExpression", "isTypeParameter", "isTypeParameterDeclaration", "isTypeParameterInstantiation", "isUnionTypeAnnotation", "is<PERSON><PERSON>ce", "isVoidTypeAnnotation", "isEnumDeclaration", "isEnumBooleanBody", "isEnumNumberBody", "isEnumStringBody", "isEnumSymbolBody", "isEnumBooleanMember", "isEnumNumberMember", "isEnumStringMember", "isEnumDefaultedMember", "isIndexedAccessType", "isOptionalIndexedAccessType", "isJSXAttribute", "isJSXClosingElement", "isJSXElement", "isJSXEmptyExpression", "isJSXExpressionContainer", "isJSXSpreadChild", "isJSXIdentifier", "isJSXMemberExpression", "isJSXNamespacedName", "isJSXOpeningElement", "isJSXSpreadAttribute", "isJSXText", "isJSXFragment", "isJSXOpeningFragment", "isJSXClosingFragment", "isNoop", "isPlaceholder", "isV8IntrinsicIdentifier", "isArgumentPlaceholder", "isBindExpression", "isImportAttribute", "isDecorator", "isDoExpression", "isExportDefaultSpecifier", "isRecordExpression", "isTupleExpression", "isDecimalLiteral", "isModuleExpression", "isTopicReference", "isPipelineTopicExpression", "isPipelineBareFunction", "isPipelinePrimaryTopicReference", "isTSParameterProperty", "isTSDeclareFunction", "isTSDeclareMethod", "isTSQualifiedName", "isTSCallSignatureDeclaration", "isTSConstructSignatureDeclaration", "isTSPropertySignature", "isTSMethodSignature", "isTSIndexSignature", "isTSAnyKeyword", "isTSBooleanKeyword", "isTSBigIntKeyword", "isTSIntrinsicKeyword", "isTSNeverKeyword", "isTSNullKeyword", "isTSNumberKeyword", "isTSObjectKeyword", "isTSStringKeyword", "isTSSymbolKeyword", "isTSUndefinedKeyword", "isTSUnknownKeyword", "isTSVoidKeyword", "isTSThisType", "isTSFunctionType", "isTSConstructorType", "isTSTypeReference", "isTSTypePredicate", "isTSTypeQuery", "isTSTypeLiteral", "isTSArrayType", "isTSTupleType", "isTSOptionalType", "isTSRestType", "isTSNamedTupleMember", "isTSUnionType", "isTSIntersectionType", "isTSConditionalType", "isTSInferType", "isTSParenthesizedType", "isTSTypeOperator", "isTSIndexedAccessType", "isTSMappedType", "isTSLiteralType", "isTSExpressionWithTypeArguments", "isTSInterfaceDeclaration", "isTSInterfaceBody", "isTSTypeAliasDeclaration", "isTSInstantiationExpression", "isTSAsExpression", "isTSTypeAssertion", "isTSEnumDeclaration", "isTSEnumMember", "isTSModuleDeclaration", "isTSModuleBlock", "isTSImportType", "isTSImportEqualsDeclaration", "isTSExternalModuleReference", "isTSNonNullExpression", "isTSExportAssignment", "isTSNamespaceExportDeclaration", "isTSTypeAnnotation", "isTSTypeParameterInstantiation", "isTSTypeParameterDeclaration", "isTSTypeParameter", "isStandardized", "expectedNode", "isExpression", "isBinary", "isScopable", "isBlockParent", "isBlock", "isStatement", "isTerminatorless", "isCompletionStatement", "isConditional", "isLoop", "<PERSON><PERSON><PERSON><PERSON>", "isExpressionWrapper", "isFor", "isForXStatement", "isFunction", "isFunctionParent", "isPureish", "isDeclaration", "isPatternLike", "isLVal", "isTSEntityName", "isLiteral", "isImmutable", "isUserWhitespacable", "isMethod", "isObjectMember", "isProperty", "isUnaryLike", "isPattern", "isClass", "isModuleDeclaration", "isExportDeclaration", "isModuleSpecifier", "isAccessor", "isPrivate", "isFlow", "isFlowType", "isFlowBaseAnnotation", "isFlowDeclaration", "isFlowPredicate", "isEnumBody", "isEnumMember", "isJSX", "isMiscellaneous", "isTypeScript", "isTSTypeElement", "isTSType", "isTSBaseType", "isNumberLiteral", "console", "trace", "isRegexLiteral", "isRestProperty", "isSpreadProperty"], "sources": ["../../../src/validators/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport shallowEqual from \"../../utils/shallowEqual\";\nimport type * as t from \"../..\";\n\nexport function isArrayExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrayExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrayExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAssignmentExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AssignmentExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AssignmentExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBinaryExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BinaryExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BinaryExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterpreterDirective(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterpreterDirective {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterpreterDirective\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDirective(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Directive {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Directive\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDirectiveLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DirectiveLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DirectiveLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBlockStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BlockStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BlockStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBreakStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BreakStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BreakStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isCallExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.CallExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"CallExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isCatchClause(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.CatchClause {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"CatchClause\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isConditionalExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ConditionalExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ConditionalExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isContinueStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ContinueStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ContinueStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDebuggerStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DebuggerStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DebuggerStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDoWhileStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DoWhileStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DoWhileStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEmptyStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EmptyStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EmptyStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExpressionStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExpressionStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExpressionStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFile(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.File {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"File\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForInStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForInStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ForInStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ForStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Identifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Identifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIfStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.IfStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"IfStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLabeledStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.LabeledStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"LabeledStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStringLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StringLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StringLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumericLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NumericLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumericLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNullLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NullLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NullLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBooleanLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BooleanLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BooleanLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRegExpLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.RegExpLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RegExpLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLogicalExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.LogicalExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"LogicalExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMemberExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.MemberExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"MemberExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNewExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NewExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NewExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isProgram(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Program {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Program\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRestElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.RestElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RestElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isReturnStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ReturnStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ReturnStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSequenceExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SequenceExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SequenceExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isParenthesizedExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ParenthesizedExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ParenthesizedExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSwitchCase(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SwitchCase {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SwitchCase\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSwitchStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SwitchStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SwitchStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isThisExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ThisExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ThisExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isThrowStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ThrowStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ThrowStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTryStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TryStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TryStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUnaryExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UnaryExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"UnaryExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUpdateExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UpdateExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"UpdateExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVariableDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.VariableDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"VariableDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVariableDeclarator(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.VariableDeclarator {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"VariableDeclarator\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isWhileStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.WhileStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"WhileStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isWithStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.WithStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"WithStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAssignmentPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AssignmentPattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AssignmentPattern\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArrayPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrayPattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrayPattern\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArrowFunctionExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrowFunctionExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrowFunctionExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportAllDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportAllDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportAllDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportDefaultDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportDefaultDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportDefaultDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportNamedDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportNamedDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportNamedDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForOfStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForOfStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ForOfStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportDefaultSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportDefaultSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportDefaultSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportNamespaceSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportNamespaceSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportNamespaceSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMetaProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.MetaProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"MetaProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectPattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectPattern\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSpreadElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SpreadElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SpreadElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSuper(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Super {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Super\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTaggedTemplateExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TaggedTemplateExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TaggedTemplateExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTemplateElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TemplateElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TemplateElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTemplateLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TemplateLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TemplateLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isYieldExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.YieldExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"YieldExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAwaitExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AwaitExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AwaitExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImport(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Import {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Import\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBigIntLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BigIntLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BigIntLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportNamespaceSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportNamespaceSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportNamespaceSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOptionalMemberExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OptionalMemberExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OptionalMemberExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOptionalCallExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OptionalCallExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OptionalCallExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassAccessorProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassAccessorProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassAccessorProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassPrivateProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassPrivateProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassPrivateProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassPrivateMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassPrivateMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassPrivateMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPrivateName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PrivateName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PrivateName\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStaticBlock(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StaticBlock {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StaticBlock\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAnyTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AnyTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AnyTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArrayTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrayTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrayTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBooleanTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BooleanTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BooleanTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBooleanLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BooleanLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BooleanLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNullLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NullLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NullLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassImplements(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassImplements {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassImplements\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareClass(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareClass {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareClass\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareFunction {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareFunction\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareInterface(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareInterface {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareInterface\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareModule(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareModule {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareModule\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareModuleExports(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareModuleExports {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareModuleExports\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareTypeAlias(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareTypeAlias {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareTypeAlias\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareOpaqueType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareOpaqueType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareOpaqueType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareVariable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareVariable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareVariable\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareExportDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareExportAllDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareExportAllDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareExportAllDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclaredPredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclaredPredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclaredPredicate\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExistsTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExistsTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExistsTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionTypeParam(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionTypeParam {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionTypeParam\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isGenericTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.GenericTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"GenericTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInferredPredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InferredPredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InferredPredicate\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterfaceExtends(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterfaceExtends {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterfaceExtends\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterfaceDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterfaceDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterfaceDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterfaceTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterfaceTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterfaceTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIntersectionTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.IntersectionTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"IntersectionTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMixedTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.MixedTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"MixedTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEmptyTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EmptyTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EmptyTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNullableTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NullableTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NullableTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumberLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NumberLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumberLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumberTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NumberTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumberTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeInternalSlot(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeInternalSlot {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeInternalSlot\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeCallProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeCallProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeCallProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeIndexer(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeIndexer {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeIndexer\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeSpreadProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeSpreadProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeSpreadProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOpaqueType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OpaqueType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OpaqueType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isQualifiedTypeIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.QualifiedTypeIdentifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"QualifiedTypeIdentifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStringLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StringLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StringLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStringTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StringTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StringTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSymbolTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SymbolTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SymbolTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isThisTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ThisTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ThisTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTupleTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TupleTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TupleTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeofTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeofTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeofTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeAlias(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeAlias {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeAlias\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeCastExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeCastExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeCastExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeParameter(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeParameter {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeParameter\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeParameterDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeParameterDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeParameterDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeParameterInstantiation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeParameterInstantiation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeParameterInstantiation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUnionTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UnionTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"UnionTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVariance(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Variance {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Variance\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVoidTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.VoidTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"VoidTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumBooleanBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumBooleanBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumBooleanBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumNumberBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumNumberBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumNumberBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumStringBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumStringBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumStringBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumSymbolBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumSymbolBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumSymbolBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumBooleanMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumBooleanMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumBooleanMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumNumberMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumNumberMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumNumberMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumStringMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumStringMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumStringMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumDefaultedMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumDefaultedMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumDefaultedMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIndexedAccessType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.IndexedAccessType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"IndexedAccessType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOptionalIndexedAccessType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OptionalIndexedAccessType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OptionalIndexedAccessType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXAttribute(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXAttribute {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXAttribute\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXClosingElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXClosingElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXClosingElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXEmptyExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXEmptyExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXEmptyExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXExpressionContainer(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXExpressionContainer {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXExpressionContainer\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXSpreadChild(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXSpreadChild {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXSpreadChild\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXIdentifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXIdentifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXMemberExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXMemberExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXMemberExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXNamespacedName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXNamespacedName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXNamespacedName\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXOpeningElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXOpeningElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXOpeningElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXSpreadAttribute(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXSpreadAttribute {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXSpreadAttribute\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXText(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXText {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXText\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXFragment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXFragment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXFragment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXOpeningFragment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXOpeningFragment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXOpeningFragment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXClosingFragment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXClosingFragment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXClosingFragment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNoop(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Noop {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Noop\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPlaceholder(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Placeholder {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Placeholder\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isV8IntrinsicIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.V8IntrinsicIdentifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"V8IntrinsicIdentifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArgumentPlaceholder(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArgumentPlaceholder {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArgumentPlaceholder\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBindExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BindExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BindExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportAttribute(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportAttribute {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportAttribute\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDecorator(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Decorator {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Decorator\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDoExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DoExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DoExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportDefaultSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportDefaultSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportDefaultSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRecordExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.RecordExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RecordExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTupleExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TupleExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TupleExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDecimalLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DecimalLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DecimalLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isModuleExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ModuleExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ModuleExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTopicReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TopicReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TopicReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPipelineTopicExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PipelineTopicExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PipelineTopicExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPipelineBareFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PipelineBareFunction {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PipelineBareFunction\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPipelinePrimaryTopicReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PipelinePrimaryTopicReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PipelinePrimaryTopicReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSParameterProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSParameterProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSParameterProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSDeclareFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSDeclareFunction {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSDeclareFunction\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSDeclareMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSDeclareMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSDeclareMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSQualifiedName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSQualifiedName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSQualifiedName\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSCallSignatureDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSCallSignatureDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSCallSignatureDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSConstructSignatureDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSConstructSignatureDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSConstructSignatureDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSPropertySignature(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSPropertySignature {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSPropertySignature\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSMethodSignature(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSMethodSignature {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSMethodSignature\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIndexSignature(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIndexSignature {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIndexSignature\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSAnyKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSAnyKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSAnyKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSBooleanKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSBooleanKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSBooleanKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSBigIntKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSBigIntKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSBigIntKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIntrinsicKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIntrinsicKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIntrinsicKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNeverKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNeverKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNeverKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNullKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNullKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNullKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNumberKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNumberKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNumberKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSObjectKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSObjectKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSObjectKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSStringKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSStringKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSStringKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSSymbolKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSSymbolKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSSymbolKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSUndefinedKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSUndefinedKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSUndefinedKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSUnknownKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSUnknownKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSUnknownKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSVoidKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSVoidKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSVoidKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSThisType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSThisType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSThisType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSFunctionType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSFunctionType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSFunctionType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSConstructorType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSConstructorType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSConstructorType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypePredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypePredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypePredicate\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeQuery(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeQuery {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeQuery\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSArrayType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSArrayType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSArrayType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTupleType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTupleType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTupleType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSOptionalType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSOptionalType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSOptionalType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSRestType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSRestType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSRestType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNamedTupleMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNamedTupleMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNamedTupleMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSUnionType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSUnionType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSUnionType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIntersectionType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIntersectionType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIntersectionType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSConditionalType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSConditionalType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSConditionalType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInferType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInferType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInferType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSParenthesizedType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSParenthesizedType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSParenthesizedType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeOperator(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeOperator {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeOperator\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIndexedAccessType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIndexedAccessType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIndexedAccessType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSMappedType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSMappedType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSMappedType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSLiteralType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSLiteralType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSLiteralType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSExpressionWithTypeArguments(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSExpressionWithTypeArguments {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSExpressionWithTypeArguments\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInterfaceDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInterfaceDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInterfaceDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInterfaceBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInterfaceBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInterfaceBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeAliasDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeAliasDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeAliasDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInstantiationExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInstantiationExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInstantiationExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSAsExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSAsExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSAsExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeAssertion(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeAssertion {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeAssertion\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSEnumDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSEnumDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSEnumDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSEnumMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSEnumMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSEnumMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSModuleDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSModuleDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSModuleDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSModuleBlock(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSModuleBlock {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSModuleBlock\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSImportType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSImportType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSImportType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSImportEqualsDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSImportEqualsDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSImportEqualsDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSExternalModuleReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSExternalModuleReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSExternalModuleReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNonNullExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNonNullExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNonNullExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSExportAssignment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSExportAssignment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSExportAssignment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNamespaceExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNamespaceExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNamespaceExportDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeParameterInstantiation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeParameterInstantiation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeParameterInstantiation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeParameterDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeParameterDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeParameterDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeParameter(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeParameter {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeParameter\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStandardized(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Standardized {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ArrayExpression\" === nodeType ||\n    \"AssignmentExpression\" === nodeType ||\n    \"BinaryExpression\" === nodeType ||\n    \"InterpreterDirective\" === nodeType ||\n    \"Directive\" === nodeType ||\n    \"DirectiveLiteral\" === nodeType ||\n    \"BlockStatement\" === nodeType ||\n    \"BreakStatement\" === nodeType ||\n    \"CallExpression\" === nodeType ||\n    \"CatchClause\" === nodeType ||\n    \"ConditionalExpression\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"DebuggerStatement\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"EmptyStatement\" === nodeType ||\n    \"ExpressionStatement\" === nodeType ||\n    \"File\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Identifier\" === nodeType ||\n    \"IfStatement\" === nodeType ||\n    \"LabeledStatement\" === nodeType ||\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"LogicalExpression\" === nodeType ||\n    \"MemberExpression\" === nodeType ||\n    \"NewExpression\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"ObjectExpression\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"ObjectProperty\" === nodeType ||\n    \"RestElement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"SequenceExpression\" === nodeType ||\n    \"ParenthesizedExpression\" === nodeType ||\n    \"SwitchCase\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"ThisExpression\" === nodeType ||\n    \"ThrowStatement\" === nodeType ||\n    \"TryStatement\" === nodeType ||\n    \"UnaryExpression\" === nodeType ||\n    \"UpdateExpression\" === nodeType ||\n    \"VariableDeclaration\" === nodeType ||\n    \"VariableDeclarator\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"WithStatement\" === nodeType ||\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassBody\" === nodeType ||\n    \"ClassExpression\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ExportSpecifier\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ImportDeclaration\" === nodeType ||\n    \"ImportDefaultSpecifier\" === nodeType ||\n    \"ImportNamespaceSpecifier\" === nodeType ||\n    \"ImportSpecifier\" === nodeType ||\n    \"MetaProperty\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    \"SpreadElement\" === nodeType ||\n    \"Super\" === nodeType ||\n    \"TaggedTemplateExpression\" === nodeType ||\n    \"TemplateElement\" === nodeType ||\n    \"TemplateLiteral\" === nodeType ||\n    \"YieldExpression\" === nodeType ||\n    \"AwaitExpression\" === nodeType ||\n    \"Import\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"ExportNamespaceSpecifier\" === nodeType ||\n    \"OptionalMemberExpression\" === nodeType ||\n    \"OptionalCallExpression\" === nodeType ||\n    \"ClassProperty\" === nodeType ||\n    \"ClassAccessorProperty\" === nodeType ||\n    \"ClassPrivateProperty\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"PrivateName\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Identifier\" === (node as t.Placeholder).expectedNode ||\n        \"StringLiteral\" === (node as t.Placeholder).expectedNode ||\n        \"BlockStatement\" === (node as t.Placeholder).expectedNode ||\n        \"ClassBody\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Expression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ArrayExpression\" === nodeType ||\n    \"AssignmentExpression\" === nodeType ||\n    \"BinaryExpression\" === nodeType ||\n    \"CallExpression\" === nodeType ||\n    \"ConditionalExpression\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Identifier\" === nodeType ||\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"LogicalExpression\" === nodeType ||\n    \"MemberExpression\" === nodeType ||\n    \"NewExpression\" === nodeType ||\n    \"ObjectExpression\" === nodeType ||\n    \"SequenceExpression\" === nodeType ||\n    \"ParenthesizedExpression\" === nodeType ||\n    \"ThisExpression\" === nodeType ||\n    \"UnaryExpression\" === nodeType ||\n    \"UpdateExpression\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassExpression\" === nodeType ||\n    \"MetaProperty\" === nodeType ||\n    \"Super\" === nodeType ||\n    \"TaggedTemplateExpression\" === nodeType ||\n    \"TemplateLiteral\" === nodeType ||\n    \"YieldExpression\" === nodeType ||\n    \"AwaitExpression\" === nodeType ||\n    \"Import\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"OptionalMemberExpression\" === nodeType ||\n    \"OptionalCallExpression\" === nodeType ||\n    \"TypeCastExpression\" === nodeType ||\n    \"JSXElement\" === nodeType ||\n    \"JSXFragment\" === nodeType ||\n    \"BindExpression\" === nodeType ||\n    \"DoExpression\" === nodeType ||\n    \"RecordExpression\" === nodeType ||\n    \"TupleExpression\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    \"ModuleExpression\" === nodeType ||\n    \"TopicReference\" === nodeType ||\n    \"PipelineTopicExpression\" === nodeType ||\n    \"PipelineBareFunction\" === nodeType ||\n    \"PipelinePrimaryTopicReference\" === nodeType ||\n    \"TSInstantiationExpression\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Expression\" === (node as t.Placeholder).expectedNode ||\n        \"Identifier\" === (node as t.Placeholder).expectedNode ||\n        \"StringLiteral\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBinary(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Binary {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"BinaryExpression\" === nodeType || \"LogicalExpression\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isScopable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Scopable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"CatchClause\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassExpression\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"BlockStatement\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBlockParent(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BlockParent {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"CatchClause\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"BlockStatement\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBlock(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Block {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"BlockStatement\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Statement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"BreakStatement\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"DebuggerStatement\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"EmptyStatement\" === nodeType ||\n    \"ExpressionStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"IfStatement\" === nodeType ||\n    \"LabeledStatement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"ThrowStatement\" === nodeType ||\n    \"TryStatement\" === nodeType ||\n    \"VariableDeclaration\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"WithStatement\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ImportDeclaration\" === nodeType ||\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"TypeAlias\" === nodeType ||\n    \"EnumDeclaration\" === nodeType ||\n    \"TSDeclareFunction\" === nodeType ||\n    \"TSInterfaceDeclaration\" === nodeType ||\n    \"TSTypeAliasDeclaration\" === nodeType ||\n    \"TSEnumDeclaration\" === nodeType ||\n    \"TSModuleDeclaration\" === nodeType ||\n    \"TSImportEqualsDeclaration\" === nodeType ||\n    \"TSExportAssignment\" === nodeType ||\n    \"TSNamespaceExportDeclaration\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Statement\" === (node as t.Placeholder).expectedNode ||\n        \"Declaration\" === (node as t.Placeholder).expectedNode ||\n        \"BlockStatement\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTerminatorless(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Terminatorless {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BreakStatement\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"ThrowStatement\" === nodeType ||\n    \"YieldExpression\" === nodeType ||\n    \"AwaitExpression\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isCompletionStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.CompletionStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BreakStatement\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"ThrowStatement\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isConditional(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Conditional {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ConditionalExpression\" === nodeType || \"IfStatement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLoop(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Loop {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"DoWhileStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"ForOfStatement\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isWhile(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.While {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"DoWhileStatement\" === nodeType || \"WhileStatement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExpressionWrapper(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExpressionWrapper {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExpressionStatement\" === nodeType ||\n    \"ParenthesizedExpression\" === nodeType ||\n    \"TypeCastExpression\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFor(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.For {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"ForOfStatement\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForXStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForXStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ForInStatement\" === nodeType || \"ForOfStatement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Function {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionParent(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionParent {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"StaticBlock\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPureish(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Pureish {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"StringLiteral\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Declaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"VariableDeclaration\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ImportDeclaration\" === nodeType ||\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"TypeAlias\" === nodeType ||\n    \"EnumDeclaration\" === nodeType ||\n    \"TSDeclareFunction\" === nodeType ||\n    \"TSInterfaceDeclaration\" === nodeType ||\n    \"TSTypeAliasDeclaration\" === nodeType ||\n    \"TSEnumDeclaration\" === nodeType ||\n    \"TSModuleDeclaration\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"Declaration\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPatternLike(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PatternLike {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Identifier\" === nodeType ||\n    \"RestElement\" === nodeType ||\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Pattern\" === (node as t.Placeholder).expectedNode ||\n        \"Identifier\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLVal(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.LVal {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Identifier\" === nodeType ||\n    \"MemberExpression\" === nodeType ||\n    \"RestElement\" === nodeType ||\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    \"TSParameterProperty\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Pattern\" === (node as t.Placeholder).expectedNode ||\n        \"Identifier\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSEntityName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSEntityName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Identifier\" === nodeType ||\n    \"TSQualifiedName\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"Identifier\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Literal {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"TemplateLiteral\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"StringLiteral\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImmutable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Immutable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"JSXAttribute\" === nodeType ||\n    \"JSXClosingElement\" === nodeType ||\n    \"JSXElement\" === nodeType ||\n    \"JSXExpressionContainer\" === nodeType ||\n    \"JSXSpreadChild\" === nodeType ||\n    \"JSXOpeningElement\" === nodeType ||\n    \"JSXText\" === nodeType ||\n    \"JSXFragment\" === nodeType ||\n    \"JSXOpeningFragment\" === nodeType ||\n    \"JSXClosingFragment\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"StringLiteral\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUserWhitespacable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UserWhitespacable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ObjectMethod\" === nodeType ||\n    \"ObjectProperty\" === nodeType ||\n    \"ObjectTypeInternalSlot\" === nodeType ||\n    \"ObjectTypeCallProperty\" === nodeType ||\n    \"ObjectTypeIndexer\" === nodeType ||\n    \"ObjectTypeProperty\" === nodeType ||\n    \"ObjectTypeSpreadProperty\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Method {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ObjectMethod\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ObjectMethod\" === nodeType || \"ObjectProperty\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Property {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ObjectProperty\" === nodeType ||\n    \"ClassProperty\" === nodeType ||\n    \"ClassAccessorProperty\" === nodeType ||\n    \"ClassPrivateProperty\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUnaryLike(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UnaryLike {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"UnaryExpression\" === nodeType || \"SpreadElement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Pattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"Pattern\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClass(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Class {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ClassExpression\" === nodeType || \"ClassDeclaration\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isModuleDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ModuleDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ImportDeclaration\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isModuleSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ModuleSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExportSpecifier\" === nodeType ||\n    \"ImportDefaultSpecifier\" === nodeType ||\n    \"ImportNamespaceSpecifier\" === nodeType ||\n    \"ImportSpecifier\" === nodeType ||\n    \"ExportNamespaceSpecifier\" === nodeType ||\n    \"ExportDefaultSpecifier\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAccessor(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Accessor {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ClassAccessorProperty\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPrivate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Private {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ClassPrivateProperty\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"PrivateName\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlow(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Flow {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AnyTypeAnnotation\" === nodeType ||\n    \"ArrayTypeAnnotation\" === nodeType ||\n    \"BooleanTypeAnnotation\" === nodeType ||\n    \"BooleanLiteralTypeAnnotation\" === nodeType ||\n    \"NullLiteralTypeAnnotation\" === nodeType ||\n    \"ClassImplements\" === nodeType ||\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"DeclaredPredicate\" === nodeType ||\n    \"ExistsTypeAnnotation\" === nodeType ||\n    \"FunctionTypeAnnotation\" === nodeType ||\n    \"FunctionTypeParam\" === nodeType ||\n    \"GenericTypeAnnotation\" === nodeType ||\n    \"InferredPredicate\" === nodeType ||\n    \"InterfaceExtends\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"InterfaceTypeAnnotation\" === nodeType ||\n    \"IntersectionTypeAnnotation\" === nodeType ||\n    \"MixedTypeAnnotation\" === nodeType ||\n    \"EmptyTypeAnnotation\" === nodeType ||\n    \"NullableTypeAnnotation\" === nodeType ||\n    \"NumberLiteralTypeAnnotation\" === nodeType ||\n    \"NumberTypeAnnotation\" === nodeType ||\n    \"ObjectTypeAnnotation\" === nodeType ||\n    \"ObjectTypeInternalSlot\" === nodeType ||\n    \"ObjectTypeCallProperty\" === nodeType ||\n    \"ObjectTypeIndexer\" === nodeType ||\n    \"ObjectTypeProperty\" === nodeType ||\n    \"ObjectTypeSpreadProperty\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"QualifiedTypeIdentifier\" === nodeType ||\n    \"StringLiteralTypeAnnotation\" === nodeType ||\n    \"StringTypeAnnotation\" === nodeType ||\n    \"SymbolTypeAnnotation\" === nodeType ||\n    \"ThisTypeAnnotation\" === nodeType ||\n    \"TupleTypeAnnotation\" === nodeType ||\n    \"TypeofTypeAnnotation\" === nodeType ||\n    \"TypeAlias\" === nodeType ||\n    \"TypeAnnotation\" === nodeType ||\n    \"TypeCastExpression\" === nodeType ||\n    \"TypeParameter\" === nodeType ||\n    \"TypeParameterDeclaration\" === nodeType ||\n    \"TypeParameterInstantiation\" === nodeType ||\n    \"UnionTypeAnnotation\" === nodeType ||\n    \"Variance\" === nodeType ||\n    \"VoidTypeAnnotation\" === nodeType ||\n    \"EnumDeclaration\" === nodeType ||\n    \"EnumBooleanBody\" === nodeType ||\n    \"EnumNumberBody\" === nodeType ||\n    \"EnumStringBody\" === nodeType ||\n    \"EnumSymbolBody\" === nodeType ||\n    \"EnumBooleanMember\" === nodeType ||\n    \"EnumNumberMember\" === nodeType ||\n    \"EnumStringMember\" === nodeType ||\n    \"EnumDefaultedMember\" === nodeType ||\n    \"IndexedAccessType\" === nodeType ||\n    \"OptionalIndexedAccessType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AnyTypeAnnotation\" === nodeType ||\n    \"ArrayTypeAnnotation\" === nodeType ||\n    \"BooleanTypeAnnotation\" === nodeType ||\n    \"BooleanLiteralTypeAnnotation\" === nodeType ||\n    \"NullLiteralTypeAnnotation\" === nodeType ||\n    \"ExistsTypeAnnotation\" === nodeType ||\n    \"FunctionTypeAnnotation\" === nodeType ||\n    \"GenericTypeAnnotation\" === nodeType ||\n    \"InterfaceTypeAnnotation\" === nodeType ||\n    \"IntersectionTypeAnnotation\" === nodeType ||\n    \"MixedTypeAnnotation\" === nodeType ||\n    \"EmptyTypeAnnotation\" === nodeType ||\n    \"NullableTypeAnnotation\" === nodeType ||\n    \"NumberLiteralTypeAnnotation\" === nodeType ||\n    \"NumberTypeAnnotation\" === nodeType ||\n    \"ObjectTypeAnnotation\" === nodeType ||\n    \"StringLiteralTypeAnnotation\" === nodeType ||\n    \"StringTypeAnnotation\" === nodeType ||\n    \"SymbolTypeAnnotation\" === nodeType ||\n    \"ThisTypeAnnotation\" === nodeType ||\n    \"TupleTypeAnnotation\" === nodeType ||\n    \"TypeofTypeAnnotation\" === nodeType ||\n    \"UnionTypeAnnotation\" === nodeType ||\n    \"VoidTypeAnnotation\" === nodeType ||\n    \"IndexedAccessType\" === nodeType ||\n    \"OptionalIndexedAccessType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowBaseAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowBaseAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AnyTypeAnnotation\" === nodeType ||\n    \"BooleanTypeAnnotation\" === nodeType ||\n    \"NullLiteralTypeAnnotation\" === nodeType ||\n    \"MixedTypeAnnotation\" === nodeType ||\n    \"EmptyTypeAnnotation\" === nodeType ||\n    \"NumberTypeAnnotation\" === nodeType ||\n    \"StringTypeAnnotation\" === nodeType ||\n    \"SymbolTypeAnnotation\" === nodeType ||\n    \"ThisTypeAnnotation\" === nodeType ||\n    \"VoidTypeAnnotation\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"TypeAlias\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowPredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowPredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"DeclaredPredicate\" === nodeType || \"InferredPredicate\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"EnumBooleanBody\" === nodeType ||\n    \"EnumNumberBody\" === nodeType ||\n    \"EnumStringBody\" === nodeType ||\n    \"EnumSymbolBody\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"EnumBooleanMember\" === nodeType ||\n    \"EnumNumberMember\" === nodeType ||\n    \"EnumStringMember\" === nodeType ||\n    \"EnumDefaultedMember\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSX(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSX {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"JSXAttribute\" === nodeType ||\n    \"JSXClosingElement\" === nodeType ||\n    \"JSXElement\" === nodeType ||\n    \"JSXEmptyExpression\" === nodeType ||\n    \"JSXExpressionContainer\" === nodeType ||\n    \"JSXSpreadChild\" === nodeType ||\n    \"JSXIdentifier\" === nodeType ||\n    \"JSXMemberExpression\" === nodeType ||\n    \"JSXNamespacedName\" === nodeType ||\n    \"JSXOpeningElement\" === nodeType ||\n    \"JSXSpreadAttribute\" === nodeType ||\n    \"JSXText\" === nodeType ||\n    \"JSXFragment\" === nodeType ||\n    \"JSXOpeningFragment\" === nodeType ||\n    \"JSXClosingFragment\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMiscellaneous(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Miscellaneous {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Noop\" === nodeType ||\n    \"Placeholder\" === nodeType ||\n    \"V8IntrinsicIdentifier\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeScript(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeScript {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSParameterProperty\" === nodeType ||\n    \"TSDeclareFunction\" === nodeType ||\n    \"TSDeclareMethod\" === nodeType ||\n    \"TSQualifiedName\" === nodeType ||\n    \"TSCallSignatureDeclaration\" === nodeType ||\n    \"TSConstructSignatureDeclaration\" === nodeType ||\n    \"TSPropertySignature\" === nodeType ||\n    \"TSMethodSignature\" === nodeType ||\n    \"TSIndexSignature\" === nodeType ||\n    \"TSAnyKeyword\" === nodeType ||\n    \"TSBooleanKeyword\" === nodeType ||\n    \"TSBigIntKeyword\" === nodeType ||\n    \"TSIntrinsicKeyword\" === nodeType ||\n    \"TSNeverKeyword\" === nodeType ||\n    \"TSNullKeyword\" === nodeType ||\n    \"TSNumberKeyword\" === nodeType ||\n    \"TSObjectKeyword\" === nodeType ||\n    \"TSStringKeyword\" === nodeType ||\n    \"TSSymbolKeyword\" === nodeType ||\n    \"TSUndefinedKeyword\" === nodeType ||\n    \"TSUnknownKeyword\" === nodeType ||\n    \"TSVoidKeyword\" === nodeType ||\n    \"TSThisType\" === nodeType ||\n    \"TSFunctionType\" === nodeType ||\n    \"TSConstructorType\" === nodeType ||\n    \"TSTypeReference\" === nodeType ||\n    \"TSTypePredicate\" === nodeType ||\n    \"TSTypeQuery\" === nodeType ||\n    \"TSTypeLiteral\" === nodeType ||\n    \"TSArrayType\" === nodeType ||\n    \"TSTupleType\" === nodeType ||\n    \"TSOptionalType\" === nodeType ||\n    \"TSRestType\" === nodeType ||\n    \"TSNamedTupleMember\" === nodeType ||\n    \"TSUnionType\" === nodeType ||\n    \"TSIntersectionType\" === nodeType ||\n    \"TSConditionalType\" === nodeType ||\n    \"TSInferType\" === nodeType ||\n    \"TSParenthesizedType\" === nodeType ||\n    \"TSTypeOperator\" === nodeType ||\n    \"TSIndexedAccessType\" === nodeType ||\n    \"TSMappedType\" === nodeType ||\n    \"TSLiteralType\" === nodeType ||\n    \"TSExpressionWithTypeArguments\" === nodeType ||\n    \"TSInterfaceDeclaration\" === nodeType ||\n    \"TSInterfaceBody\" === nodeType ||\n    \"TSTypeAliasDeclaration\" === nodeType ||\n    \"TSInstantiationExpression\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSEnumDeclaration\" === nodeType ||\n    \"TSEnumMember\" === nodeType ||\n    \"TSModuleDeclaration\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    \"TSImportType\" === nodeType ||\n    \"TSImportEqualsDeclaration\" === nodeType ||\n    \"TSExternalModuleReference\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    \"TSExportAssignment\" === nodeType ||\n    \"TSNamespaceExportDeclaration\" === nodeType ||\n    \"TSTypeAnnotation\" === nodeType ||\n    \"TSTypeParameterInstantiation\" === nodeType ||\n    \"TSTypeParameterDeclaration\" === nodeType ||\n    \"TSTypeParameter\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSCallSignatureDeclaration\" === nodeType ||\n    \"TSConstructSignatureDeclaration\" === nodeType ||\n    \"TSPropertySignature\" === nodeType ||\n    \"TSMethodSignature\" === nodeType ||\n    \"TSIndexSignature\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSAnyKeyword\" === nodeType ||\n    \"TSBooleanKeyword\" === nodeType ||\n    \"TSBigIntKeyword\" === nodeType ||\n    \"TSIntrinsicKeyword\" === nodeType ||\n    \"TSNeverKeyword\" === nodeType ||\n    \"TSNullKeyword\" === nodeType ||\n    \"TSNumberKeyword\" === nodeType ||\n    \"TSObjectKeyword\" === nodeType ||\n    \"TSStringKeyword\" === nodeType ||\n    \"TSSymbolKeyword\" === nodeType ||\n    \"TSUndefinedKeyword\" === nodeType ||\n    \"TSUnknownKeyword\" === nodeType ||\n    \"TSVoidKeyword\" === nodeType ||\n    \"TSThisType\" === nodeType ||\n    \"TSFunctionType\" === nodeType ||\n    \"TSConstructorType\" === nodeType ||\n    \"TSTypeReference\" === nodeType ||\n    \"TSTypePredicate\" === nodeType ||\n    \"TSTypeQuery\" === nodeType ||\n    \"TSTypeLiteral\" === nodeType ||\n    \"TSArrayType\" === nodeType ||\n    \"TSTupleType\" === nodeType ||\n    \"TSOptionalType\" === nodeType ||\n    \"TSRestType\" === nodeType ||\n    \"TSUnionType\" === nodeType ||\n    \"TSIntersectionType\" === nodeType ||\n    \"TSConditionalType\" === nodeType ||\n    \"TSInferType\" === nodeType ||\n    \"TSParenthesizedType\" === nodeType ||\n    \"TSTypeOperator\" === nodeType ||\n    \"TSIndexedAccessType\" === nodeType ||\n    \"TSMappedType\" === nodeType ||\n    \"TSLiteralType\" === nodeType ||\n    \"TSExpressionWithTypeArguments\" === nodeType ||\n    \"TSImportType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSBaseType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSBaseType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSAnyKeyword\" === nodeType ||\n    \"TSBooleanKeyword\" === nodeType ||\n    \"TSBigIntKeyword\" === nodeType ||\n    \"TSIntrinsicKeyword\" === nodeType ||\n    \"TSNeverKeyword\" === nodeType ||\n    \"TSNullKeyword\" === nodeType ||\n    \"TSNumberKeyword\" === nodeType ||\n    \"TSObjectKeyword\" === nodeType ||\n    \"TSStringKeyword\" === nodeType ||\n    \"TSSymbolKeyword\" === nodeType ||\n    \"TSUndefinedKeyword\" === nodeType ||\n    \"TSUnknownKeyword\" === nodeType ||\n    \"TSVoidKeyword\" === nodeType ||\n    \"TSThisType\" === nodeType ||\n    \"TSLiteralType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumberLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  console.trace(\n    \"The node type NumberLiteral has been renamed to NumericLiteral\",\n  );\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumberLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRegexLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  console.trace(\"The node type RegexLiteral has been renamed to RegExpLiteral\");\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RegexLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRestProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  console.trace(\"The node type RestProperty has been renamed to RestElement\");\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RestProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSpreadProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  console.trace(\n    \"The node type SpreadProperty has been renamed to SpreadElement\",\n  );\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SpreadProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;;AAGO,SAASA,iBAAT,CACLC,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASI,sBAAT,CACLL,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASK,kBAAT,CACLN,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASM,sBAAT,CACLP,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASO,WAAT,CACLR,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,WAAjB,EAA8B;IAC5B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASQ,kBAAT,CACLT,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASS,gBAAT,CACLV,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASU,gBAAT,CACLX,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASW,gBAAT,CACLZ,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASY,aAAT,CACLb,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASa,uBAAT,CACLd,IADK,EAELC,IAFK,EAG4B;EACjC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,uBAAjB,EAA0C;IACxC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASc,mBAAT,CACLf,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASe,mBAAT,CACLhB,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgB,kBAAT,CACLjB,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiB,gBAAT,CACLlB,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkB,qBAAT,CACLnB,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmB,MAAT,CACLpB,IADK,EAELC,IAFK,EAGW;EAChB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,MAAjB,EAAyB;IACvB,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoB,gBAAT,CACLrB,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqB,cAAT,CACLtB,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsB,qBAAT,CACLvB,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuB,oBAAT,CACLxB,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwB,YAAT,CACLzB,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,YAAjB,EAA+B;IAC7B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyB,aAAT,CACL1B,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0B,kBAAT,CACL3B,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2B,eAAT,CACL5B,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4B,gBAAT,CACL7B,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6B,aAAT,CACL9B,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8B,gBAAT,CACL/B,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+B,eAAT,CACLhC,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgC,mBAAT,CACLjC,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiC,kBAAT,CACLlC,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkC,eAAT,CACLnC,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmC,SAAT,CACLpC,IADK,EAELC,IAFK,EAGc;EACnB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,SAAjB,EAA4B;IAC1B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoC,kBAAT,CACLrC,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqC,cAAT,CACLtC,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsC,gBAAT,CACLvC,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuC,aAAT,CACLxC,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwC,iBAAT,CACLzC,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyC,oBAAT,CACL1C,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0C,yBAAT,CACL3C,IADK,EAELC,IAFK,EAG8B;EACnC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,yBAAjB,EAA4C;IAC1C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2C,YAAT,CACL5C,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,YAAjB,EAA+B;IAC7B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4C,iBAAT,CACL7C,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6C,gBAAT,CACL9C,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8C,gBAAT,CACL/C,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+C,cAAT,CACLhD,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgD,iBAAT,CACLjD,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiD,kBAAT,CACLlD,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkD,qBAAT,CACLnD,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmD,oBAAT,CACLpD,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoD,gBAAT,CACLrD,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqD,eAAT,CACLtD,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsD,mBAAT,CACLvD,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuD,cAAT,CACLxD,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwD,yBAAT,CACLzD,IADK,EAELC,IAFK,EAG8B;EACnC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,yBAAjB,EAA4C;IAC1C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyD,WAAT,CACL1D,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,WAAjB,EAA8B;IAC5B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0D,iBAAT,CACL3D,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2D,kBAAT,CACL5D,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4D,sBAAT,CACL7D,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6D,0BAAT,CACL9D,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8D,wBAAT,CACL/D,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+D,iBAAT,CACLhE,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgE,gBAAT,CACLjE,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiE,mBAAT,CACLlE,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkE,wBAAT,CACLnE,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmE,0BAAT,CACLpE,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoE,iBAAT,CACLrE,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqE,cAAT,CACLtE,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsE,aAAT,CACLvE,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuE,eAAT,CACLxE,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwE,eAAT,CACLzE,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyE,OAAT,CACL1E,IADK,EAELC,IAFK,EAGY;EACjB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,OAAjB,EAA0B;IACxB,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0E,0BAAT,CACL3E,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2E,iBAAT,CACL5E,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4E,iBAAT,CACL7E,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6E,iBAAT,CACL9E,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8E,iBAAT,CACL/E,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+E,QAAT,CACLhF,IADK,EAELC,IAFK,EAGa;EAClB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,QAAjB,EAA2B;IACzB,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgF,eAAT,CACLjF,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiF,0BAAT,CACLlF,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkF,0BAAT,CACLnF,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmF,wBAAT,CACLpF,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoF,eAAT,CACLrF,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqF,uBAAT,CACLtF,IADK,EAELC,IAFK,EAG4B;EACjC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,uBAAjB,EAA0C;IACxC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsF,sBAAT,CACLvF,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuF,oBAAT,CACLxF,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwF,aAAT,CACLzF,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyF,aAAT,CACL1F,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0F,mBAAT,CACL3F,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2F,qBAAT,CACL5F,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4F,uBAAT,CACL7F,IADK,EAELC,IAFK,EAG4B;EACjC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,uBAAjB,EAA0C;IACxC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6F,8BAAT,CACL9F,IADK,EAELC,IAFK,EAGmC;EACxC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,8BAAjB,EAAiD;IAC/C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8F,2BAAT,CACL/F,IADK,EAELC,IAFK,EAGgC;EACrC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,2BAAjB,EAA8C;IAC5C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+F,iBAAT,CACLhG,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgG,cAAT,CACLjG,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiG,iBAAT,CACLlG,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkG,kBAAT,CACLnG,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmG,eAAT,CACLpG,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoG,sBAAT,CACLrG,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqG,kBAAT,CACLtG,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsG,mBAAT,CACLvG,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuG,iBAAT,CACLxG,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwG,0BAAT,CACLzG,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyG,6BAAT,CACL1G,IADK,EAELC,IAFK,EAGkC;EACvC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,6BAAjB,EAAgD;IAC9C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0G,mBAAT,CACL3G,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2G,sBAAT,CACL5G,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4G,wBAAT,CACL7G,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6G,mBAAT,CACL9G,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8G,uBAAT,CACL/G,IADK,EAELC,IAFK,EAG4B;EACjC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,uBAAjB,EAA0C;IACxC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+G,mBAAT,CACLhH,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgH,kBAAT,CACLjH,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiH,sBAAT,CACLlH,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkH,yBAAT,CACLnH,IADK,EAELC,IAFK,EAG8B;EACnC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,yBAAjB,EAA4C;IAC1C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmH,4BAAT,CACLpH,IADK,EAELC,IAFK,EAGiC;EACtC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,4BAAjB,EAA+C;IAC7C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoH,qBAAT,CACLrH,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqH,qBAAT,CACLtH,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsH,wBAAT,CACLvH,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuH,6BAAT,CACLxH,IADK,EAELC,IAFK,EAGkC;EACvC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,6BAAjB,EAAgD;IAC9C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwH,sBAAT,CACLzH,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyH,sBAAT,CACL1H,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0H,wBAAT,CACL3H,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2H,wBAAT,CACL5H,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4H,mBAAT,CACL7H,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6H,oBAAT,CACL9H,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8H,0BAAT,CACL/H,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+H,YAAT,CACLhI,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,YAAjB,EAA+B;IAC7B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgI,yBAAT,CACLjI,IADK,EAELC,IAFK,EAG8B;EACnC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,yBAAjB,EAA4C;IAC1C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiI,6BAAT,CACLlI,IADK,EAELC,IAFK,EAGkC;EACvC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,6BAAjB,EAAgD;IAC9C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkI,sBAAT,CACLnI,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmI,sBAAT,CACLpI,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoI,oBAAT,CACLrI,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqI,qBAAT,CACLtI,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsI,sBAAT,CACLvI,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuI,WAAT,CACLxI,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,WAAjB,EAA8B;IAC5B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwI,gBAAT,CACLzI,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyI,oBAAT,CACL1I,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0I,eAAT,CACL3I,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2I,0BAAT,CACL5I,IADK,EAELC,IAFK,EAG+B;EACpC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,0BAAjB,EAA6C;IAC3C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4I,4BAAT,CACL7I,IADK,EAELC,IAFK,EAGiC;EACtC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,4BAAjB,EAA+C;IAC7C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6I,qBAAT,CACL9I,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8I,UAAT,CACL/I,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,UAAjB,EAA6B;IAC3B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+I,oBAAT,CACLhJ,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgJ,iBAAT,CACLjJ,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiJ,iBAAT,CACLlJ,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkJ,gBAAT,CACLnJ,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmJ,gBAAT,CACLpJ,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoJ,gBAAT,CACLrJ,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqJ,mBAAT,CACLtJ,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsJ,kBAAT,CACLvJ,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuJ,kBAAT,CACLxJ,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwJ,qBAAT,CACLzJ,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyJ,mBAAT,CACL1J,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0J,2BAAT,CACL3J,IADK,EAELC,IAFK,EAGgC;EACrC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,2BAAjB,EAA8C;IAC5C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2J,cAAT,CACL5J,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4J,mBAAT,CACL7J,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6J,YAAT,CACL9J,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,YAAjB,EAA+B;IAC7B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8J,oBAAT,CACL/J,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+J,wBAAT,CACLhK,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgK,gBAAT,CACLjK,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiK,eAAT,CACLlK,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkK,qBAAT,CACLnK,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmK,mBAAT,CACLpK,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoK,mBAAT,CACLrK,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqK,oBAAT,CACLtK,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsK,SAAT,CACLvK,IADK,EAELC,IAFK,EAGc;EACnB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,SAAjB,EAA4B;IAC1B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuK,aAAT,CACLxK,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwK,oBAAT,CACLzK,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyK,oBAAT,CACL1K,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0K,MAAT,CACL3K,IADK,EAELC,IAFK,EAGW;EAChB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,MAAjB,EAAyB;IACvB,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2K,aAAT,CACL5K,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4K,uBAAT,CACL7K,IADK,EAELC,IAFK,EAG4B;EACjC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,uBAAjB,EAA0C;IACxC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6K,qBAAT,CACL9K,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8K,gBAAT,CACL/K,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+K,iBAAT,CACLhL,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgL,WAAT,CACLjL,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,WAAjB,EAA8B;IAC5B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiL,cAAT,CACLlL,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkL,wBAAT,CACLnL,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmL,kBAAT,CACLpL,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoL,iBAAT,CACLrL,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqL,gBAAT,CACLtL,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsL,kBAAT,CACLvL,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuL,gBAAT,CACLxL,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwL,yBAAT,CACLzL,IADK,EAELC,IAFK,EAG8B;EACnC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,yBAAjB,EAA4C;IAC1C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyL,sBAAT,CACL1L,IADK,EAELC,IAFK,EAG2B;EAChC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,sBAAjB,EAAyC;IACvC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0L,+BAAT,CACL3L,IADK,EAELC,IAFK,EAGoC;EACzC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,+BAAjB,EAAkD;IAChD,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2L,qBAAT,CACL5L,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4L,mBAAT,CACL7L,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6L,iBAAT,CACL9L,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8L,iBAAT,CACL/L,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+L,4BAAT,CACLhM,IADK,EAELC,IAFK,EAGiC;EACtC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,4BAAjB,EAA+C;IAC7C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgM,iCAAT,CACLjM,IADK,EAELC,IAFK,EAGsC;EAC3C,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iCAAjB,EAAoD;IAClD,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiM,qBAAT,CACLlM,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkM,mBAAT,CACLnM,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmM,kBAAT,CACLpM,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoM,cAAT,CACLrM,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqM,kBAAT,CACLtM,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsM,iBAAT,CACLvM,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuM,oBAAT,CACLxM,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwM,gBAAT,CACLzM,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyM,eAAT,CACL1M,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0M,iBAAT,CACL3M,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2M,iBAAT,CACL5M,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4M,iBAAT,CACL7M,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6M,iBAAT,CACL9M,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8M,oBAAT,CACL/M,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+M,kBAAT,CACLhN,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgN,eAAT,CACLjN,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiN,YAAT,CACLlN,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,YAAjB,EAA+B;IAC7B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkN,gBAAT,CACLnN,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmN,mBAAT,CACLpN,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoN,iBAAT,CACLrN,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqN,iBAAT,CACLtN,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsN,aAAT,CACLvN,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuN,eAAT,CACLxN,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwN,aAAT,CACLzN,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyN,aAAT,CACL1N,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0N,gBAAT,CACL3N,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2N,YAAT,CACL5N,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,YAAjB,EAA+B;IAC7B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4N,oBAAT,CACL7N,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6N,aAAT,CACL9N,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8N,oBAAT,CACL/N,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+N,mBAAT,CACLhO,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgO,aAAT,CACLjO,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,aAAjB,EAAgC;IAC9B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiO,qBAAT,CACLlO,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkO,gBAAT,CACLnO,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmO,qBAAT,CACLpO,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoO,cAAT,CACLrO,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqO,eAAT,CACLtO,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsO,+BAAT,CACLvO,IADK,EAELC,IAFK,EAGoC;EACzC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,+BAAjB,EAAkD;IAChD,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuO,wBAAT,CACLxO,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwO,iBAAT,CACLzO,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyO,wBAAT,CACL1O,IADK,EAELC,IAFK,EAG6B;EAClC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,wBAAjB,EAA2C;IACzC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0O,2BAAT,CACL3O,IADK,EAELC,IAFK,EAGgC;EACrC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,2BAAjB,EAA8C;IAC5C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2O,gBAAT,CACL5O,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4O,iBAAT,CACL7O,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6O,mBAAT,CACL9O,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,mBAAjB,EAAsC;IACpC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8O,cAAT,CACL/O,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+O,qBAAT,CACLhP,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgP,eAAT,CACLjP,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiP,cAAT,CACLlP,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkP,2BAAT,CACLnP,IADK,EAELC,IAFK,EAGgC;EACrC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,2BAAjB,EAA8C;IAC5C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmP,2BAAT,CACLpP,IADK,EAELC,IAFK,EAGgC;EACrC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,2BAAjB,EAA8C;IAC5C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoP,qBAAT,CACLrP,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,qBAAjB,EAAwC;IACtC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqP,oBAAT,CACLtP,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,oBAAjB,EAAuC;IACrC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsP,8BAAT,CACLvP,IADK,EAELC,IAFK,EAGmC;EACxC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,8BAAjB,EAAiD;IAC/C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuP,kBAAT,CACLxP,IADK,EAELC,IAFK,EAGuB;EAC5B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,kBAAjB,EAAqC;IACnC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwP,8BAAT,CACLzP,IADK,EAELC,IAFK,EAGmC;EACxC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,8BAAjB,EAAiD;IAC/C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyP,4BAAT,CACL1P,IADK,EAELC,IAFK,EAGiC;EACtC,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,4BAAjB,EAA+C;IAC7C,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0P,iBAAT,CACL3P,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,iBAAjB,EAAoC;IAClC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2P,cAAT,CACL5P,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,sBAAsBD,QAAtB,IACA,2BAA2BA,QAD3B,IAEA,uBAAuBA,QAFvB,IAGA,2BAA2BA,QAH3B,IAIA,gBAAgBA,QAJhB,IAKA,uBAAuBA,QALvB,IAMA,qBAAqBA,QANrB,IAOA,qBAAqBA,QAPrB,IAQA,qBAAqBA,QARrB,IASA,kBAAkBA,QATlB,IAUA,4BAA4BA,QAV5B,IAWA,wBAAwBA,QAXxB,IAYA,wBAAwBA,QAZxB,IAaA,uBAAuBA,QAbvB,IAcA,qBAAqBA,QAdrB,IAeA,0BAA0BA,QAf1B,IAgBA,WAAWA,QAhBX,IAiBA,qBAAqBA,QAjBrB,IAkBA,mBAAmBA,QAlBnB,IAmBA,0BAA0BA,QAnB1B,IAoBA,yBAAyBA,QApBzB,IAqBA,iBAAiBA,QArBjB,IAsBA,kBAAkBA,QAtBlB,IAuBA,uBAAuBA,QAvBvB,IAwBA,oBAAoBA,QAxBpB,IAyBA,qBAAqBA,QAzBrB,IA0BA,kBAAkBA,QA1BlB,IA2BA,qBAAqBA,QA3BrB,IA4BA,oBAAoBA,QA5BpB,IA6BA,wBAAwBA,QA7BxB,IA8BA,uBAAuBA,QA9BvB,IA+BA,oBAAoBA,QA/BpB,IAgCA,cAAcA,QAhCd,IAiCA,uBAAuBA,QAjCvB,IAkCA,mBAAmBA,QAlCnB,IAmCA,qBAAqBA,QAnCrB,IAoCA,kBAAkBA,QApClB,IAqCA,sBAAsBA,QArCtB,IAsCA,yBAAyBA,QAtCzB,IAuCA,8BAA8BA,QAvC9B,IAwCA,iBAAiBA,QAxCjB,IAyCA,sBAAsBA,QAzCtB,IA0CA,qBAAqBA,QA1CrB,IA2CA,qBAAqBA,QA3CrB,IA4CA,mBAAmBA,QA5CnB,IA6CA,sBAAsBA,QA7CtB,IA8CA,uBAAuBA,QA9CvB,IA+CA,0BAA0BA,QA/C1B,IAgDA,yBAAyBA,QAhDzB,IAiDA,qBAAqBA,QAjDrB,IAkDA,oBAAoBA,QAlDpB,IAmDA,wBAAwBA,QAnDxB,IAoDA,mBAAmBA,QApDnB,IAqDA,8BAA8BA,QArD9B,IAsDA,gBAAgBA,QAtDhB,IAuDA,sBAAsBA,QAvDtB,IAwDA,uBAAuBA,QAxDvB,IAyDA,2BAA2BA,QAzD3B,IA0DA,+BAA+BA,QA1D/B,IA2DA,6BAA6BA,QA3D7B,IA4DA,sBAAsBA,QA5DtB,IA6DA,qBAAqBA,QA7DrB,IA8DA,wBAAwBA,QA9DxB,IA+DA,6BAA6BA,QA/D7B,IAgEA,+BAA+BA,QAhE/B,IAiEA,sBAAsBA,QAjEtB,IAkEA,mBAAmBA,QAlEnB,IAmEA,kBAAkBA,QAnElB,IAoEA,oBAAoBA,QApEpB,IAqEA,oBAAoBA,QArEpB,IAsEA,YAAYA,QAtEZ,IAuEA,+BAA+BA,QAvE/B,IAwEA,sBAAsBA,QAxEtB,IAyEA,sBAAsBA,QAzEtB,IA0EA,sBAAsBA,QA1EtB,IA2EA,sBAAsBA,QA3EtB,IA4EA,aAAaA,QA5Eb,IA6EA,oBAAoBA,QA7EpB,IA8EA,+BAA+BA,QA9E/B,IA+EA,+BAA+BA,QA/E/B,IAgFA,6BAA6BA,QAhF7B,IAiFA,oBAAoBA,QAjFpB,IAkFA,4BAA4BA,QAlF5B,IAmFA,2BAA2BA,QAnF3B,IAoFA,yBAAyBA,QApFzB,IAqFA,kBAAkBA,QArFlB,IAsFA,kBAAkBA,QAtFlB,IAuFCA,QAAQ,KAAK,aAAb,KACE,iBAAkBF,IAAD,CAAwB6P,YAAzC,IACC,oBAAqB7P,IAAD,CAAwB6P,YAD7C,IAEC,qBAAsB7P,IAAD,CAAwB6P,YAF9C,IAGC,gBAAiB7P,IAAD,CAAwB6P,YAJ3C,CAxFH,EA6FE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6P,YAAT,CACL9P,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,sBAAsBD,QAAtB,IACA,2BAA2BA,QAD3B,IAEA,uBAAuBA,QAFvB,IAGA,qBAAqBA,QAHrB,IAIA,4BAA4BA,QAJ5B,IAKA,yBAAyBA,QALzB,IAMA,iBAAiBA,QANjB,IAOA,oBAAoBA,QAPpB,IAQA,qBAAqBA,QARrB,IASA,kBAAkBA,QATlB,IAUA,qBAAqBA,QAVrB,IAWA,oBAAoBA,QAXpB,IAYA,wBAAwBA,QAZxB,IAaA,uBAAuBA,QAbvB,IAcA,oBAAoBA,QAdpB,IAeA,uBAAuBA,QAfvB,IAgBA,yBAAyBA,QAhBzB,IAiBA,8BAA8BA,QAjB9B,IAkBA,qBAAqBA,QAlBrB,IAmBA,sBAAsBA,QAnBtB,IAoBA,uBAAuBA,QApBvB,IAqBA,8BAA8BA,QArB9B,IAsBA,sBAAsBA,QAtBtB,IAuBA,mBAAmBA,QAvBnB,IAwBA,YAAYA,QAxBZ,IAyBA,+BAA+BA,QAzB/B,IA0BA,sBAAsBA,QA1BtB,IA2BA,sBAAsBA,QA3BtB,IA4BA,sBAAsBA,QA5BtB,IA6BA,aAAaA,QA7Bb,IA8BA,oBAAoBA,QA9BpB,IA+BA,+BAA+BA,QA/B/B,IAgCA,6BAA6BA,QAhC7B,IAiCA,yBAAyBA,QAjCzB,IAkCA,iBAAiBA,QAlCjB,IAmCA,kBAAkBA,QAnClB,IAoCA,qBAAqBA,QApCrB,IAqCA,mBAAmBA,QArCnB,IAsCA,uBAAuBA,QAtCvB,IAuCA,sBAAsBA,QAvCtB,IAwCA,qBAAqBA,QAxCrB,IAyCA,uBAAuBA,QAzCvB,IA0CA,qBAAqBA,QA1CrB,IA2CA,8BAA8BA,QA3C9B,IA4CA,2BAA2BA,QA5C3B,IA6CA,oCAAoCA,QA7CpC,IA8CA,gCAAgCA,QA9ChC,IA+CA,qBAAqBA,QA/CrB,IAgDA,sBAAsBA,QAhDtB,IAiDA,0BAA0BA,QAjD1B,IAkDCA,QAAQ,KAAK,aAAb,KACE,iBAAkBF,IAAD,CAAwB6P,YAAzC,IACC,iBAAkB7P,IAAD,CAAwB6P,YAD1C,IAEC,oBAAqB7P,IAAD,CAAwB6P,YAH/C,CAnDH,EAuDE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8P,QAAT,CACL/P,IADK,EAELC,IAFK,EAGa;EAClB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,uBAAuBD,QAAvB,IAAmC,wBAAwBA,QAA/D,EAAyE;IACvE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+P,UAAT,CACLhQ,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,kBAAkBA,QADlB,IAEA,uBAAuBA,QAFvB,IAGA,qBAAqBA,QAHrB,IAIA,mBAAmBA,QAJnB,IAKA,0BAA0BA,QAL1B,IAMA,yBAAyBA,QANzB,IAOA,cAAcA,QAPd,IAQA,mBAAmBA,QARnB,IASA,sBAAsBA,QATtB,IAUA,qBAAqBA,QAVrB,IAWA,8BAA8BA,QAX9B,IAYA,sBAAsBA,QAZtB,IAaA,uBAAuBA,QAbvB,IAcA,qBAAqBA,QAdrB,IAeA,kBAAkBA,QAflB,IAgBA,yBAAyBA,QAhBzB,IAiBA,kBAAkBA,QAjBlB,IAkBA,oBAAoBA,QAlBpB,IAmBCA,QAAQ,KAAK,aAAb,IACC,qBAAsBF,IAAD,CAAwB6P,YArBjD,EAsBE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgQ,aAAT,CACLjQ,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,kBAAkBA,QADlB,IAEA,uBAAuBA,QAFvB,IAGA,qBAAqBA,QAHrB,IAIA,mBAAmBA,QAJnB,IAKA,0BAA0BA,QAL1B,IAMA,yBAAyBA,QANzB,IAOA,cAAcA,QAPd,IAQA,mBAAmBA,QARnB,IASA,sBAAsBA,QATtB,IAUA,qBAAqBA,QAVrB,IAWA,8BAA8BA,QAX9B,IAYA,qBAAqBA,QAZrB,IAaA,kBAAkBA,QAblB,IAcA,yBAAyBA,QAdzB,IAeA,kBAAkBA,QAflB,IAgBA,oBAAoBA,QAhBpB,IAiBCA,QAAQ,KAAK,aAAb,IACC,qBAAsBF,IAAD,CAAwB6P,YAnBjD,EAoBE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiQ,OAAT,CACLlQ,IADK,EAELC,IAFK,EAGY;EACjB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,cAAcA,QADd,IAEA,oBAAoBA,QAFpB,IAGCA,QAAQ,KAAK,aAAb,IACC,qBAAsBF,IAAD,CAAwB6P,YALjD,EAME;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkQ,WAAT,CACLnQ,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,qBAAqBA,QADrB,IAEA,wBAAwBA,QAFxB,IAGA,wBAAwBA,QAHxB,IAIA,uBAAuBA,QAJvB,IAKA,qBAAqBA,QALrB,IAMA,0BAA0BA,QAN1B,IAOA,qBAAqBA,QAPrB,IAQA,mBAAmBA,QARnB,IASA,0BAA0BA,QAT1B,IAUA,kBAAkBA,QAVlB,IAWA,uBAAuBA,QAXvB,IAYA,sBAAsBA,QAZtB,IAaA,sBAAsBA,QAbtB,IAcA,qBAAqBA,QAdrB,IAeA,mBAAmBA,QAfnB,IAgBA,0BAA0BA,QAhB1B,IAiBA,qBAAqBA,QAjBrB,IAkBA,oBAAoBA,QAlBpB,IAmBA,uBAAuBA,QAnBvB,IAoBA,2BAA2BA,QApB3B,IAqBA,+BAA+BA,QArB/B,IAsBA,6BAA6BA,QAtB7B,IAuBA,qBAAqBA,QAvBrB,IAwBA,wBAAwBA,QAxBxB,IAyBA,mBAAmBA,QAzBnB,IA0BA,sBAAsBA,QA1BtB,IA2BA,uBAAuBA,QA3BvB,IA4BA,oBAAoBA,QA5BpB,IA6BA,2BAA2BA,QA7B3B,IA8BA,uBAAuBA,QA9BvB,IA+BA,wBAAwBA,QA/BxB,IAgCA,sBAAsBA,QAhCtB,IAiCA,+BAA+BA,QAjC/B,IAkCA,kCAAkCA,QAlClC,IAmCA,2BAA2BA,QAnC3B,IAoCA,iBAAiBA,QApCjB,IAqCA,gBAAgBA,QArChB,IAsCA,sBAAsBA,QAtCtB,IAuCA,wBAAwBA,QAvCxB,IAwCA,6BAA6BA,QAxC7B,IAyCA,6BAA6BA,QAzC7B,IA0CA,wBAAwBA,QA1CxB,IA2CA,0BAA0BA,QA3C1B,IA4CA,gCAAgCA,QA5ChC,IA6CA,yBAAyBA,QA7CzB,IA8CA,mCAAmCA,QA9CnC,IA+CCA,QAAQ,KAAK,aAAb,KACE,gBAAiBF,IAAD,CAAwB6P,YAAxC,IACC,kBAAmB7P,IAAD,CAAwB6P,YAD3C,IAEC,qBAAsB7P,IAAD,CAAwB6P,YAHhD,CAhDH,EAoDE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmQ,gBAAT,CACLpQ,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,wBAAwBA,QADxB,IAEA,sBAAsBA,QAFtB,IAGA,qBAAqBA,QAHrB,IAIA,sBAAsBA,QAJtB,IAKA,sBAAsBA,QANxB,EAOE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoQ,qBAAT,CACLrQ,IADK,EAELC,IAFK,EAG0B;EAC/B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,wBAAwBA,QADxB,IAEA,sBAAsBA,QAFtB,IAGA,qBAAqBA,QAJvB,EAKE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqQ,aAAT,CACLtQ,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,4BAA4BD,QAA5B,IAAwC,kBAAkBA,QAA9D,EAAwE;IACtE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsQ,MAAT,CACLvQ,IADK,EAELC,IAFK,EAGW;EAChB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,uBAAuBD,QAAvB,IACA,qBAAqBA,QADrB,IAEA,mBAAmBA,QAFnB,IAGA,qBAAqBA,QAHrB,IAIA,qBAAqBA,QALvB,EAME;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuQ,OAAT,CACLxQ,IADK,EAELC,IAFK,EAGY;EACjB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,uBAAuBD,QAAvB,IAAmC,qBAAqBA,QAA5D,EAAsE;IACpE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwQ,mBAAT,CACLzQ,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,0BAA0BD,QAA1B,IACA,8BAA8BA,QAD9B,IAEA,yBAAyBA,QAH3B,EAIE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyQ,KAAT,CACL1Q,IADK,EAELC,IAFK,EAGU;EACf,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,mBAAmBA,QADnB,IAEA,qBAAqBA,QAHvB,EAIE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0Q,eAAT,CACL3Q,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,qBAAqBD,QAArB,IAAiC,qBAAqBA,QAA1D,EAAoE;IAClE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2Q,UAAT,CACL5Q,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,0BAA0BD,QAA1B,IACA,yBAAyBA,QADzB,IAEA,mBAAmBA,QAFnB,IAGA,8BAA8BA,QAH9B,IAIA,kBAAkBA,QAJlB,IAKA,yBAAyBA,QAN3B,EAOE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4Q,gBAAT,CACL7Q,IADK,EAELC,IAFK,EAGqB;EAC1B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,0BAA0BD,QAA1B,IACA,yBAAyBA,QADzB,IAEA,mBAAmBA,QAFnB,IAGA,8BAA8BA,QAH9B,IAIA,kBAAkBA,QAJlB,IAKA,yBAAyBA,QALzB,IAMA,kBAAkBA,QAPpB,EAQE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6Q,SAAT,CACL9Q,IADK,EAELC,IAFK,EAGc;EACnB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,0BAA0BD,QAA1B,IACA,yBAAyBA,QADzB,IAEA,oBAAoBA,QAFpB,IAGA,qBAAqBA,QAHrB,IAIA,kBAAkBA,QAJlB,IAKA,qBAAqBA,QALrB,IAMA,oBAAoBA,QANpB,IAOA,8BAA8BA,QAP9B,IAQA,oBAAoBA,QARpB,IASA,qBAAqBA,QATrB,IAUCA,QAAQ,KAAK,aAAb,IACC,oBAAqBF,IAAD,CAAwB6P,YAZhD,EAaE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8Q,aAAT,CACL/Q,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,0BAA0BD,QAA1B,IACA,0BAA0BA,QAD1B,IAEA,uBAAuBA,QAFvB,IAGA,2BAA2BA,QAH3B,IAIA,+BAA+BA,QAJ/B,IAKA,6BAA6BA,QAL7B,IAMA,wBAAwBA,QANxB,IAOA,mBAAmBA,QAPnB,IAQA,sBAAsBA,QARtB,IASA,uBAAuBA,QATvB,IAUA,oBAAoBA,QAVpB,IAWA,2BAA2BA,QAX3B,IAYA,uBAAuBA,QAZvB,IAaA,wBAAwBA,QAbxB,IAcA,sBAAsBA,QAdtB,IAeA,+BAA+BA,QAf/B,IAgBA,kCAAkCA,QAhBlC,IAiBA,2BAA2BA,QAjB3B,IAkBA,iBAAiBA,QAlBjB,IAmBA,gBAAgBA,QAnBhB,IAoBA,sBAAsBA,QApBtB,IAqBA,wBAAwBA,QArBxB,IAsBA,6BAA6BA,QAtB7B,IAuBA,6BAA6BA,QAvB7B,IAwBA,wBAAwBA,QAxBxB,IAyBA,0BAA0BA,QAzB1B,IA0BCA,QAAQ,KAAK,aAAb,IACC,kBAAmBF,IAAD,CAAwB6P,YA5B9C,EA6BE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+Q,aAAT,CACLhR,IADK,EAELC,IAFK,EAGkB;EACvB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,iBAAiBD,QAAjB,IACA,kBAAkBA,QADlB,IAEA,wBAAwBA,QAFxB,IAGA,mBAAmBA,QAHnB,IAIA,oBAAoBA,QAJpB,IAKA,qBAAqBA,QALrB,IAMA,sBAAsBA,QANtB,IAOA,0BAA0BA,QAP1B,IAQCA,QAAQ,KAAK,aAAb,KACE,cAAeF,IAAD,CAAwB6P,YAAtC,IACC,iBAAkB7P,IAAD,CAAwB6P,YAF5C,CATH,EAYE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgR,MAAT,CACLjR,IADK,EAELC,IAFK,EAGW;EAChB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,iBAAiBD,QAAjB,IACA,uBAAuBA,QADvB,IAEA,kBAAkBA,QAFlB,IAGA,wBAAwBA,QAHxB,IAIA,mBAAmBA,QAJnB,IAKA,oBAAoBA,QALpB,IAMA,0BAA0BA,QAN1B,IAOA,qBAAqBA,QAPrB,IAQA,sBAAsBA,QARtB,IASA,0BAA0BA,QAT1B,IAUCA,QAAQ,KAAK,aAAb,KACE,cAAeF,IAAD,CAAwB6P,YAAtC,IACC,iBAAkB7P,IAAD,CAAwB6P,YAF5C,CAXH,EAcE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiR,cAAT,CACLlR,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,iBAAiBD,QAAjB,IACA,sBAAsBA,QADtB,IAECA,QAAQ,KAAK,aAAb,IACC,iBAAkBF,IAAD,CAAwB6P,YAJ7C,EAKE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkR,SAAT,CACLnR,IADK,EAELC,IAFK,EAGc;EACnB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,oBAAoBD,QAApB,IACA,qBAAqBA,QADrB,IAEA,kBAAkBA,QAFlB,IAGA,qBAAqBA,QAHrB,IAIA,oBAAoBA,QAJpB,IAKA,sBAAsBA,QALtB,IAMA,oBAAoBA,QANpB,IAOA,qBAAqBA,QAPrB,IAQCA,QAAQ,KAAK,aAAb,IACC,oBAAqBF,IAAD,CAAwB6P,YAVhD,EAWE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmR,WAAT,CACLpR,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,oBAAoBD,QAApB,IACA,qBAAqBA,QADrB,IAEA,kBAAkBA,QAFlB,IAGA,qBAAqBA,QAHrB,IAIA,oBAAoBA,QAJpB,IAKA,mBAAmBA,QALnB,IAMA,wBAAwBA,QANxB,IAOA,iBAAiBA,QAPjB,IAQA,6BAA6BA,QAR7B,IASA,qBAAqBA,QATrB,IAUA,wBAAwBA,QAVxB,IAWA,cAAcA,QAXd,IAYA,kBAAkBA,QAZlB,IAaA,yBAAyBA,QAbzB,IAcA,yBAAyBA,QAdzB,IAeA,qBAAqBA,QAfrB,IAgBCA,QAAQ,KAAK,aAAb,IACC,oBAAqBF,IAAD,CAAwB6P,YAlBhD,EAmBE;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoR,mBAAT,CACLrR,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,mBAAmBD,QAAnB,IACA,qBAAqBA,QADrB,IAEA,6BAA6BA,QAF7B,IAGA,6BAA6BA,QAH7B,IAIA,wBAAwBA,QAJxB,IAKA,yBAAyBA,QALzB,IAMA,+BAA+BA,QAPjC,EAQE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqR,QAAT,CACLtR,IADK,EAELC,IAFK,EAGa;EAClB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,mBAAmBD,QAAnB,IACA,kBAAkBA,QADlB,IAEA,yBAAyBA,QAH3B,EAIE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsR,cAAT,CACLvR,IADK,EAELC,IAFK,EAGmB;EACxB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,mBAAmBD,QAAnB,IAA+B,qBAAqBA,QAAxD,EAAkE;IAChE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuR,UAAT,CACLxR,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,qBAAqBD,QAArB,IACA,oBAAoBA,QADpB,IAEA,4BAA4BA,QAF5B,IAGA,2BAA2BA,QAJ7B,EAKE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwR,WAAT,CACLzR,IADK,EAELC,IAFK,EAGgB;EACrB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,sBAAsBD,QAAtB,IAAkC,oBAAoBA,QAA1D,EAAoE;IAClE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyR,SAAT,CACL1R,IADK,EAELC,IAFK,EAGc;EACnB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,wBAAwBD,QAAxB,IACA,mBAAmBA,QADnB,IAEA,oBAAoBA,QAFpB,IAGCA,QAAQ,KAAK,aAAb,IACC,cAAeF,IAAD,CAAwB6P,YAL1C,EAME;IACA,IAAI,OAAO5P,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0R,OAAT,CACL3R,IADK,EAELC,IAFK,EAGY;EACjB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,sBAAsBD,QAAtB,IAAkC,uBAAuBA,QAA7D,EAAuE;IACrE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2R,mBAAT,CACL5R,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,2BAA2BD,QAA3B,IACA,+BAA+BA,QAD/B,IAEA,6BAA6BA,QAF7B,IAGA,wBAAwBA,QAJ1B,EAKE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4R,mBAAT,CACL7R,IADK,EAELC,IAFK,EAGwB;EAC7B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,2BAA2BD,QAA3B,IACA,+BAA+BA,QAD/B,IAEA,6BAA6BA,QAH/B,EAIE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6R,iBAAT,CACL9R,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,sBAAsBD,QAAtB,IACA,6BAA6BA,QAD7B,IAEA,+BAA+BA,QAF/B,IAGA,sBAAsBA,QAHtB,IAIA,+BAA+BA,QAJ/B,IAKA,6BAA6BA,QAN/B,EAOE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS8R,UAAT,CACL/R,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,4BAA4BD,QAAhC,EAA0C;IACxC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS+R,SAAT,CACLhS,IADK,EAELC,IAFK,EAGc;EACnB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,2BAA2BD,QAA3B,IACA,yBAAyBA,QADzB,IAEA,kBAAkBA,QAHpB,EAIE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgS,MAAT,CACLjS,IADK,EAELC,IAFK,EAGW;EAChB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,wBAAwBD,QAAxB,IACA,0BAA0BA,QAD1B,IAEA,4BAA4BA,QAF5B,IAGA,mCAAmCA,QAHnC,IAIA,gCAAgCA,QAJhC,IAKA,sBAAsBA,QALtB,IAMA,mBAAmBA,QANnB,IAOA,sBAAsBA,QAPtB,IAQA,uBAAuBA,QARvB,IASA,oBAAoBA,QATpB,IAUA,2BAA2BA,QAV3B,IAWA,uBAAuBA,QAXvB,IAYA,wBAAwBA,QAZxB,IAaA,sBAAsBA,QAbtB,IAcA,+BAA+BA,QAd/B,IAeA,kCAAkCA,QAflC,IAgBA,wBAAwBA,QAhBxB,IAiBA,2BAA2BA,QAjB3B,IAkBA,6BAA6BA,QAlB7B,IAmBA,wBAAwBA,QAnBxB,IAoBA,4BAA4BA,QApB5B,IAqBA,wBAAwBA,QArBxB,IAsBA,uBAAuBA,QAtBvB,IAuBA,2BAA2BA,QAvB3B,IAwBA,8BAA8BA,QAxB9B,IAyBA,iCAAiCA,QAzBjC,IA0BA,0BAA0BA,QA1B1B,IA2BA,0BAA0BA,QA3B1B,IA4BA,6BAA6BA,QA5B7B,IA6BA,kCAAkCA,QA7BlC,IA8BA,2BAA2BA,QA9B3B,IA+BA,2BAA2BA,QA/B3B,IAgCA,6BAA6BA,QAhC7B,IAiCA,6BAA6BA,QAjC7B,IAkCA,wBAAwBA,QAlCxB,IAmCA,yBAAyBA,QAnCzB,IAoCA,+BAA+BA,QApC/B,IAqCA,iBAAiBA,QArCjB,IAsCA,8BAA8BA,QAtC9B,IAuCA,kCAAkCA,QAvClC,IAwCA,2BAA2BA,QAxC3B,IAyCA,2BAA2BA,QAzC3B,IA0CA,yBAAyBA,QA1CzB,IA2CA,0BAA0BA,QA3C1B,IA4CA,2BAA2BA,QA5C3B,IA6CA,gBAAgBA,QA7ChB,IA8CA,qBAAqBA,QA9CrB,IA+CA,yBAAyBA,QA/CzB,IAgDA,oBAAoBA,QAhDpB,IAiDA,+BAA+BA,QAjD/B,IAkDA,iCAAiCA,QAlDjC,IAmDA,0BAA0BA,QAnD1B,IAoDA,eAAeA,QApDf,IAqDA,yBAAyBA,QArDzB,IAsDA,sBAAsBA,QAtDtB,IAuDA,sBAAsBA,QAvDtB,IAwDA,qBAAqBA,QAxDrB,IAyDA,qBAAqBA,QAzDrB,IA0DA,qBAAqBA,QA1DrB,IA2DA,wBAAwBA,QA3DxB,IA4DA,uBAAuBA,QA5DvB,IA6DA,uBAAuBA,QA7DvB,IA8DA,0BAA0BA,QA9D1B,IA+DA,wBAAwBA,QA/DxB,IAgEA,gCAAgCA,QAjElC,EAkEE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiS,UAAT,CACLlS,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,wBAAwBD,QAAxB,IACA,0BAA0BA,QAD1B,IAEA,4BAA4BA,QAF5B,IAGA,mCAAmCA,QAHnC,IAIA,gCAAgCA,QAJhC,IAKA,2BAA2BA,QAL3B,IAMA,6BAA6BA,QAN7B,IAOA,4BAA4BA,QAP5B,IAQA,8BAA8BA,QAR9B,IASA,iCAAiCA,QATjC,IAUA,0BAA0BA,QAV1B,IAWA,0BAA0BA,QAX1B,IAYA,6BAA6BA,QAZ7B,IAaA,kCAAkCA,QAblC,IAcA,2BAA2BA,QAd3B,IAeA,2BAA2BA,QAf3B,IAgBA,kCAAkCA,QAhBlC,IAiBA,2BAA2BA,QAjB3B,IAkBA,2BAA2BA,QAlB3B,IAmBA,yBAAyBA,QAnBzB,IAoBA,0BAA0BA,QApB1B,IAqBA,2BAA2BA,QArB3B,IAsBA,0BAA0BA,QAtB1B,IAuBA,yBAAyBA,QAvBzB,IAwBA,wBAAwBA,QAxBxB,IAyBA,gCAAgCA,QA1BlC,EA2BE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkS,oBAAT,CACLnS,IADK,EAELC,IAFK,EAGyB;EAC9B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,wBAAwBD,QAAxB,IACA,4BAA4BA,QAD5B,IAEA,gCAAgCA,QAFhC,IAGA,0BAA0BA,QAH1B,IAIA,0BAA0BA,QAJ1B,IAKA,2BAA2BA,QAL3B,IAMA,2BAA2BA,QAN3B,IAOA,2BAA2BA,QAP3B,IAQA,yBAAyBA,QARzB,IASA,yBAAyBA,QAV3B,EAWE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASmS,iBAAT,CACLpS,IADK,EAELC,IAFK,EAGsB;EAC3B,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,mBAAmBD,QAAnB,IACA,sBAAsBA,QADtB,IAEA,uBAAuBA,QAFvB,IAGA,oBAAoBA,QAHpB,IAIA,2BAA2BA,QAJ3B,IAKA,uBAAuBA,QALvB,IAMA,wBAAwBA,QANxB,IAOA,sBAAsBA,QAPtB,IAQA,+BAA+BA,QAR/B,IASA,kCAAkCA,QATlC,IAUA,2BAA2BA,QAV3B,IAWA,iBAAiBA,QAXjB,IAYA,gBAAgBA,QAblB,EAcE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASoS,eAAT,CACLrS,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAI,wBAAwBD,QAAxB,IAAoC,wBAAwBA,QAAhE,EAA0E;IACxE,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASqS,UAAT,CACLtS,IADK,EAELC,IAFK,EAGe;EACpB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,sBAAsBD,QAAtB,IACA,qBAAqBA,QADrB,IAEA,qBAAqBA,QAFrB,IAGA,qBAAqBA,QAJvB,EAKE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASsS,YAAT,CACLvS,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,wBAAwBD,QAAxB,IACA,uBAAuBA,QADvB,IAEA,uBAAuBA,QAFvB,IAGA,0BAA0BA,QAJ5B,EAKE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASuS,KAAT,CACLxS,IADK,EAELC,IAFK,EAGU;EACf,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,mBAAmBD,QAAnB,IACA,wBAAwBA,QADxB,IAEA,iBAAiBA,QAFjB,IAGA,yBAAyBA,QAHzB,IAIA,6BAA6BA,QAJ7B,IAKA,qBAAqBA,QALrB,IAMA,oBAAoBA,QANpB,IAOA,0BAA0BA,QAP1B,IAQA,wBAAwBA,QARxB,IASA,wBAAwBA,QATxB,IAUA,yBAAyBA,QAVzB,IAWA,cAAcA,QAXd,IAYA,kBAAkBA,QAZlB,IAaA,yBAAyBA,QAbzB,IAcA,yBAAyBA,QAf3B,EAgBE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASwS,eAAT,CACLzS,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,WAAWD,QAAX,IACA,kBAAkBA,QADlB,IAEA,4BAA4BA,QAH9B,EAIE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASyS,YAAT,CACL1S,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,0BAA0BD,QAA1B,IACA,wBAAwBA,QADxB,IAEA,sBAAsBA,QAFtB,IAGA,sBAAsBA,QAHtB,IAIA,iCAAiCA,QAJjC,IAKA,sCAAsCA,QALtC,IAMA,0BAA0BA,QAN1B,IAOA,wBAAwBA,QAPxB,IAQA,uBAAuBA,QARvB,IASA,mBAAmBA,QATnB,IAUA,uBAAuBA,QAVvB,IAWA,sBAAsBA,QAXtB,IAYA,yBAAyBA,QAZzB,IAaA,qBAAqBA,QAbrB,IAcA,oBAAoBA,QAdpB,IAeA,sBAAsBA,QAftB,IAgBA,sBAAsBA,QAhBtB,IAiBA,sBAAsBA,QAjBtB,IAkBA,sBAAsBA,QAlBtB,IAmBA,yBAAyBA,QAnBzB,IAoBA,uBAAuBA,QApBvB,IAqBA,oBAAoBA,QArBpB,IAsBA,iBAAiBA,QAtBjB,IAuBA,qBAAqBA,QAvBrB,IAwBA,wBAAwBA,QAxBxB,IAyBA,sBAAsBA,QAzBtB,IA0BA,sBAAsBA,QA1BtB,IA2BA,kBAAkBA,QA3BlB,IA4BA,oBAAoBA,QA5BpB,IA6BA,kBAAkBA,QA7BlB,IA8BA,kBAAkBA,QA9BlB,IA+BA,qBAAqBA,QA/BrB,IAgCA,iBAAiBA,QAhCjB,IAiCA,yBAAyBA,QAjCzB,IAkCA,kBAAkBA,QAlClB,IAmCA,yBAAyBA,QAnCzB,IAoCA,wBAAwBA,QApCxB,IAqCA,kBAAkBA,QArClB,IAsCA,0BAA0BA,QAtC1B,IAuCA,qBAAqBA,QAvCrB,IAwCA,0BAA0BA,QAxC1B,IAyCA,mBAAmBA,QAzCnB,IA0CA,oBAAoBA,QA1CpB,IA2CA,oCAAoCA,QA3CpC,IA4CA,6BAA6BA,QA5C7B,IA6CA,sBAAsBA,QA7CtB,IA8CA,6BAA6BA,QA9C7B,IA+CA,gCAAgCA,QA/ChC,IAgDA,qBAAqBA,QAhDrB,IAiDA,sBAAsBA,QAjDtB,IAkDA,wBAAwBA,QAlDxB,IAmDA,mBAAmBA,QAnDnB,IAoDA,0BAA0BA,QApD1B,IAqDA,oBAAoBA,QArDpB,IAsDA,mBAAmBA,QAtDnB,IAuDA,gCAAgCA,QAvDhC,IAwDA,gCAAgCA,QAxDhC,IAyDA,0BAA0BA,QAzD1B,IA0DA,yBAAyBA,QA1DzB,IA2DA,mCAAmCA,QA3DnC,IA4DA,uBAAuBA,QA5DvB,IA6DA,mCAAmCA,QA7DnC,IA8DA,iCAAiCA,QA9DjC,IA+DA,sBAAsBA,QAhExB,EAiEE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS0S,eAAT,CACL3S,IADK,EAELC,IAFK,EAGoB;EACzB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,iCAAiCD,QAAjC,IACA,sCAAsCA,QADtC,IAEA,0BAA0BA,QAF1B,IAGA,wBAAwBA,QAHxB,IAIA,uBAAuBA,QALzB,EAME;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS2S,QAAT,CACL5S,IADK,EAELC,IAFK,EAGa;EAClB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,mBAAmBD,QAAnB,IACA,uBAAuBA,QADvB,IAEA,sBAAsBA,QAFtB,IAGA,yBAAyBA,QAHzB,IAIA,qBAAqBA,QAJrB,IAKA,oBAAoBA,QALpB,IAMA,sBAAsBA,QANtB,IAOA,sBAAsBA,QAPtB,IAQA,sBAAsBA,QARtB,IASA,sBAAsBA,QATtB,IAUA,yBAAyBA,QAVzB,IAWA,uBAAuBA,QAXvB,IAYA,oBAAoBA,QAZpB,IAaA,iBAAiBA,QAbjB,IAcA,qBAAqBA,QAdrB,IAeA,wBAAwBA,QAfxB,IAgBA,sBAAsBA,QAhBtB,IAiBA,sBAAsBA,QAjBtB,IAkBA,kBAAkBA,QAlBlB,IAmBA,oBAAoBA,QAnBpB,IAoBA,kBAAkBA,QApBlB,IAqBA,kBAAkBA,QArBlB,IAsBA,qBAAqBA,QAtBrB,IAuBA,iBAAiBA,QAvBjB,IAwBA,kBAAkBA,QAxBlB,IAyBA,yBAAyBA,QAzBzB,IA0BA,wBAAwBA,QA1BxB,IA2BA,kBAAkBA,QA3BlB,IA4BA,0BAA0BA,QA5B1B,IA6BA,qBAAqBA,QA7BrB,IA8BA,0BAA0BA,QA9B1B,IA+BA,mBAAmBA,QA/BnB,IAgCA,oBAAoBA,QAhCpB,IAiCA,oCAAoCA,QAjCpC,IAkCA,mBAAmBA,QAnCrB,EAoCE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS4S,YAAT,CACL7S,IADK,EAELC,IAFK,EAGiB;EACtB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IACE,mBAAmBD,QAAnB,IACA,uBAAuBA,QADvB,IAEA,sBAAsBA,QAFtB,IAGA,yBAAyBA,QAHzB,IAIA,qBAAqBA,QAJrB,IAKA,oBAAoBA,QALpB,IAMA,sBAAsBA,QANtB,IAOA,sBAAsBA,QAPtB,IAQA,sBAAsBA,QARtB,IASA,sBAAsBA,QATtB,IAUA,yBAAyBA,QAVzB,IAWA,uBAAuBA,QAXvB,IAYA,oBAAoBA,QAZpB,IAaA,iBAAiBA,QAbjB,IAcA,oBAAoBA,QAftB,EAgBE;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAAS6S,eAAT,CACL9S,IADK,EAELC,IAFK,EAGI;EACT8S,OAAO,CAACC,KAAR,CACE,gEADF;EAGA,IAAI,CAAChT,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,eAAjB,EAAkC;IAChC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASgT,cAAT,CACLjT,IADK,EAELC,IAFK,EAGI;EACT8S,OAAO,CAACC,KAAR,CAAc,8DAAd;EACA,IAAI,CAAChT,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASiT,cAAT,CACLlT,IADK,EAELC,IAFK,EAGI;EACT8S,OAAO,CAACC,KAAR,CAAc,4DAAd;EACA,IAAI,CAAChT,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,cAAjB,EAAiC;IAC/B,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD;;AACM,SAASkT,gBAAT,CACLnT,IADK,EAELC,IAFK,EAGI;EACT8S,OAAO,CAACC,KAAR,CACE,gEADF;EAGA,IAAI,CAAChT,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,QAAQ,GAAIF,IAAD,CAAiBG,IAAlC;;EACA,IAAID,QAAQ,KAAK,gBAAjB,EAAmC;IACjC,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;MAC/B,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,IAAAG,qBAAA,EAAaJ,IAAb,EAAmBC,IAAnB,CAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD"}