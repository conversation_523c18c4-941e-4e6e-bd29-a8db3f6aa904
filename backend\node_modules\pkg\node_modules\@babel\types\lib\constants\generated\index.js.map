{"version": 3, "names": ["STANDARDIZED_TYPES", "FLIPPED_ALIAS_KEYS", "EXPRESSION_TYPES", "BINARY_TYPES", "SCOPABLE_TYPES", "BLOCKPARENT_TYPES", "BLOCK_TYPES", "STATEMENT_TYPES", "TERMINATORLESS_TYPES", "COMPLETIONSTATEMENT_TYPES", "CONDITIONAL_TYPES", "LOOP_TYPES", "WHILE_TYPES", "EXPRESSIONWRAPPER_TYPES", "FOR_TYPES", "FORXSTATEMENT_TYPES", "FUNCTION_TYPES", "FUNCTIONPARENT_TYPES", "PUREISH_TYPES", "DECLARATION_TYPES", "PATTERNLIKE_TYPES", "LVAL_TYPES", "TSENTITYNAME_TYPES", "LITERAL_TYPES", "IMMUTABLE_TYPES", "USERWHITESPACABLE_TYPES", "METHOD_TYPES", "OBJECTMEMBER_TYPES", "PROPERTY_TYPES", "UNARYLIKE_TYPES", "PATTERN_TYPES", "CLASS_TYPES", "MODULEDECLARATION_TYPES", "EXPORTDECLARATION_TYPES", "MODULESPECIFIER_TYPES", "ACCESSOR_TYPES", "PRIVATE_TYPES", "FLOW_TYPES", "FLOWTYPE_TYPES", "FLOWBASEANNOTATION_TYPES", "FLOWDECLARATION_TYPES", "FLOWPREDICATE_TYPES", "ENUMBODY_TYPES", "ENUMMEMBER_TYPES", "JSX_TYPES", "MISCELLANEOUS_TYPES", "TYPESCRIPT_TYPES", "TSTYPEELEMENT_TYPES", "TSTYPE_TYPES", "TSBASETYPE_TYPES"], "sources": ["../../../src/constants/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport { FLIPPED_ALIAS_KEYS } from \"../../definitions\";\n\nexport const STANDARDIZED_TYPES = FLIPPED_ALIAS_KEYS[\"Standardized\"];\nexport const EXPRESSION_TYPES = FLIPPED_ALIAS_KEYS[\"Expression\"];\nexport const BINARY_TYPES = FLIPPED_ALIAS_KEYS[\"Binary\"];\nexport const SCOPABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Scopable\"];\nexport const BLOCKPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"BlockParent\"];\nexport const BLOCK_TYPES = FLIPPED_ALIAS_KEYS[\"Block\"];\nexport const STATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"Statement\"];\nexport const TERMINATORLESS_TYPES = FLIPPED_ALIAS_KEYS[\"Terminatorless\"];\nexport const COMPLETIONSTATEMENT_TYPES =\n  FLIPPED_ALIAS_KEYS[\"CompletionStatement\"];\nexport const CONDITIONAL_TYPES = FLIPPED_ALIAS_KEYS[\"Conditional\"];\nexport const LOOP_TYPES = FLIPPED_ALIAS_KEYS[\"Loop\"];\nexport const WHILE_TYPES = FLIPPED_ALIAS_KEYS[\"While\"];\nexport const EXPRESSIONWRAPPER_TYPES = FLIPPED_ALIAS_KEYS[\"ExpressionWrapper\"];\nexport const FOR_TYPES = FLIPPED_ALIAS_KEYS[\"For\"];\nexport const FORXSTATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"ForXStatement\"];\nexport const FUNCTION_TYPES = FLIPPED_ALIAS_KEYS[\"Function\"];\nexport const FUNCTIONPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"FunctionParent\"];\nexport const PUREISH_TYPES = FLIPPED_ALIAS_KEYS[\"Pureish\"];\nexport const DECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"Declaration\"];\nexport const PATTERNLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"PatternLike\"];\nexport const LVAL_TYPES = FLIPPED_ALIAS_KEYS[\"LVal\"];\nexport const TSENTITYNAME_TYPES = FLIPPED_ALIAS_KEYS[\"TSEntityName\"];\nexport const LITERAL_TYPES = FLIPPED_ALIAS_KEYS[\"Literal\"];\nexport const IMMUTABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Immutable\"];\nexport const USERWHITESPACABLE_TYPES = FLIPPED_ALIAS_KEYS[\"UserWhitespacable\"];\nexport const METHOD_TYPES = FLIPPED_ALIAS_KEYS[\"Method\"];\nexport const OBJECTMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"ObjectMember\"];\nexport const PROPERTY_TYPES = FLIPPED_ALIAS_KEYS[\"Property\"];\nexport const UNARYLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"UnaryLike\"];\nexport const PATTERN_TYPES = FLIPPED_ALIAS_KEYS[\"Pattern\"];\nexport const CLASS_TYPES = FLIPPED_ALIAS_KEYS[\"Class\"];\nexport const MODULEDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"ModuleDeclaration\"];\nexport const EXPORTDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"ExportDeclaration\"];\nexport const MODULESPECIFIER_TYPES = FLIPPED_ALIAS_KEYS[\"ModuleSpecifier\"];\nexport const ACCESSOR_TYPES = FLIPPED_ALIAS_KEYS[\"Accessor\"];\nexport const PRIVATE_TYPES = FLIPPED_ALIAS_KEYS[\"Private\"];\nexport const FLOW_TYPES = FLIPPED_ALIAS_KEYS[\"Flow\"];\nexport const FLOWTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowType\"];\nexport const FLOWBASEANNOTATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"FlowBaseAnnotation\"];\nexport const FLOWDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"FlowDeclaration\"];\nexport const FLOWPREDICATE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowPredicate\"];\nexport const ENUMBODY_TYPES = FLIPPED_ALIAS_KEYS[\"EnumBody\"];\nexport const ENUMMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"EnumMember\"];\nexport const JSX_TYPES = FLIPPED_ALIAS_KEYS[\"JSX\"];\nexport const MISCELLANEOUS_TYPES = FLIPPED_ALIAS_KEYS[\"Miscellaneous\"];\nexport const TYPESCRIPT_TYPES = FLIPPED_ALIAS_KEYS[\"TypeScript\"];\nexport const TSTYPEELEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"TSTypeElement\"];\nexport const TSTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSType\"];\nexport const TSBASETYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSBaseType\"];\n"], "mappings": ";;;;;;;AAIA;;AAEO,MAAMA,kBAAkB,GAAGC,+BAAA,CAAmB,cAAnB,CAA3B;;AACA,MAAMC,gBAAgB,GAAGD,+BAAA,CAAmB,YAAnB,CAAzB;;AACA,MAAME,YAAY,GAAGF,+BAAA,CAAmB,QAAnB,CAArB;;AACA,MAAMG,cAAc,GAAGH,+BAAA,CAAmB,UAAnB,CAAvB;;AACA,MAAMI,iBAAiB,GAAGJ,+BAAA,CAAmB,aAAnB,CAA1B;;AACA,MAAMK,WAAW,GAAGL,+BAAA,CAAmB,OAAnB,CAApB;;AACA,MAAMM,eAAe,GAAGN,+BAAA,CAAmB,WAAnB,CAAxB;;AACA,MAAMO,oBAAoB,GAAGP,+BAAA,CAAmB,gBAAnB,CAA7B;;AACA,MAAMQ,yBAAyB,GACpCR,+BAAA,CAAmB,qBAAnB,CADK;;AAEA,MAAMS,iBAAiB,GAAGT,+BAAA,CAAmB,aAAnB,CAA1B;;AACA,MAAMU,UAAU,GAAGV,+BAAA,CAAmB,MAAnB,CAAnB;;AACA,MAAMW,WAAW,GAAGX,+BAAA,CAAmB,OAAnB,CAApB;;AACA,MAAMY,uBAAuB,GAAGZ,+BAAA,CAAmB,mBAAnB,CAAhC;;AACA,MAAMa,SAAS,GAAGb,+BAAA,CAAmB,KAAnB,CAAlB;;AACA,MAAMc,mBAAmB,GAAGd,+BAAA,CAAmB,eAAnB,CAA5B;;AACA,MAAMe,cAAc,GAAGf,+BAAA,CAAmB,UAAnB,CAAvB;;AACA,MAAMgB,oBAAoB,GAAGhB,+BAAA,CAAmB,gBAAnB,CAA7B;;AACA,MAAMiB,aAAa,GAAGjB,+BAAA,CAAmB,SAAnB,CAAtB;;AACA,MAAMkB,iBAAiB,GAAGlB,+BAAA,CAAmB,aAAnB,CAA1B;;AACA,MAAMmB,iBAAiB,GAAGnB,+BAAA,CAAmB,aAAnB,CAA1B;;AACA,MAAMoB,UAAU,GAAGpB,+BAAA,CAAmB,MAAnB,CAAnB;;AACA,MAAMqB,kBAAkB,GAAGrB,+BAAA,CAAmB,cAAnB,CAA3B;;AACA,MAAMsB,aAAa,GAAGtB,+BAAA,CAAmB,SAAnB,CAAtB;;AACA,MAAMuB,eAAe,GAAGvB,+BAAA,CAAmB,WAAnB,CAAxB;;AACA,MAAMwB,uBAAuB,GAAGxB,+BAAA,CAAmB,mBAAnB,CAAhC;;AACA,MAAMyB,YAAY,GAAGzB,+BAAA,CAAmB,QAAnB,CAArB;;AACA,MAAM0B,kBAAkB,GAAG1B,+BAAA,CAAmB,cAAnB,CAA3B;;AACA,MAAM2B,cAAc,GAAG3B,+BAAA,CAAmB,UAAnB,CAAvB;;AACA,MAAM4B,eAAe,GAAG5B,+BAAA,CAAmB,WAAnB,CAAxB;;AACA,MAAM6B,aAAa,GAAG7B,+BAAA,CAAmB,SAAnB,CAAtB;;AACA,MAAM8B,WAAW,GAAG9B,+BAAA,CAAmB,OAAnB,CAApB;;AACA,MAAM+B,uBAAuB,GAAG/B,+BAAA,CAAmB,mBAAnB,CAAhC;;AACA,MAAMgC,uBAAuB,GAAGhC,+BAAA,CAAmB,mBAAnB,CAAhC;;AACA,MAAMiC,qBAAqB,GAAGjC,+BAAA,CAAmB,iBAAnB,CAA9B;;AACA,MAAMkC,cAAc,GAAGlC,+BAAA,CAAmB,UAAnB,CAAvB;;AACA,MAAMmC,aAAa,GAAGnC,+BAAA,CAAmB,SAAnB,CAAtB;;AACA,MAAMoC,UAAU,GAAGpC,+BAAA,CAAmB,MAAnB,CAAnB;;AACA,MAAMqC,cAAc,GAAGrC,+BAAA,CAAmB,UAAnB,CAAvB;;AACA,MAAMsC,wBAAwB,GACnCtC,+BAAA,CAAmB,oBAAnB,CADK;;AAEA,MAAMuC,qBAAqB,GAAGvC,+BAAA,CAAmB,iBAAnB,CAA9B;;AACA,MAAMwC,mBAAmB,GAAGxC,+BAAA,CAAmB,eAAnB,CAA5B;;AACA,MAAMyC,cAAc,GAAGzC,+BAAA,CAAmB,UAAnB,CAAvB;;AACA,MAAM0C,gBAAgB,GAAG1C,+BAAA,CAAmB,YAAnB,CAAzB;;AACA,MAAM2C,SAAS,GAAG3C,+BAAA,CAAmB,KAAnB,CAAlB;;AACA,MAAM4C,mBAAmB,GAAG5C,+BAAA,CAAmB,eAAnB,CAA5B;;AACA,MAAM6C,gBAAgB,GAAG7C,+BAAA,CAAmB,YAAnB,CAAzB;;AACA,MAAM8C,mBAAmB,GAAG9C,+BAAA,CAAmB,eAAnB,CAA5B;;AACA,MAAM+C,YAAY,GAAG/C,+BAAA,CAAmB,QAAnB,CAArB;;AACA,MAAMgD,gBAAgB,GAAGhD,+BAAA,CAAmB,YAAnB,CAAzB"}