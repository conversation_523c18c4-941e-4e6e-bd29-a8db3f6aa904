{"version": 3, "names": ["has", "Function", "call", "bind", "Object", "prototype", "hasOwnProperty", "cloneIfNode", "obj", "deep", "withoutLoc", "commentsCache", "type", "cloneNodeInternal", "cloneIfNodeOrArray", "Array", "isArray", "map", "node", "cloneNode", "Map", "newNode", "isIdentifier", "name", "optional", "typeAnnotation", "NODE_FIELDS", "Error", "field", "keys", "isFile", "maybeCloneComments", "comments", "loc", "leadingComments", "innerComments", "trailingComments", "extra", "comment", "cache", "get", "value", "ret", "set"], "sources": ["../../src/clone/cloneNode.ts"], "sourcesContent": ["import { NODE_FIELDS } from \"../definitions\";\nimport type * as t from \"..\";\nimport { isFile, isIdentifier } from \"../validators/generated\";\n\nconst has = Function.call.bind(Object.prototype.hasOwnProperty);\n\ntype CommentCache = Map<t.Comment, t.Comment>;\n\n// This function will never be called for comments, only for real nodes.\nfunction cloneIfNode(\n  obj: t.Node | undefined | null,\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: CommentCache,\n) {\n  if (obj && typeof obj.type === \"string\") {\n    return cloneNodeInternal(obj, deep, withoutLoc, commentsCache);\n  }\n\n  return obj;\n}\n\nfunction cloneIfNodeOrArray(\n  obj: t.Node | undefined | null | (t.Node | undefined | null)[],\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: CommentCache,\n) {\n  if (Array.isArray(obj)) {\n    return obj.map(node => cloneIfNode(node, deep, withoutLoc, commentsCache));\n  }\n  return cloneIfNode(obj, deep, withoutLoc, commentsCache);\n}\n\n/**\n * Create a clone of a `node` including only properties belonging to the node.\n * If the second parameter is `false`, cloneNode performs a shallow clone.\n * If the third parameter is true, the cloned nodes exclude location properties.\n */\nexport default function cloneNode<T extends t.Node>(\n  node: T,\n  deep: boolean = true,\n  withoutLoc: boolean = false,\n): T {\n  return cloneNodeInternal(node, deep, withoutLoc, new Map());\n}\n\nfunction cloneNodeInternal<T extends t.Node>(\n  node: T,\n  deep: boolean = true,\n  withoutLoc: boolean = false,\n  commentsCache: CommentCache,\n): T {\n  if (!node) return node;\n\n  const { type } = node;\n  const newNode: any = { type: node.type };\n\n  // Special-case identifiers since they are the most cloned nodes.\n  if (isIdentifier(node)) {\n    newNode.name = node.name;\n\n    if (has(node, \"optional\") && typeof node.optional === \"boolean\") {\n      newNode.optional = node.optional;\n    }\n\n    if (has(node, \"typeAnnotation\")) {\n      newNode.typeAnnotation = deep\n        ? cloneIfNodeOrArray(\n            node.typeAnnotation,\n            true,\n            withoutLoc,\n            commentsCache,\n          )\n        : node.typeAnnotation;\n    }\n  } else if (!has(NODE_FIELDS, type)) {\n    throw new Error(`Unknown node type: \"${type}\"`);\n  } else {\n    for (const field of Object.keys(NODE_FIELDS[type])) {\n      if (has(node, field)) {\n        if (deep) {\n          newNode[field] =\n            isFile(node) && field === \"comments\"\n              ? maybeCloneComments(\n                  node.comments,\n                  deep,\n                  withoutLoc,\n                  commentsCache,\n                )\n              : cloneIfNodeOrArray(\n                  // @ts-expect-error node[field] has been guarded by has check\n                  node[field],\n                  true,\n                  withoutLoc,\n                  commentsCache,\n                );\n        } else {\n          newNode[field] =\n            // @ts-expect-error node[field] has been guarded by has check\n            node[field];\n        }\n      }\n    }\n  }\n\n  if (has(node, \"loc\")) {\n    if (withoutLoc) {\n      newNode.loc = null;\n    } else {\n      newNode.loc = node.loc;\n    }\n  }\n  if (has(node, \"leadingComments\")) {\n    newNode.leadingComments = maybeCloneComments(\n      node.leadingComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (has(node, \"innerComments\")) {\n    newNode.innerComments = maybeCloneComments(\n      node.innerComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (has(node, \"trailingComments\")) {\n    newNode.trailingComments = maybeCloneComments(\n      node.trailingComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (has(node, \"extra\")) {\n    newNode.extra = {\n      ...node.extra,\n    };\n  }\n\n  return newNode;\n}\n\nfunction maybeCloneComments<T extends t.Comment>(\n  comments: ReadonlyArray<T> | null,\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: Map<T, T>,\n): ReadonlyArray<T> | null {\n  if (!comments || !deep) {\n    return comments;\n  }\n  return comments.map(comment => {\n    const cache = commentsCache.get(comment);\n    if (cache) return cache;\n\n    const { type, value, loc } = comment;\n\n    const ret = { type, value, loc } as T;\n    if (withoutLoc) {\n      ret.loc = null;\n    }\n\n    commentsCache.set(comment, ret);\n\n    return ret;\n  });\n}\n"], "mappings": ";;;;;;;AAAA;;AAEA;;AAEA,MAAMA,GAAG,GAAGC,QAAQ,CAACC,IAAT,CAAcC,IAAd,CAAmBC,MAAM,CAACC,SAAP,CAAiBC,cAApC,CAAZ;;AAKA,SAASC,WAAT,CACEC,GADF,EAEEC,IAFF,EAGEC,UAHF,EAIEC,aAJF,EAKE;EACA,IAAIH,GAAG,IAAI,OAAOA,GAAG,CAACI,IAAX,KAAoB,QAA/B,EAAyC;IACvC,OAAOC,iBAAiB,CAACL,GAAD,EAAMC,IAAN,EAAYC,UAAZ,EAAwBC,aAAxB,CAAxB;EACD;;EAED,OAAOH,GAAP;AACD;;AAED,SAASM,kBAAT,CACEN,GADF,EAEEC,IAFF,EAGEC,UAHF,EAIEC,aAJF,EAKE;EACA,IAAII,KAAK,CAACC,OAAN,CAAcR,GAAd,CAAJ,EAAwB;IACtB,OAAOA,GAAG,CAACS,GAAJ,CAAQC,IAAI,IAAIX,WAAW,CAACW,IAAD,EAAOT,IAAP,EAAaC,UAAb,EAAyBC,aAAzB,CAA3B,CAAP;EACD;;EACD,OAAOJ,WAAW,CAACC,GAAD,EAAMC,IAAN,EAAYC,UAAZ,EAAwBC,aAAxB,CAAlB;AACD;;AAOc,SAASQ,SAAT,CACbD,IADa,EAEbT,IAAa,GAAG,IAFH,EAGbC,UAAmB,GAAG,KAHT,EAIV;EACH,OAAOG,iBAAiB,CAACK,IAAD,EAAOT,IAAP,EAAaC,UAAb,EAAyB,IAAIU,GAAJ,EAAzB,CAAxB;AACD;;AAED,SAASP,iBAAT,CACEK,IADF,EAEET,IAAa,GAAG,IAFlB,EAGEC,UAAmB,GAAG,KAHxB,EAIEC,aAJF,EAKK;EACH,IAAI,CAACO,IAAL,EAAW,OAAOA,IAAP;EAEX,MAAM;IAAEN;EAAF,IAAWM,IAAjB;EACA,MAAMG,OAAY,GAAG;IAAET,IAAI,EAAEM,IAAI,CAACN;EAAb,CAArB;;EAGA,IAAI,IAAAU,uBAAA,EAAaJ,IAAb,CAAJ,EAAwB;IACtBG,OAAO,CAACE,IAAR,GAAeL,IAAI,CAACK,IAApB;;IAEA,IAAIvB,GAAG,CAACkB,IAAD,EAAO,UAAP,CAAH,IAAyB,OAAOA,IAAI,CAACM,QAAZ,KAAyB,SAAtD,EAAiE;MAC/DH,OAAO,CAACG,QAAR,GAAmBN,IAAI,CAACM,QAAxB;IACD;;IAED,IAAIxB,GAAG,CAACkB,IAAD,EAAO,gBAAP,CAAP,EAAiC;MAC/BG,OAAO,CAACI,cAAR,GAAyBhB,IAAI,GACzBK,kBAAkB,CAChBI,IAAI,CAACO,cADW,EAEhB,IAFgB,EAGhBf,UAHgB,EAIhBC,aAJgB,CADO,GAOzBO,IAAI,CAACO,cAPT;IAQD;EACF,CAjBD,MAiBO,IAAI,CAACzB,GAAG,CAAC0B,wBAAD,EAAcd,IAAd,CAAR,EAA6B;IAClC,MAAM,IAAIe,KAAJ,CAAW,uBAAsBf,IAAK,GAAtC,CAAN;EACD,CAFM,MAEA;IACL,KAAK,MAAMgB,KAAX,IAAoBxB,MAAM,CAACyB,IAAP,CAAYH,wBAAA,CAAYd,IAAZ,CAAZ,CAApB,EAAoD;MAClD,IAAIZ,GAAG,CAACkB,IAAD,EAAOU,KAAP,CAAP,EAAsB;QACpB,IAAInB,IAAJ,EAAU;UACRY,OAAO,CAACO,KAAD,CAAP,GACE,IAAAE,iBAAA,EAAOZ,IAAP,KAAgBU,KAAK,KAAK,UAA1B,GACIG,kBAAkB,CAChBb,IAAI,CAACc,QADW,EAEhBvB,IAFgB,EAGhBC,UAHgB,EAIhBC,aAJgB,CADtB,GAOIG,kBAAkB,CAEhBI,IAAI,CAACU,KAAD,CAFY,EAGhB,IAHgB,EAIhBlB,UAJgB,EAKhBC,aALgB,CARxB;QAeD,CAhBD,MAgBO;UACLU,OAAO,CAACO,KAAD,CAAP,GAEEV,IAAI,CAACU,KAAD,CAFN;QAGD;MACF;IACF;EACF;;EAED,IAAI5B,GAAG,CAACkB,IAAD,EAAO,KAAP,CAAP,EAAsB;IACpB,IAAIR,UAAJ,EAAgB;MACdW,OAAO,CAACY,GAAR,GAAc,IAAd;IACD,CAFD,MAEO;MACLZ,OAAO,CAACY,GAAR,GAAcf,IAAI,CAACe,GAAnB;IACD;EACF;;EACD,IAAIjC,GAAG,CAACkB,IAAD,EAAO,iBAAP,CAAP,EAAkC;IAChCG,OAAO,CAACa,eAAR,GAA0BH,kBAAkB,CAC1Cb,IAAI,CAACgB,eADqC,EAE1CzB,IAF0C,EAG1CC,UAH0C,EAI1CC,aAJ0C,CAA5C;EAMD;;EACD,IAAIX,GAAG,CAACkB,IAAD,EAAO,eAAP,CAAP,EAAgC;IAC9BG,OAAO,CAACc,aAAR,GAAwBJ,kBAAkB,CACxCb,IAAI,CAACiB,aADmC,EAExC1B,IAFwC,EAGxCC,UAHwC,EAIxCC,aAJwC,CAA1C;EAMD;;EACD,IAAIX,GAAG,CAACkB,IAAD,EAAO,kBAAP,CAAP,EAAmC;IACjCG,OAAO,CAACe,gBAAR,GAA2BL,kBAAkB,CAC3Cb,IAAI,CAACkB,gBADsC,EAE3C3B,IAF2C,EAG3CC,UAH2C,EAI3CC,aAJ2C,CAA7C;EAMD;;EACD,IAAIX,GAAG,CAACkB,IAAD,EAAO,OAAP,CAAP,EAAwB;IACtBG,OAAO,CAACgB,KAAR,qBACKnB,IAAI,CAACmB,KADV;EAGD;;EAED,OAAOhB,OAAP;AACD;;AAED,SAASU,kBAAT,CACEC,QADF,EAEEvB,IAFF,EAGEC,UAHF,EAIEC,aAJF,EAK2B;EACzB,IAAI,CAACqB,QAAD,IAAa,CAACvB,IAAlB,EAAwB;IACtB,OAAOuB,QAAP;EACD;;EACD,OAAOA,QAAQ,CAACf,GAAT,CAAaqB,OAAO,IAAI;IAC7B,MAAMC,KAAK,GAAG5B,aAAa,CAAC6B,GAAd,CAAkBF,OAAlB,CAAd;IACA,IAAIC,KAAJ,EAAW,OAAOA,KAAP;IAEX,MAAM;MAAE3B,IAAF;MAAQ6B,KAAR;MAAeR;IAAf,IAAuBK,OAA7B;IAEA,MAAMI,GAAG,GAAG;MAAE9B,IAAF;MAAQ6B,KAAR;MAAeR;IAAf,CAAZ;;IACA,IAAIvB,UAAJ,EAAgB;MACdgC,GAAG,CAACT,GAAJ,GAAU,IAAV;IACD;;IAEDtB,aAAa,CAACgC,GAAd,CAAkBL,OAAlB,EAA2BI,GAA3B;IAEA,OAAOA,GAAP;EACD,CAdM,CAAP;AAeD"}