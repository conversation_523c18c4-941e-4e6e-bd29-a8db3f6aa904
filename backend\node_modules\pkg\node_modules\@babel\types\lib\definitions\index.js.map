{"version": 3, "names": ["toFastProperties", "VISITOR_KEYS", "ALIAS_KEYS", "FLIPPED_ALIAS_KEYS", "NODE_FIELDS", "BUILDER_KEYS", "DEPRECATED_KEYS", "PLACEHOLDERS_ALIAS", "PLACEHOLDERS_FLIPPED_ALIAS", "TYPES", "concat", "Object", "keys"], "sources": ["../../src/definitions/index.ts"], "sourcesContent": ["import toFastProperties from \"to-fast-properties\";\nimport \"./core\";\nimport \"./flow\";\nimport \"./jsx\";\nimport \"./misc\";\nimport \"./experimental\";\nimport \"./typescript\";\nimport {\n  VISITOR_KEYS,\n  ALIAS_KEYS,\n  FLIPPED_ALIAS_KEYS,\n  NODE_FIELDS,\n  BUILDER_KEYS,\n  DEPRECATED_KEYS,\n  NODE_PARENT_VALIDATIONS,\n} from \"./utils\";\nimport {\n  PLACEHOLDERS,\n  PLACEHOLDERS_ALIAS,\n  PLACEHOLDERS_FLIPPED_ALIAS,\n} from \"./placeholders\";\n\n// We do this here, because at this point the visitor keys should be ready and setup\ntoFastProperties(VISITOR_KEYS);\ntoFastProperties(ALIAS_KEYS);\ntoFastProperties(FLIPPED_ALIAS_KEYS);\ntoFastProperties(NODE_FIELDS);\ntoFastProperties(BUILDER_KEYS);\ntoFastProperties(DEPRECATED_KEYS);\n\ntoFastProperties(PLACEHOLDERS_ALIAS);\ntoFastProperties(PLACEHOLDERS_FLIPPED_ALIAS);\n\nconst TYPES: Array<string> = [].concat(\n  Object.keys(VISITOR_KEYS),\n  Object.keys(FLIPPED_ALIAS_KEYS),\n  Object.keys(DEPRECATED_KEYS),\n);\n\nexport {\n  VISITOR_KEYS,\n  ALIAS_KEYS,\n  FLIPPED_ALIAS_KEYS,\n  NODE_FIELDS,\n  BUILDER_KEYS,\n  DEPRECATED_KEYS,\n  NODE_PARENT_VALIDATIONS,\n  PLACEHOLDERS,\n  PLACEHOLDERS_ALIAS,\n  PLACEHOLDERS_FLIPPED_ALIAS,\n  TYPES,\n};\n\nexport type { FieldOptions } from \"./utils\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AASA;;AAOAA,iBAAgB,CAACC,mBAAD,CAAhB;;AACAD,iBAAgB,CAACE,iBAAD,CAAhB;;AACAF,iBAAgB,CAACG,yBAAD,CAAhB;;AACAH,iBAAgB,CAACI,kBAAD,CAAhB;;AACAJ,iBAAgB,CAACK,mBAAD,CAAhB;;AACAL,iBAAgB,CAACM,sBAAD,CAAhB;;AAEAN,iBAAgB,CAACO,gCAAD,CAAhB;;AACAP,iBAAgB,CAACQ,wCAAD,CAAhB;;AAEA,MAAMC,KAAoB,GAAG,GAAGC,MAAH,CAC3BC,MAAM,CAACC,IAAP,CAAYX,mBAAZ,CAD2B,EAE3BU,MAAM,CAACC,IAAP,CAAYT,yBAAZ,CAF2B,EAG3BQ,MAAM,CAACC,IAAP,CAAYN,sBAAZ,CAH2B,CAA7B"}