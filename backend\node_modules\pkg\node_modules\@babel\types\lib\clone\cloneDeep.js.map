{"version": 3, "names": ["cloneDeep", "node", "cloneNode"], "sources": ["../../src/clone/cloneDeep.ts"], "sourcesContent": ["import cloneNode from \"./cloneNode\";\nimport type * as t from \"..\";\n\n/**\n * Create a deep clone of a `node` and all of it's child nodes\n * including only properties belonging to the node.\n * @deprecated Use t.cloneNode instead.\n */\nexport default function cloneDeep<T extends t.Node>(node: T): T {\n  return cloneNode(node);\n}\n"], "mappings": ";;;;;;;AAAA;;AAQe,SAASA,SAAT,CAAqCC,IAArC,EAAiD;EAC9D,OAAO,IAAAC,kBAAA,EAAUD,IAAV,CAAP;AACD"}