"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataService = void 0;
const DatabaseService_1 = require("./DatabaseService");
class DataService {
    // 初始化数据服务
    static async initialize() {
        DataService.db = DatabaseService_1.DatabaseService.getInstance();
        await DataService.db.initialize();
        // 检查是否需要插入默认数据
        await DataService.insertDefaultDataIfNeeded();
        console.log('[DataService] 数据服务初始化完成');
    }
    // 检查并插入默认数据
    static async insertDefaultDataIfNeeded() {
        const users = await DataService.db.getUsers();
        // 如果没有用户数据，插入默认数据
        if (users.length === 0) {
            console.log('[DataService] 插入默认数据...');
            // 默认用户
            const defaultUsers = [
                {
                    username: 'admin',
                    email: '<EMAIL>',
                    passwordHash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                    roles: ['admin'],
                    createdAt: '2024-01-01'
                },
                {
                    username: 'user1',
                    email: '<EMAIL>',
                    passwordHash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
                    roles: ['user'],
                    createdAt: '2024-01-02'
                },
                {
                    username: 'manager1',
                    email: '<EMAIL>',
                    passwordHash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
                    roles: ['manager'],
                    createdAt: '2024-01-03'
                },
                {
                    username: 'zansimple',
                    email: '<EMAIL>',
                    passwordHash: '$2b$10$2CdCCfuHetn4UR/.KGOF0e8LYx5eklgjv4raMcYoBbG0mGE4..Wjq', // zan724513
                    roles: ['user'],
                    createdAt: '2024-01-04'
                }
            ];
            for (const user of defaultUsers) {
                await DataService.db.createUser(user);
            }
            // 默认软件
            const defaultSoftware = [
                {
                    name: '示例软件A',
                    productKey: 'ABCD-EFGH-IJKL-MNOP',
                    description: '这是一款功能强大的数据处理软件，支持多种文件格式转换和批量处理功能。',
                    isActive: true,
                    createdAt: '2024-01-01',
                    price: 299.00,
                    trialDays: 7
                },
                {
                    name: '示例软件B',
                    productKey: 'QRST-UVWX-YZ12-3456',
                    description: '专业的图像编辑工具，提供丰富的滤镜和特效，适合设计师和摄影师使用。',
                    isActive: true,
                    createdAt: '2024-01-02',
                    price: 199.00,
                    trialDays: 14
                },
                {
                    name: '示例软件C',
                    productKey: '7890-ABCD-EFGH-IJKL',
                    description: '轻量级的文本编辑器，支持语法高亮和多标签页，适合程序员和作家使用。',
                    isActive: false,
                    createdAt: '2024-01-03',
                    price: 99.00,
                    trialDays: 30
                }
            ];
            for (const software of defaultSoftware) {
                await DataService.db.createSoftware(software);
            }
            // 默认许可证
            const defaultLicenses = [
                {
                    licenseKey: 'LIC-12345678-ABCD-EFGH',
                    softwareId: 1,
                    userId: 2,
                    activationType: 'offline',
                    activatedAt: '2024-01-15T10:30:00Z',
                    expiresAt: '2024-02-15T10:30:00Z',
                    machineId: 'MACHINE-001',
                    isRevoked: false,
                    isTrial: false,
                    createdAt: '2024-01-15T10:30:00Z'
                },
                {
                    licenseKey: 'LIC-87654321-WXYZ-1234',
                    softwareId: 2,
                    userId: 3,
                    activationType: 'offline',
                    activatedAt: '2024-01-16T14:20:00Z',
                    expiresAt: '2024-02-16T14:20:00Z',
                    machineId: 'MACHINE-002',
                    isRevoked: false,
                    isTrial: true,
                    createdAt: '2024-01-16T14:20:00Z'
                },
                {
                    licenseKey: 'LIC-11223344-ABCD-EFGH',
                    softwareId: 1,
                    userId: 4,
                    activationType: 'offline',
                    activatedAt: '2024-01-17T10:00:00Z',
                    expiresAt: '2025-02-17T10:00:00Z', // 设置为2025年，确保未过期
                    machineId: '',
                    isRevoked: false,
                    isTrial: true,
                    createdAt: '2024-01-17T10:00:00Z'
                }
            ];
            for (const license of defaultLicenses) {
                await DataService.db.createLicense(license);
            }
            console.log('[DataService] 默认数据插入完成');
        }
    }
    // 用户数据操作
    static async getUsers() {
        return await DataService.db.getUsers();
    }
    static async getUserById(id) {
        return await DataService.db.getUserById(id);
    }
    static async getUserByUsername(username) {
        return await DataService.db.getUserByUsername(username);
    }
    static async addUser(user) {
        return await DataService.db.createUser(user);
    }
    static async updateUser(userId, updates) {
        return await DataService.db.updateUser(userId, updates);
    }
    static async deleteUser(userId) {
        return await DataService.db.deleteUser(userId);
    }
    // 软件数据操作
    static async getSoftware() {
        return await DataService.db.getSoftware();
    }
    static async addSoftware(software) {
        return await DataService.db.createSoftware(software);
    }
    static async updateSoftware(softwareId, updates) {
        return await DataService.db.updateSoftware(softwareId, updates);
    }
    static async deleteSoftware(softwareId) {
        return await DataService.db.deleteSoftware(softwareId);
    }
    // 许可证数据操作
    static async getLicenses() {
        return await DataService.db.getLicenses();
    }
    static async addLicense(license) {
        return await DataService.db.createLicense(license);
    }
    static async updateLicense(licenseId, updates) {
        return await DataService.db.updateLicense(licenseId, updates);
    }
    static async deleteLicense(licenseId) {
        return await DataService.db.deleteLicense(licenseId);
    }
    // 获取下一个可用ID（SQLite自动处理，但保留兼容性）
    static async getNextUserId() {
        const users = await DataService.getUsers();
        return users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1;
    }
    static async getNextSoftwareId() {
        const software = await DataService.getSoftware();
        return software.length > 0 ? Math.max(...software.map(s => s.id)) + 1 : 1;
    }
    static async getNextLicenseId() {
        const licenses = await DataService.getLicenses();
        return licenses.length > 0 ? Math.max(...licenses.map(l => l.id)) + 1 : 1;
    }
    // 日志操作
    static async createLog(log) {
        return await DataService.db.createLog(log);
    }
    static async getLogs(limit = 100) {
        return await DataService.db.getLogs(limit);
    }
    // 关闭数据库连接
    static async close() {
        if (DataService.db) {
            await DataService.db.close();
        }
    }
}
exports.DataService = DataService;
//# sourceMappingURL=DataService.js.map