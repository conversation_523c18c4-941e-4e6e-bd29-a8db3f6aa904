{"version": 3, "names": ["toCom<PERSON><PERSON>ey", "node", "key", "property", "computed", "isIdentifier", "stringLiteral", "name"], "sources": ["../../src/converters/toComputedKey.ts"], "sourcesContent": ["import { isIdentifier } from \"../validators/generated\";\nimport { stringLiteral } from \"../builders/generated\";\nimport type * as t from \"..\";\n\nexport default function toComputedKey(\n  node:\n    | t.ObjectMember\n    | t.ObjectProperty\n    | t.ClassMethod\n    | t.ClassProperty\n    | t.ClassAccessorProperty\n    | t.MemberExpression\n    | t.OptionalMemberExpression,\n  // @ts-expect-error todo(flow->ts): maybe check the type of node before accessing .key and .property\n  key: t.Expression | t.PrivateName = node.key || node.property,\n) {\n  if (!node.computed && isIdentifier(key)) key = stringLiteral(key.name);\n\n  return key;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAGe,SAASA,aAAT,CACbC,IADa,EAUbC,GAAiC,GAAGD,IAAI,CAACC,GAAL,IAAYD,IAAI,CAACE,QAVxC,EAWb;EACA,IAAI,CAACF,IAAI,CAACG,QAAN,IAAkB,IAAAC,uBAAA,EAAaH,GAAb,CAAtB,EAAyCA,GAAG,GAAG,IAAAI,yBAAA,EAAcJ,GAAG,CAACK,IAAlB,CAAN;EAEzC,OAAOL,GAAP;AACD"}