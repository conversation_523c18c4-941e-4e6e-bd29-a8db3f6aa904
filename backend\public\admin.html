<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件权限管理系统 - 后台管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        /* 登录页面样式 */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 400px;
        }

        .login-box h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .btn-login {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-login:hover {
            transform: translateY(-2px);
        }

        .error-message {
            color: #e53e3e;
            text-align: center;
            margin-top: 10px;
        }

        /* 主界面样式 */
        .admin-container {
            display: none;
            flex-direction: column;
            height: 100vh;
        }

        .header {
            background: #2d3748;
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .main-content {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 250px;
            background: #1a202c;
            color: white;
            padding: 20px 0;
        }

        .nav-item {
            padding: 15px 30px;
            cursor: pointer;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background-color: #2d3748;
        }

        .nav-item.active {
            background-color: #4299e1;
            border-left-color: #63b3ed;
        }

        .nav-item i {
            margin-right: 10px;
            width: 20px;
        }

        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: #2d3748;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-warning {
            background: #ed8936;
            color: white;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 数据表格样式 */
        .data-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #f7fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-box {
            padding: 8px 12px;
            border: 1px solid #cbd5e0;
            border-radius: 4px;
            width: 250px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }

        tr:hover {
            background: #f7fafc;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-inactive {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-expired {
            background: #feebc8;
            color: #744210;
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.users { background: #4299e1; }
        .stat-icon.software { background: #48bb78; }
        .stat-icon.licenses { background: #ed8936; }
        .stat-icon.active { background: #38b2ac; }

        .stat-info h3 {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-info p {
            color: #718096;
            font-size: 14px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 50px auto;
            padding: 30px;
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #a0aec0;
        }

        .close:hover {
            color: #2d3748;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        /* 隐藏页面样式 */
        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* 响应式样式 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .content-area {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container">
        <div class="login-box">
            <h2><i class="fas fa-shield-alt"></i> 后台管理登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn-login" id="loginSubmitBtn" onclick="handleLoginClick(event)">登录</button>
                
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- 主管理界面 -->
    <div id="adminPage" class="admin-container">
        <!-- 顶部导航 -->
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 软件权限管理系统</h1>
            <div class="user-info">
                <span id="currentUser">欢迎，管理员</span>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </button>
                <button id="debugToggle" onclick="toggleDebugPanel()" style="margin-left: 10px; padding: 5px 10px; background: #28a745; border: none; color: white; border-radius: 3px; cursor: pointer;">
                    <i class="fas fa-bug"></i> 调试
                </button>
            </div>
        </div>

        <!-- 调试面板 -->
        <div id="debugPanel" style="display: none; background: #f8f9fa; border-bottom: 1px solid #dee2e6; padding: 10px; font-family: monospace; font-size: 12px; max-height: 150px; overflow-y: auto;">
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 5px;">
                <strong>调试日志</strong>
                <button onclick="clearDebugLog()" style="margin-left: auto; padding: 2px 8px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">清除</button>
            </div>
            <div id="debugLog"></div>
        </div>

        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="nav-item active" onclick="showPage('dashboard', this)">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </div>
                <div class="nav-item" onclick="showPage('users', this)">
                    <i class="fas fa-users"></i>
                    <span>用户管理</span>
                </div>
                <div class="nav-item" onclick="showPage('software', this)">
                    <i class="fas fa-box"></i>
                    <span>软件管理</span>
                </div>
                <div class="nav-item" onclick="showPage('licenses', this)">
                    <i class="fas fa-key"></i>
                    <span>许可证管理</span>
                </div>
                <div class="nav-item" onclick="showPage('logs', this)">
                    <i class="fas fa-list"></i>
                    <span>操作日志</span>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 仪表板页面 -->
                <div id="dashboardPage" class="page active">
                    <div class="page-header">
                        <h2 class="page-title">仪表板</h2>
                        <div>
                            
                        </div>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalUsers">0</h3>
                                <p>总用户数</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon software">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalSoftware">0</h3>
                                <p>软件产品</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon licenses">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalLicenses">0</h3>
                                <p>许可证总数</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon active">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeLicenses">0</h3>
                                <p>有效许可证</p>
                            </div>
                        </div>
                    </div>

                    <div class="data-table">
                        <div class="table-header">
                            <h3>最近激活的许可证</h3>
                        </div>
                        <table id="recentLicensesTable">
                            <thead>
                                <tr>
                                    <th>许可证密钥</th>
                                    <th>软件</th>
                                    <th>用户</th>
                                    <th>激活时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="recentLicensesBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div id="usersPage" class="page">
                    <div class="page-header">
                        <h2 class="page-title">用户管理</h2>
                        <button class="btn btn-primary" onclick="showAddUserModal()">
                            <i class="fas fa-plus"></i> 添加用户
                        </button>
                    </div>
                    
                    <div class="data-table">
                        <div class="table-header">
                            <h3>用户列表</h3>
                            <input type="text" class="search-box" placeholder="搜索用户..." id="userSearch">
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>角色</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 软件管理页面 -->
                <div id="softwarePage" class="page">
                    <div class="page-header">
                        <h2 class="page-title">软件管理</h2>
                        <div>
                            <button class="btn btn-success" onclick="toggleAllSoftwareStatus(true)" style="margin-right: 10px;">
                                <i class="fas fa-toggle-on"></i> 批量启用
                            </button>
                            <button class="btn btn-secondary" onclick="toggleAllSoftwareStatus(false)" style="margin-right: 10px;">
                                <i class="fas fa-toggle-off"></i> 批量禁用
                            </button>
                            <button class="btn btn-primary" onclick="showAddSoftwareModal()">
                                <i class="fas fa-plus"></i> 添加软件
                            </button>
                        </div>
                    </div>
                    
                    <div class="data-table">
                        <div class="table-header">
                            <h3>软件列表</h3>
                            <input type="text" class="search-box" placeholder="搜索软件..." id="softwareSearch">
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>软件名称</th>
                                    <th>产品密钥</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="softwareTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 许可证管理页面 -->
                <div id="licensesPage" class="page">
                    <div class="page-header">
                        <h2 class="page-title">许可证管理</h2>
                        <button class="btn btn-primary" onclick="showAddLicenseModal()">
                            <i class="fas fa-plus"></i> 生成许可证
                        </button>
                    </div>
                    
                    <div class="data-table">
                        <div class="table-header">
                            <h3>许可证列表</h3>
                            <input type="text" class="search-box" placeholder="搜索许可证..." id="licenseSearch">
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>许可证密钥</th>
                                    <th>软件</th>
                                    <th>用户</th>
                                    <th>类型</th>
                                    <th>激活时间</th>
                                    <th>过期时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="licensesTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 操作日志页面 -->
                <div id="logsPage" class="page">
                    <div class="page-header">
                        <h2 class="page-title">操作日志</h2>
                    </div>
                    
                    <div class="data-table">
                        <div class="table-header">
                            <h3>系统日志</h3>
                            <input type="text" class="search-box" placeholder="搜索日志..." id="logSearch">
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>对象</th>
                                    <th>结果</th>
                                </tr>
                            </thead>
                            <tbody id="logsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">添加用户</h3>
                <span class="close" onclick="closeModal('userModal')">&times;</span>
            </div>
            <form id="userForm">
                <input type="hidden" id="userId">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="userUsername" required>
                </div>
                <div class="form-group">
                    <label>邮箱</label>
                    <input type="email" id="userEmail" required>
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="userPassword">
                    <small id="passwordHint" style="color: #666; display: none;">编辑时留空表示不修改密码</small>
                </div>
                <div class="form-group">
                    <label>角色</label>
                    <select id="userRole">
                        <option value="user">普通用户</option>
                        <option value="manager">管理员</option>
                        <option value="admin">超级管理员</option>
                    </select>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeModal('userModal')">取消</button>
                    <button type="submit" class="btn btn-primary" id="userSubmitBtn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加软件模态框 -->
    <div id="softwareModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="softwareModalTitle">添加软件</h3>
                <span class="close" onclick="closeModal('softwareModal')">&times;</span>
            </div>
            <form id="softwareForm">
                <input type="hidden" id="softwareId">
                <div class="form-group">
                    <label>软件名称</label>
                    <input type="text" id="softwareName" required>
                </div>
                <div class="form-group">
                    <label>产品密钥</label>
                    <input type="text" id="softwareProductKey" required>
                    <button type="button" class="btn btn-success" onclick="generateProductKey()" style="margin-top: 5px;" id="generateKeyBtn">
                        <i class="fas fa-magic"></i> 自动生成
                    </button>
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <textarea id="softwareDescription" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>试用天数</label>
                    <input type="number" id="softwareTrialDays" min="1" max="365" value="7">
                    <small style="color: #666;">用户可以申请试用的天数</small>
                </div>
                <div class="form-group">
                    <label>软件状态</label>
                    <select id="softwareIsActive">
                        <option value="true">启用</option>
                        <option value="false">禁用</option>
                    </select>
                    <small style="color: #666;">禁用后用户无法申请试用或激活</small>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeModal('softwareModal')">取消</button>
                    <button type="submit" class="btn btn-primary" id="softwareSubmitBtn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加许可证模态框 -->
    <div id="licenseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>生成许可证</h3>
                <span class="close" onclick="closeModal('licenseModal')">&times;</span>
            </div>
            <form id="licenseForm">
                <div class="form-group">
                    <label>软件产品</label>
                    <select id="licenseSoftware" required>
                        <option value="">请选择软件</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>用户</label>
                    <select id="licenseUser" required>
                        <option value="">请选择用户</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>激活类型</label>
                    <select id="licenseType" required>
                        <option value="">请选择类型</option>
                        <option value="online">在线激活</option>
                        <option value="offline">离线激活</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>过期时间 (可选)</label>
                    <input type="datetime-local" id="licenseExpires">
                    <small style="color: #666;">留空表示永久有效</small>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeModal('licenseModal')">取消</button>
                    <button type="submit" class="btn btn-primary">生成许可证</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let currentToken = '';
        let debugEnabled = false;

        // 调试功能
        function debugLog(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            const colors = {
                info: '#007bff',
                success: '#28a745', 
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            if (logDiv) {
                logDiv.innerHTML += `<div style="color: ${colors[type]}; margin: 2px 0;">[${time}] ${message}</div>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            }
            console.log(`[${time}] ${message}`);
        }

        function toggleDebugPanel() {
            const panel = document.getElementById('debugPanel');
            if (panel) {
                const isVisible = panel.style.display !== 'none';
                panel.style.display = isVisible ? 'none' : 'block';
                debugEnabled = !isVisible;
                debugLog('调试面板已' + (debugEnabled ? '开启' : '关闭'), 'info');
            }
        }

        function clearDebugLog() {
            const logDiv = document.getElementById('debugLog');
            if (logDiv) {
                logDiv.innerHTML = '';
            }
        }

        // 增强的错误处理
        function handleError(operation, error) {
            const message = `${operation}失败: ${error.message || error}`;
            debugLog(message, 'error');
            alert(message);
        }

        // 成功提示
        function showSuccess(message) {
            debugLog(message, 'success');
            alert(message);
        }

        // 统一的时间格式化函数
        function formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        }

        // 完整时间格式化（包含秒）
        function formatDateTimeWithSeconds(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        }
        

        
        // 处理登录点击（备用方案）
        function handleLoginClick(event) {
            console.log('登录按钮被点击（onclick）');
            event.preventDefault();
            performLogin();
        }
        
        // 执行登录的核心函数
        async function performLogin() {
            console.log('开始执行登录...');
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');
            
            console.log('用户名:', username, '密码长度:', password.length);
            
            if (!username || !password) {
                errorDiv.textContent = '请输入用户名和密码';
                return;
            }
            
            // 清除之前的错误信息
            errorDiv.textContent = '';
            errorDiv.style.color = '#e53e3e';
            
            try {
                console.log('发送登录请求...');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('响应状态:', response.status);
                const data = await response.json();
                console.log('响应数据:', data);
                
                if (data.success && data.token) {
                    console.log('登录成功！');
                    currentToken = data.token;
                    localStorage.setItem('adminToken', data.token);
                    localStorage.setItem('currentUser', data.user.username);
                    
                    document.getElementById('currentUser').textContent = `欢迎，${data.user.username}`;
                    
                    errorDiv.style.color = '#48bb78';
                    errorDiv.textContent = '登录成功，正在跳转...';
                    
                    setTimeout(() => {
                        showAdminPage();
                    }, 500);
                } else {
                    console.error('登录失败:', data.message);
                    errorDiv.textContent = data.message || '登录失败';
                }
            } catch (error) {
                console.error('登录请求错误:', error);
                errorDiv.textContent = '网络错误，请重试: ' + error.message;
            }
        }



        // 强制重新绑定所有事件
        function rebindAllEvents() {
            debugLog('重新绑定所有事件...', 'info');
            
            // 重新绑定表单事件
            const forms = [
                { id: 'userForm', handler: handleUserFormSubmit },
                { id: 'softwareForm', handler: handleSoftwareFormSubmit },
                { id: 'licenseForm', handler: handleLicenseFormSubmit }
            ];
            
            forms.forEach(form => {
                const element = document.getElementById(form.id);
                if (element) {
                    element.removeEventListener('submit', form.handler);
                    element.addEventListener('submit', form.handler);
                    debugLog(`✅ ${form.id} 事件已重新绑定`, 'success');
                } else {
                    debugLog(`❌ ${form.id} 元素未找到`, 'warning');
                }
            });
            
            // 重新设置搜索功能
            setupSearchFunctions();
            debugLog('事件重新绑定完成', 'success');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('页面DOM加载完成，开始初始化...', 'info');
            
            // 检查是否已登录
            const token = localStorage.getItem('adminToken');
            if (token) {
                currentToken = token;
                debugLog('发现已保存的登录令牌', 'info');
                showAdminPage();
            } else {
                debugLog('未发现登录令牌，显示登录页面', 'info');
                showLoginPage();
            }

            // 绑定登录表单事件
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
                debugLog('✅ 登录表单事件已绑定', 'success');
            } else {
                debugLog('❌ 登录表单元素未找到', 'error');
            }

            // 延迟绑定管理表单事件
            setTimeout(() => {
                debugLog('开始绑定管理界面事件...', 'info');
                rebindAllEvents();
            }, 1000);

            // 添加全局错误处理
            window.addEventListener('error', function(e) {
                debugLog(`全局错误: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
            });
            
            debugLog('页面初始化完成', 'success');
        });

        // 显示登录页面
        function showLoginPage() {
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('adminPage').style.display = 'none';
        }

        // 显示管理页面
        function showAdminPage() {
            console.log('切换到管理页面...');
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('adminPage').style.display = 'flex';
            console.log('开始加载仪表板数据...');
            loadDashboardData();
        }

        // 处理登录
        async function handleLogin(e) {
            console.log('登录表单提交事件触发');
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');

            console.log('准备登录，用户名:', username);

            // 清除之前的错误信息
            errorDiv.textContent = '';
            errorDiv.style.color = '#e53e3e';

            // 显示加载状态
            const submitButton = e.target.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = '登录中...';
            submitButton.disabled = true;

            try {
                console.log('发送登录请求...');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                console.log('登录响应状态:', response.status);
                const data = await response.json();
                console.log('登录响应数据:', data);

                if (data.success && data.token) {
                    console.log('登录成功，保存token...');
                    currentToken = data.token;
                    localStorage.setItem('adminToken', data.token);
                    localStorage.setItem('currentUser', data.user.username);
                    
                    document.getElementById('currentUser').textContent = `欢迎，${data.user.username}`;
                    
                    // 显示成功消息
                    errorDiv.style.color = '#48bb78';
                    errorDiv.textContent = '登录成功，正在跳转...';
                    
                    // 延迟跳转以显示成功消息
                    setTimeout(() => {
                        showAdminPage();
                    }, 500);
                } else {
                    console.error('登录失败:', data.message);
                    errorDiv.textContent = data.message || '登录失败';
                }
            } catch (error) {
                console.error('登录请求错误:', error);
                errorDiv.textContent = '网络错误，请重试';
            } finally {
                // 恢复按钮状态
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('currentUser');
            currentToken = '';
            showLoginPage();
        }

        // 页面切换
        function showPage(pageName, clickedElement) {
            console.log('切换到页面:', pageName);
            
            try {
                // 移除所有active类
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });

                // 添加active类到点击的导航项
                if (clickedElement) {
                    clickedElement.classList.add('active');
                } else {
                    // fallback: 查找对应的导航项
                    const navItems = document.querySelectorAll('.nav-item');
                    navItems.forEach(item => {
                        if (item.textContent.includes(getPageDisplayName(pageName))) {
                            item.classList.add('active');
                        }
                    });
                }

                // 显示对应页面
                const targetPage = document.getElementById(pageName + 'Page');
                if (targetPage) {
                    targetPage.classList.add('active');
                    console.log('页面切换成功:', pageName);
                } else {
                    console.error('页面元素未找到:', pageName + 'Page');
                }

                // 加载对应页面数据
                switch(pageName) {
                    case 'dashboard':
                        loadDashboardData();
                        break;
                    case 'users':
                        loadUsersData();
                        break;
                    case 'software':
                        loadSoftwareData();
                        break;
                    case 'licenses':
                        loadLicensesData();
                        break;
                    case 'logs':
                        loadLogsData();
                        break;
                    default:
                        console.warn('未知页面:', pageName);
                }
            } catch (error) {
                console.error('页面切换错误:', error);
            }
        }

        // 获取页面显示名称
        function getPageDisplayName(pageName) {
            const names = {
                'dashboard': '仪表板',
                'users': '用户管理',
                'software': '软件管理', 
                'licenses': '许可证管理',
                'logs': '操作日志'
            };
            return names[pageName] || pageName;
        }

        // 加载仪表板数据
        async function loadDashboardData() {
            debugLog('开始加载仪表板数据...');
            try {
                if (!currentToken) {
                    throw new Error('未找到认证令牌');
                }

                const response = await fetch('/api/admin/stats', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                debugLog(`仪表板API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    const data = result.data;
                    
                    debugLog(`仪表板数据: 用户${data.totalUsers}, 软件${data.totalSoftware}, 许可证${data.totalLicenses}`, 'success');
                    
                    document.getElementById('totalUsers').textContent = data.totalUsers;
                    document.getElementById('totalSoftware').textContent = data.totalSoftware;
                    document.getElementById('totalLicenses').textContent = data.totalLicenses;
                    document.getElementById('activeLicenses').textContent = data.activeLicenses;

                    // 加载最近许可证
                    const tbody = document.getElementById('recentLicensesBody');
                    tbody.innerHTML = '';
                    
                    data.recentLicenses.forEach(license => {
                        const row = `
                            <tr>
                                <td><code>${license.licenseKey}</code></td>
                                <td>${license.softwareName}</td>
                                <td>${license.username}</td>
                                <td>${license.activatedAt ? formatDateTime(license.activatedAt) : '未激活'}</td>
                                <td><span class="status-badge ${license.isRevoked ? 'status-inactive' : 'status-active'}">${license.isRevoked ? '已撤销' : '有效'}</span></td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                    
                    debugLog(`加载了${data.recentLicenses.length}条最近许可证记录`, 'success');
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP ${response.status}`);
                }
            } catch (error) {
                handleError('加载仪表板数据', error);
            }
        }

        // 加载用户数据
        async function loadUsersData() {
            try {
                const response = await fetch('/api/admin/users', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const tbody = document.getElementById('usersTableBody');
                    tbody.innerHTML = '';
                    
                    result.data.forEach(user => {
                        const row = `
                            <tr>
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${user.email}</td>
                                <td>${user.roles.join(', ')}</td>
                                <td>${formatDateTime(user.createdAt)}</td>
                                <td>
                                    <button class="btn btn-warning" onclick="editUser(${user.id})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="deleteUser(${user.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                }
            } catch (error) {
                console.error('加载用户数据失败:', error);
            }
        }

        // 加载软件数据
        async function loadSoftwareData() {
            try {
                const response = await fetch('/api/admin/software', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const tbody = document.getElementById('softwareTableBody');
                    tbody.innerHTML = '';
                    
                    result.data.forEach(software => {
                        const row = `
                            <tr>
                                <td>${software.id}</td>
                                <td>${software.name}</td>
                                <td><code>${software.productKey}</code></td>
                                <td>${software.description || '无'}</td>
                                <td><span class="status-badge ${software.isActive ? 'status-active' : 'status-inactive'}">${software.isActive ? '启用' : '禁用'}</span></td>
                                <td>
                                    <button class="btn ${software.isActive ? 'btn-secondary' : 'btn-success'}" 
                                            onclick="toggleSoftwareStatus(${software.id}, ${!software.isActive})"
                                            title="${software.isActive ? '禁用软件' : '启用软件'}">
                                        <i class="fas fa-${software.isActive ? 'toggle-off' : 'toggle-on'}"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editSoftware(${software.id})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="deleteSoftware(${software.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    console.error('加载软件数据失败');
                }
            } catch (error) {
                console.error('加载软件数据失败:', error);
                alert('加载软件数据失败: ' + error.message);
            }
        }

        // 加载许可证数据
        async function loadLicensesData() {
            try {
                const response = await fetch('/api/admin/licenses', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const tbody = document.getElementById('licensesTableBody');
                    tbody.innerHTML = '';
                    
                    result.data.forEach(license => {
                        const isExpired = license.expiresAt && new Date(license.expiresAt) < new Date();
                        
                        let statusClass = 'status-active';
                        let statusText = '有效';
                        
                        if (license.isRevoked) {
                            statusClass = 'status-inactive';
                            statusText = '已撤销';
                        } else if (isExpired) {
                            statusClass = 'status-expired';
                            statusText = '已过期';
                        }
                        
                        const row = `
                            <tr>
                                <td><code>${license.licenseKey}</code></td>
                                <td>${license.softwareName}</td>
                                <td>${license.username}</td>
                                <td>${license.activationType}</td>
                                <td>${license.activatedAt ? formatDateTime(license.activatedAt) : '未激活'}</td>
                                <td>${license.expiresAt ? formatDateTime(license.expiresAt) : '永久'}</td>
                                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                                <td>
                                    <button class="btn btn-danger" onclick="revokeLicense('${license.licenseKey}')" ${license.isRevoked ? 'disabled' : ''}>
                                        <i class="fas fa-ban"></i> 撤销
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    console.error('加载许可证数据失败');
                }
            } catch (error) {
                console.error('加载许可证数据失败:', error);
                alert('加载许可证数据失败: ' + error.message);
            }
        }

        // 加载日志数据
        async function loadLogsData() {
            try {
                const response = await fetch('/api/admin/logs', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const tbody = document.getElementById('logsTableBody');
                    tbody.innerHTML = '';
                    
                    result.data.forEach(log => {
                        const time = formatDateTimeWithSeconds(log.timestamp);
                        const row = `
                            <tr>
                                <td>${time}</td>
                                <td>${log.username}</td>
                                <td>${log.action}</td>
                                <td>${log.object}</td>
                                <td><span class="status-badge ${log.result === '成功' ? 'status-active' : 'status-inactive'}">${log.result}</span></td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    console.error('加载日志数据失败');
                }
            } catch (error) {
                console.error('加载日志数据失败:', error);
                alert('加载日志数据失败: ' + error.message);
            }
        }

        // 模态框操作
        function showAddUserModal() {
            document.getElementById('userModalTitle').textContent = '添加用户';
            document.getElementById('userSubmitBtn').textContent = '添加用户';
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('userPassword').required = true;
            document.getElementById('passwordHint').style.display = 'none';
            document.getElementById('userModal').style.display = 'block';
        }

        function showEditUserModal(userId, userData) {
            document.getElementById('userModalTitle').textContent = '编辑用户';
            document.getElementById('userSubmitBtn').textContent = '更新用户';
            document.getElementById('userId').value = userId;
            document.getElementById('userUsername').value = userData.username;
            document.getElementById('userEmail').value = userData.email;
            document.getElementById('userPassword').value = '';
            document.getElementById('userPassword').required = false;
            document.getElementById('userRole').value = userData.roles[0] || 'user';
            document.getElementById('passwordHint').style.display = 'block';
            document.getElementById('userModal').style.display = 'block';
        }

        function showAddSoftwareModal() {
            document.getElementById('softwareModalTitle').textContent = '添加软件';
            document.getElementById('softwareSubmitBtn').textContent = '添加软件';
            document.getElementById('softwareForm').reset();
            document.getElementById('softwareId').value = '';
            document.getElementById('softwareIsActive').value = 'true'; // 默认启用
            document.getElementById('generateKeyBtn').style.display = 'inline-block';
            document.getElementById('softwareModal').style.display = 'block';
        }

        function showEditSoftwareModal(softwareId, softwareData) {
            document.getElementById('softwareModalTitle').textContent = '编辑软件';
            document.getElementById('softwareSubmitBtn').textContent = '更新软件';
            document.getElementById('softwareId').value = softwareId;
            document.getElementById('softwareName').value = softwareData.name;
            document.getElementById('softwareProductKey').value = softwareData.productKey;
            document.getElementById('softwareDescription').value = softwareData.description || '';
            document.getElementById('softwareTrialDays').value = softwareData.trialDays || 7;
            document.getElementById('softwareIsActive').value = softwareData.isActive ? 'true' : 'false';
            document.getElementById('generateKeyBtn').style.display = 'none'; // 编辑时隐藏生成按钮
            document.getElementById('softwareModal').style.display = 'block';
        }

        function showAddLicenseModal() {
            loadUsersAndSoftwareForLicense();
            document.getElementById('licenseModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 用户表单提交
        async function handleUserFormSubmit(e) {
            e.preventDefault();
            
            const userId = document.getElementById('userId').value;
            const username = document.getElementById('userUsername').value;
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;
            const roles = [document.getElementById('userRole').value];
            
            const isEdit = !!userId;
            const url = isEdit ? `/api/admin/users/${userId}` : '/api/admin/users';
            const method = isEdit ? 'PUT' : 'POST';
            
            const requestBody = { username, email, roles };
            
            // 只有在添加用户或密码不为空时才包含密码
            if (!isEdit || password.trim()) {
                requestBody.password = password;
            }

            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                
                if (result.success) {
                    alert(isEdit ? '用户更新成功！' : '用户创建成功！');
                    closeModal('userModal');
                    loadUsersData();
                    loadDashboardData();
                } else {
                    alert((isEdit ? '更新' : '创建') + '失败：' + result.message);
                }
            } catch (error) {
                alert((isEdit ? '更新' : '创建') + '失败：' + error.message);
            }
        }

        // 软件表单提交
        async function handleSoftwareFormSubmit(e) {
            e.preventDefault();
            
            const softwareId = document.getElementById('softwareId').value;
            const name = document.getElementById('softwareName').value;
            const productKey = document.getElementById('softwareProductKey').value;
            const description = document.getElementById('softwareDescription').value;
            const trialDays = parseInt(document.getElementById('softwareTrialDays').value) || 7;
            const isActive = document.getElementById('softwareIsActive').value === 'true';
            
            const isEdit = !!softwareId;
            const url = isEdit ? `/api/admin/software/${softwareId}` : '/api/admin/software';
            const method = isEdit ? 'PUT' : 'POST';

            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({ name, productKey, description, trialDays, isActive })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert(isEdit ? '软件更新成功！' : '软件创建成功！');
                    closeModal('softwareModal');
                    loadSoftwareData();
                    loadDashboardData();
                } else {
                    alert((isEdit ? '更新' : '创建') + '失败：' + result.message);
                }
            } catch (error) {
                alert((isEdit ? '更新' : '创建') + '失败：' + error.message);
            }
        }

        // 许可证表单提交
        async function handleLicenseFormSubmit(e) {
            e.preventDefault();
            
            const softwareId = document.getElementById('licenseSoftware').value;
            const userId = document.getElementById('licenseUser').value;
            const activationType = document.getElementById('licenseType').value;
            const expiresAt = document.getElementById('licenseExpires').value;

            try {
                const response = await fetch('/api/admin/licenses/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({ softwareId, userId, activationType, expiresAt })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('许可证生成成功！\n许可证密钥：' + result.data.licenseKey);
                    closeModal('licenseModal');
                    loadLicensesData();
                    loadDashboardData();
                } else {
                    alert('生成失败：' + result.message);
                }
            } catch (error) {
                alert('生成失败：' + error.message);
            }
        }

        // 生成产品密钥
        function generateProductKey() {
            const chars = '0123456789ABCDEF';
            let result = '';
            for (let i = 0; i < 4; i++) {
                if (i > 0) result += '-';
                for (let j = 0; j < 4; j++) {
                    result += chars[Math.floor(Math.random() * chars.length)];
                }
            }
            document.getElementById('softwareProductKey').value = result;
        }

        // 撤销许可证
        async function revokeLicense(licenseKey) {
            if (confirm('确定要撤销此许可证吗？')) {
                try {
                    const response = await fetch(`/api/admin/licenses/${licenseKey}/revoke`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${currentToken}`
                        }
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        alert(result.message);
                        loadLicensesData();
                        loadDashboardData();
                    } else {
                        const error = await response.json();
                        alert('撤销失败：' + error.message);
                    }
                } catch (error) {
                    alert('撤销失败：' + error.message);
                }
            }
        }

        // 编辑用户
        async function editUser(userId) {
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showEditUserModal(userId, result.data);
                } else {
                    const error = await response.json();
                    alert('获取用户信息失败：' + error.message);
                }
            } catch (error) {
                alert('获取用户信息失败：' + error.message);
            }
        }

        // 删除用户
        async function deleteUser(userId) {
            if (confirm('确定要删除此用户吗？此操作不可撤销！')) {
                try {
                    const response = await fetch(`/api/admin/users/${userId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${currentToken}`
                        }
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        alert(result.message);
                        loadUsersData();
                        loadDashboardData();
                    } else {
                        const error = await response.json();
                        alert('删除失败：' + error.message);
                    }
                } catch (error) {
                    alert('删除失败：' + error.message);
                }
            }
        }

        // 快速切换软件启用/禁用状态
        async function toggleSoftwareStatus(softwareId, newStatus) {
            try {
                const response = await fetch(`/api/admin/software/${softwareId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({ isActive: newStatus })
                });

                const result = await response.json();
                
                if (result.success) {
                    // 重新加载软件列表
                    loadSoftwareData();
                    loadDashboardData();
                    showSuccess(`软件已${newStatus ? '启用' : '禁用'}`);
                } else {
                    alert('操作失败：' + result.message);
                }
            } catch (error) {
                alert('操作失败：' + error.message);
            }
        }

        // 批量切换软件状态
        async function toggleAllSoftwareStatus(newStatus) {
            const action = newStatus ? '启用' : '禁用';
            if (!confirm(`确定要${action}所有软件吗？`)) {
                return;
            }

            try {
                // 获取所有软件
                const response = await fetch('/api/admin/software', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const softwareList = result.data;
                    
                    // 批量更新
                    let successCount = 0;
                    let errorCount = 0;

                    for (const software of softwareList) {
                        if (software.isActive !== newStatus) {
                            try {
                                const updateResponse = await fetch(`/api/admin/software/${software.id}`, {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${currentToken}`
                                    },
                                    body: JSON.stringify({ isActive: newStatus })
                                });

                                if (updateResponse.ok) {
                                    successCount++;
                                } else {
                                    errorCount++;
                                }
                            } catch {
                                errorCount++;
                            }
                        }
                    }

                    // 重新加载数据
                    loadSoftwareData();
                    loadDashboardData();

                    if (errorCount === 0) {
                        showSuccess(`批量${action}成功！共处理 ${successCount} 个软件`);
                    } else {
                        alert(`批量${action}完成！成功: ${successCount} 个，失败: ${errorCount} 个`);
                    }
                } else {
                    alert('获取软件列表失败');
                }
            } catch (error) {
                alert('批量操作失败：' + error.message);
            }
        }

        // 编辑软件
        async function editSoftware(softwareId) {
            try {
                const response = await fetch(`/api/admin/software/${softwareId}`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showEditSoftwareModal(softwareId, result.data);
                } else {
                    const error = await response.json();
                    alert('获取软件信息失败：' + error.message);
                }
            } catch (error) {
                alert('获取软件信息失败：' + error.message);
            }
        }

        // 删除软件
        async function deleteSoftware(softwareId) {
            if (confirm('确定要删除此软件吗？此操作不可撤销！')) {
                try {
                    const response = await fetch(`/api/admin/software/${softwareId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${currentToken}`
                        }
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        alert(result.message);
                        loadSoftwareData();
                        loadDashboardData();
                    } else {
                        const error = await response.json();
                        alert('删除失败：' + error.message);
                    }
                } catch (error) {
                    alert('删除失败：' + error.message);
                }
            }
        }

        // 搜索功能
        function setupSearchFunctions() {
            // 用户搜索
            const userSearch = document.getElementById('userSearch');
            if (userSearch) {
                userSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('#usersTableBody tr');
                    
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(searchTerm) ? '' : 'none';
                    });
                });
            }

            // 软件搜索
            const softwareSearch = document.getElementById('softwareSearch');
            if (softwareSearch) {
                softwareSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('#softwareTableBody tr');
                    
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(searchTerm) ? '' : 'none';
                    });
                });
            }

            // 许可证搜索
            const licenseSearch = document.getElementById('licenseSearch');
            if (licenseSearch) {
                licenseSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('#licensesTableBody tr');
                    
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(searchTerm) ? '' : 'none';
                    });
                });
            }

            // 日志搜索
            const logSearch = document.getElementById('logSearch');
            if (logSearch) {
                logSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('#logsTableBody tr');
                    
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(searchTerm) ? '' : 'none';
                    });
                });
            }
        }

        // 加载许可证模态框所需的用户和软件数据
        async function loadUsersAndSoftwareForLicense() {
            try {
                // 加载用户列表
                const usersResponse = await fetch('/api/admin/users', {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                
                // 加载软件列表
                const softwareResponse = await fetch('/api/admin/software', {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });

                if (usersResponse.ok && softwareResponse.ok) {
                    const usersData = await usersResponse.json();
                    const softwareData = await softwareResponse.json();
                    
                    // 填充用户下拉框
                    const userSelect = document.getElementById('licenseUser');
                    userSelect.innerHTML = '<option value="">请选择用户</option>';
                    usersData.data.forEach(user => {
                        userSelect.innerHTML += `<option value="${user.id}">${user.username} (${user.email})</option>`;
                    });
                    
                    // 填充软件下拉框
                    const softwareSelect = document.getElementById('licenseSoftware');
                    softwareSelect.innerHTML = '<option value="">请选择软件</option>';
                    softwareData.data.forEach(software => {
                        if (software.isActive) {
                            softwareSelect.innerHTML += `<option value="${software.id}">${software.name}</option>`;
                        }
                    });
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                alert('加载数据失败: ' + error.message);
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html> 